import Navigation from '@/components/Navigation'
import AdLibraryClient from '@/components/ad-library/AdLibraryClient'
import { createClient } from '@supabase/supabase-js'

// Use service role key for public data access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function getInitialData() {
  try {
    // Fetch initial analyses (first page)
    const { data: analyses, error: analysesError } = await supabase
      .from('ad_analyses')
      .select(`
        id,
        slug,
        title,
        inferred_brand,
        thumbnail_url,
        duration_seconds,
        overall_sentiment,
        created_at,
        analysis_completed_at,
        youtube_video_id,
        is_public,
        showcase
      `)
      .eq('is_public', true)
      .eq('status', 'completed')
      .order('created_at', { ascending: false })
      .range(0, 11) // First 12 items

    // Get total count for pagination
    const { count } = await supabase
      .from('ad_analyses')
      .select('*', { count: 'exact', head: true })
      .eq('is_public', true)
      .eq('status', 'completed')

    // Fetch filter options
    const { data: brandsData } = await supabase
      .from('ad_analyses')
      .select('inferred_brand')
      .eq('is_public', true)
      .eq('status', 'completed')
      .not('inferred_brand', 'is', null)

    const brands = [...new Set(brandsData?.map(item => item.inferred_brand).filter(Boolean) || [])]

    // For now, we'll use empty arrays for celebrities and years as these fields may not exist
    const celebrities: string[] = []
    const years = [...new Set(analyses?.map(item => new Date(item.created_at).getFullYear().toString()) || [])]

    if (analysesError) {
      throw new Error(analysesError.message)
    }

    // Format analyses for client
    const formattedAnalyses = (analyses || []).map((analysis: any) => ({
      ...analysis,
      video_thumbnail_url: analysis.thumbnail_url,
      duration_formatted: formatDuration(analysis.duration_seconds),
      url: `/ad/${analysis.slug}`,
    }))

    const pagination = {
      page: 1,
      limit: 12,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / 12),
      hasNext: (count || 0) > 12,
      hasPrev: false
    }

    return {
      analyses: formattedAnalyses,
      pagination,
      filters: {
        brands: brands.sort(),
        celebrities: celebrities.sort(),
        years: years.sort().reverse()
      }
    }
  } catch (error) {
    console.error('Error fetching initial ad library data:', error)
    return {
      analyses: [],
      pagination: {
        page: 1,
        limit: 12,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      },
      filters: {
        brands: [],
        celebrities: [],
        years: []
      }
    }
  }
}

function formatDuration(seconds: number): string {
  if (!seconds) return '0:00'
  const mins = Math.floor(seconds / 60)
  const secs = Math.round(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

export default async function AdLibraryServer() {
  const initialData = await getInitialData()

  return (
    <div className="min-h-screen bg-white">
      <Navigation />

      <main className="container mx-auto px-4 py-8 max-w-7xl">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Ad Library
          </h1>
          <p className="text-md text-gray-600 max-w-3xl mx-auto mb-8">
            Discover AI-powered insights from video ad analyses curated by our community. 
            Learn from successful advertising strategies, creative approaches, and audience engagement techniques.
          </p>
        </div>

        <AdLibraryClient 
          initialAnalyses={initialData.analyses}
          initialPagination={initialData.pagination}
          initialFilters={initialData.filters}
        />
      </main>
    </div>
  )
}