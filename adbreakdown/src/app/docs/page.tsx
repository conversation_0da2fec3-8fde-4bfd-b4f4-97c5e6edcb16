import Navigation from '@/components/Navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Book, Code, Video, Zap, ArrowRight } from 'lucide-react'

export default function DocumentationPage() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />

      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Documentation
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Learn how to use AdBreakdown&apos;s AI-powered video ad analysis platform to gain insights and improve your advertising strategy.
          </p>
        </div>

        {/* Quick Start */}
        <div className="mb-12">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-blue-600" />
                Quick Start Guide
              </CardTitle>
              <CardDescription>
                Get up and running with AdBreakdown in minutes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                  <div>
                    <h3 className="font-semibold">Sign up for an account</h3>
                    <p className="text-gray-600">Create your free AdBreakdown account to get started</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                  <div>
                    <h3 className="font-semibold">Upload a YouTube video URL</h3>
                    <p className="text-gray-600">Paste any public YouTube ad URL into the analysis tool</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                  <div>
                    <h3 className="font-semibold">Get AI-powered insights</h3>
                    <p className="text-gray-600">Receive comprehensive analysis including sentiment, targeting, and creative recommendations</p>
                  </div>
                </div>
              </div>
              <div className="mt-6">
                <Link href="/dashboard">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    Start Analyzing Now
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Documentation Sections */}
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Book className="h-5 w-5 text-green-600" />
                User Guide
              </CardTitle>
              <CardDescription>
                Step-by-step instructions for using all features
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Creating Your First Analysis
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Understanding Analysis Results
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Generating Marketing Assets
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Managing Your Credits
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Sharing and Collaboration
                  </Link>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Code className="h-5 w-5 text-purple-600" />
                API Reference
              </CardTitle>
              <CardDescription>
                Technical documentation for developers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li>
                  <Link href="/api-docs" className="text-blue-600 hover:text-blue-800 underline">
                    API Overview
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Authentication
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Analysis Endpoints
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Webhooks
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Rate Limits
                  </Link>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Video className="h-5 w-5 text-red-600" />
                Video Tutorials
              </CardTitle>
              <CardDescription>
                Visual guides and walkthroughs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Platform Overview (5 min)
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Advanced Analysis Features (8 min)
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Best Practices for Ad Analysis (12 min)
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Integrating with Your Workflow (6 min)
                  </Link>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>FAQ</CardTitle>
              <CardDescription>
                Common questions and answers
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                <li>
                  <Link href="/faq" className="text-blue-600 hover:text-blue-800 underline">
                    Frequently Asked Questions
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-blue-600 hover:text-blue-800 underline">
                    Contact Support
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Troubleshooting Guide
                  </Link>
                </li>
                <li>
                  <Link href="#" className="text-blue-600 hover:text-blue-800 underline">
                    Community Forum
                  </Link>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Support Section */}
        <div className="mt-12">
          <Card>
            <CardHeader>
              <CardTitle>Need Help?</CardTitle>
              <CardDescription>
                Can&apos;t find what you&apos;re looking for? We&apos;re here to help.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link href="/contact">
                  <Button variant="outline">
                    Contact Support
                  </Button>
                </Link>
                <Link href="mailto:<EMAIL>">
                  <Button variant="outline">
                    Email Us
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}