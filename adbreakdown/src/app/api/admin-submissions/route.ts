import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { submitToAdmin, type AdminSubmissionData } from '@/lib/youtube-validator'

// POST /api/admin-submissions - Submit video for admin analysis
export async function POST(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await req.json()
    const { youtubeUrl, videoTitle, channelTitle, duration, reason, userEmail } = body

    // Validate required fields
    if (!youtubeUrl || !videoTitle || !duration || !reason) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Prepare submission data
    const submissionData: AdminSubmissionData = {
      youtubeUrl,
      userEmail: userEmail || '<EMAIL>',
      videoTitle,
      channelTitle: channelTitle || 'Unknown Channel',
      duration,
      requestedAt: new Date().toISOString(),
      reason
    }

    // Submit to admin queue
    const success = await submitToAdmin(submissionData)

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to submit request' },
        { status: 500 }
      )
    }

    return NextResponse.json(
      { 
        success: true,
        message: 'Request submitted successfully' 
      },
      { status: 201 }
    )

  } catch (error) {
    console.error('Error in POST /api/admin-submissions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/admin-submissions - List admin submissions (admin only)
export async function GET(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Check if user is admin (you may want to implement proper admin checking)
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Simple admin check - you might want to implement a proper admin role system
    const isAdmin = user.email?.endsWith('@adbreakdown.com') || 
                   user.email?.endsWith('@admin.com') ||
                   process.env.ADMIN_EMAILS?.split(',').includes(user.email)

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Admin access required' },
        { status: 403 }
      )
    }

    // Get pagination parameters
    const url = new URL(req.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const status = url.searchParams.get('status') || 'pending'
    const offset = (page - 1) * limit

    // Get submissions with filtering
    let query = supabase
      .from('admin_submissions')
      .select('*')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (status !== 'all') {
      query = query.eq('status', status)
    }

    const { data: submissions, error } = await query

    if (error) {
      console.error('Error fetching admin submissions:', error)
      return NextResponse.json(
        { error: 'Failed to fetch submissions' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      submissions,
      pagination: {
        page,
        limit,
        hasMore: submissions.length === limit
      }
    })

  } catch (error) {
    console.error('Error in GET /api/admin-submissions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}