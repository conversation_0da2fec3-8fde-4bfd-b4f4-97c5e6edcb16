import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(req: NextRequest) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    console.log('🔍 Debug: User ID:', user.id)

    // Get all analyses for this user
    const { data: analyses, error: analysesError } = await supabase
      .from('ad_analyses')
      .select('id, slug, title, status, youtube_url, created_at, updated_at')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    console.log('🔍 Debug: User analyses:', analyses)

    if (analysesError) {
      console.error('❌ Error fetching analyses:', analysesError)
      return NextResponse.json({ error: 'Failed to fetch analyses' }, { status: 500 })
    }

    return NextResponse.json({
      userId: user.id,
      clerkId: userId,
      analyses: analyses || [],
      totalCount: analyses?.length || 0
    })

  } catch (error) {
    console.error('❌ Debug endpoint error:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}