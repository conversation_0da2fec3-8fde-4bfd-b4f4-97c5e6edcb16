import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Use service role key for public data access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(req: NextRequest) {
  try {
    const today = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
    
    console.log('Daily showcase API: Looking for date:', today)
    
    // Simplified query - get all showcases and filter
    const { data: allShowcases, error: showcaseError } = await supabase
      .from('daily_showcases')
      .select(`
        id,
        analysis_id,
        featured_date,
        excerpt,
        key_insights,
        created_at
      `)
      .order('featured_date', { ascending: false })
    
    console.log('Daily showcase API: Found showcases:', allShowcases?.length || 0)
    
    if (showcaseError) {
      console.error('Error fetching daily showcases:', showcaseError)
      return NextResponse.json({ error: 'Failed to fetch showcases' }, { status: 500 })
    }

    let currentShowcase: any = null
    let previousShowcases: any[] = []

    if (allShowcases && allShowcases.length > 0) {
      // Find today's showcase
      const todayShowcase = allShowcases.find(s => s.featured_date === today)
      
      if (todayShowcase) {
        currentShowcase = todayShowcase
        previousShowcases = allShowcases.filter(s => s.featured_date !== today).slice(0, 6)
      } else {
        // Use most recent as current, rest as previous
        currentShowcase = allShowcases[0]
        previousShowcases = allShowcases.slice(1, 7)
      }

      // Now fetch the analysis data for each showcase
      const analysisIds = [
        ...(currentShowcase ? [currentShowcase.analysis_id] : []),
        ...previousShowcases.map(s => s.analysis_id)
      ]

      console.log('Daily showcase API: Analysis IDs to fetch:', analysisIds)

      let analyses: any[] = []
      let analysisError: any = null

      if (analysisIds.length > 0) {
        const result = await supabase
          .from('ad_analyses')
          .select(`
            id,
            slug,
            title,
            inferred_brand,
            thumbnail_url,
            overall_sentiment,
            created_at,
            view_count,
            showcase,
            is_public
          `)
          .in('id', analysisIds)
          .eq('is_public', true)
          .eq('showcase', true)
        
        analyses = result.data || []
        analysisError = result.error
      }

      if (analysisError) {
        console.error('Error fetching analysis data:', analysisError)
        return NextResponse.json({ error: 'Failed to fetch analysis data' }, { status: 500 })
      }

      console.log('Daily showcase API: Found analyses:', analyses?.length || 0)

      // Map showcases to their analysis data
      const mapShowcaseToAnalysis = (showcase: any) => {
        const analysis = analyses?.find(a => a.id === showcase.analysis_id)
        if (!analysis) return null
        
        return {
          id: analysis.id,
          slug: analysis.slug,
          title: analysis.title,
          inferred_brand: analysis.inferred_brand,
          video_thumbnail_url: analysis.thumbnail_url,
          overall_sentiment: analysis.overall_sentiment,
          created_at: analysis.created_at,
          view_count: analysis.view_count || 0,
          showcase: analysis.showcase,
          featured_date: showcase.featured_date,
          excerpt: showcase.excerpt,
          key_insights: showcase.key_insights || []
        }
      }

      currentShowcase = currentShowcase ? mapShowcaseToAnalysis(currentShowcase) : null
      previousShowcases = previousShowcases.map(mapShowcaseToAnalysis).filter(Boolean)
    }

    console.log('Daily showcase API: Returning:', {
      current: currentShowcase ? 'found' : 'null',
      previous: previousShowcases.length
    })

    return NextResponse.json({
      current: currentShowcase,
      previous: previousShowcases
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600', // 30min cache, 1hr stale
      },
    })

  } catch (error) {
    console.error('Error fetching daily showcase:', error)
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : 'No stack trace'
    })
    return NextResponse.json(
      { 
        error: 'Failed to fetch daily showcase',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Admin endpoint to create/update daily showcase
export async function POST(req: NextRequest) {
  try {
    const body = await req.json()
    const { analysis_id, featured_date, excerpt, key_insights } = body

    // Verify the analysis exists and is public
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select('id, is_public')
      .eq('id', analysis_id)
      .single()

    if (!analysis || !analysis.is_public) {
      return NextResponse.json(
        { error: 'Analysis not found or not public' },
        { status: 404 }
      )
    }

    // Insert or update daily showcase
    const { data, error } = await supabase
      .from('daily_showcases')
      .upsert({
        analysis_id,
        featured_date,
        excerpt,
        key_insights,
        created_at: new Date().toISOString()
      }, {
        onConflict: 'featured_date'
      })

    if (error) {
      throw error
    }

    return NextResponse.json({ success: true, data })

  } catch (error) {
    console.error('Error creating daily showcase:', error)
    return NextResponse.json(
      { error: 'Failed to create daily showcase' },
      { status: 500 }
    )
  }
}