import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function GET(req: NextRequest) {
  try {
    console.log('Simple daily showcase API called')

    // Step 1: Get all daily showcases
    const { data: showcases, error: showcaseError } = await supabase
      .from('daily_showcases')
      .select('*')
      .order('featured_date', { ascending: false })

    console.log('Showcases from DB:', showcases?.length || 0, showcaseError?.message || 'no error')

    if (showcaseError) {
      console.error('Showcase query error:', showcaseError)
      return NextResponse.json({ error: `Showcase error: ${showcaseError.message}` }, { status: 500 })
    }

    if (!showcases || showcases.length === 0) {
      console.log('No showcases found in daily_showcases table')
      return NextResponse.json({ current: null, previous: [] }, {
        headers: {
          'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600', // 5min cache for empty state
        },
      })
    }

    // Step 2: Get analysis data for the first showcase
    const firstShowcase = showcases[0]
    console.log('First showcase:', firstShowcase)

    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select('*')
      .eq('id', firstShowcase.analysis_id)
      .single()

    console.log('Analysis for showcase:', analysis, analysisError?.message || 'no error')

    if (analysisError) {
      console.error('Analysis query error:', analysisError)
      return NextResponse.json({ error: `Analysis error: ${analysisError.message}` }, { status: 500 })
    }

    if (!analysis) {
      console.log('No analysis found for showcase')
      return NextResponse.json({ current: null, previous: [] }, {
        headers: {
          'Cache-Control': 'public, s-maxage=300, stale-while-revalidate=600', // 5min cache for empty state
        },
      })
    }

    // Step 3: Format response
    const formattedShowcase = {
      id: firstShowcase.id,
      analysis_id: firstShowcase.analysis_id,
      featured_date: firstShowcase.featured_date,
      excerpt: firstShowcase.excerpt,
      key_insights: firstShowcase.key_insights || [],
      created_at: firstShowcase.created_at,
      updated_at: firstShowcase.updated_at,
      analysis: analysis
    }

    console.log('Returning formatted showcase:', formattedShowcase)

    return NextResponse.json({
      current: formattedShowcase,
      previous: []
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600', // 30min cache, 1hr stale
      },
    })

  } catch (error) {
    console.error('Simple daily showcase error:', error)
    return NextResponse.json(
      { 
        error: 'Simple API failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}