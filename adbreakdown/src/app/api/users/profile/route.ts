import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function GET(req: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // First get the internal user ID from Clerk ID
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      console.error('User not found for Clerk ID:', userId, userError)
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const internalUserId = user.id

    // Get user profile from Supabase
    const { data: profile, error } = await supabase
      .from('profiles')
      .select(`
        subscription_status,
        subscription_plan,
        subscription_period_end,
        credits_remaining,
        created_at,
        updated_at
      `)
      .eq('user_id', internalUserId)
      .single()

    if (error) {
      // If profile doesn't exist, create one with default values
      console.log('Profile not found for user:', userId, 'Creating new profile...')
      
      const { data: newProfile, error: createError } = await supabase
        .from('profiles')
        .insert({
          user_id: internalUserId,
          credits_remaining: 100,
          subscription_status: 'free',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select(`
          subscription_status,
          subscription_plan,
          subscription_period_end,
          credits_remaining,
          created_at,
          updated_at
        `)
        .single()

      if (createError) {
        console.error('Failed to create profile:', createError)
        return NextResponse.json({ error: 'Failed to create user profile' }, { status: 500 })
      }

      return NextResponse.json({
        success: true,
        profile: newProfile
      })
    }

    return NextResponse.json({
      success: true,
      profile
    })

  } catch (error) {
    console.error('Error in profile endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(req: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // First get the internal user ID from Clerk ID
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      console.error('User not found for Clerk ID:', userId, userError)
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const internalUserId = user.id

    const body = await req.json()
    const allowedFields = [
      'subscription_status',
      'subscription_plan',
      'subscription_period_end'
    ]

    // Filter only allowed fields for security
    const updateData = Object.keys(body)
      .filter(key => allowedFields.includes(key))
      .reduce((obj: any, key) => {
        obj[key] = body[key]
        return obj
      }, {})

    updateData.updated_at = new Date().toISOString()

    // Update user profile
    const { data: profile, error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('user_id', internalUserId)
      .select()
      .single()

    if (error) {
      console.error('Error updating user profile:', error)
      return NextResponse.json(
        { error: 'Failed to update profile' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      profile
    })

  } catch (error) {
    console.error('Error in profile update endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}