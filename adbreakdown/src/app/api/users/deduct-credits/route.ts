import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function POST(req: NextRequest) {
  try {
    const supabase = createServerSupabaseClient()
    const { userId } = await auth()

    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { credits } = await req.json()
    
    if (!credits || credits <= 0) {
      return NextResponse.json({ error: 'Invalid credit amount' }, { status: 400 })
    }

    // First get the internal user ID from Clerk ID
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      console.error('User not found for Clerk ID:', userId, userError)
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const internalUserId = user.id

    // Get current user profile to check credits
    const { data: currentProfile, error: fetchError } = await supabase
      .from('profiles')
      .select('credits_remaining')
      .eq('user_id', internalUserId)
      .single()

    if (fetchError) {
      console.error('Error fetching user profile:', fetchError)
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Check if user has enough credits
    if (currentProfile.credits_remaining < credits) {
      return NextResponse.json({ 
        error: 'Insufficient credits',
        credits_remaining: currentProfile.credits_remaining,
        required: credits
      }, { status: 402 }) // Payment Required
    }

    // Deduct credits
    const newCreditsRemaining = currentProfile.credits_remaining - credits
    const { data: updatedProfile, error: updateError } = await supabase
      .from('profiles')
      .update({ 
        credits_remaining: newCreditsRemaining,
        updated_at: new Date().toISOString()
      })
      .eq('user_id', internalUserId)
      .select('credits_remaining')
      .single()

    if (updateError) {
      console.error('Error updating credits:', updateError)
      return NextResponse.json({ error: 'Failed to deduct credits' }, { status: 500 })
    }

    // Log credit usage
    await supabase
      .from('credit_usage')
      .insert({
        user_id: internalUserId,
        credits_used: credits,
        description: 'Video analysis',
        created_at: new Date().toISOString()
      })

    return NextResponse.json({
      success: true,
      credits_remaining: updatedProfile.credits_remaining,
      credits_deducted: credits
    })

  } catch (error) {
    console.error('Error in deduct-credits endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}