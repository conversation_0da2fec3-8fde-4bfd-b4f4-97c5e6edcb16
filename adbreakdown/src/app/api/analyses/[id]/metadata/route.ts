import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// GET /api/analyses/{id}/metadata - Get YouTube metadata and extended info
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    
    // Get analysis with metadata fields
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select(`
        id,
        youtube_url,
        youtube_video_id,
        transcript,
        summary,
        video_info,
        marketing_analysis,
        visual_appeal,
        audio_quality,
        color_palette,
        sentiment_timeline,
        target_demographics,
        target_interests,
        target_behaviors,
        music_mood,
        voice_tone
      `)
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)
      .single()

    if (analysisError || !analysis) {
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      )
    }

    // Also fetch YouTube metadata if we have a video ID
    let youtubeMetadata = null
    if (analysis.youtube_video_id) {
      try {
        const ytApiKey = process.env.YOUTUBE_API_KEY || process.env.NEXT_PUBLIC_YOUTUBE_API_KEY
        if (ytApiKey) {
          const response = await fetch(
            `https://www.googleapis.com/youtube/v3/videos?id=${analysis.youtube_video_id}&part=snippet,statistics,contentDetails&key=${ytApiKey}`
          )
          
          if (response.ok) {
            const data = await response.json()
            if (data.items && data.items.length > 0) {
              const video = data.items[0]
              const snippet = video.snippet
              const statistics = video.statistics
              const contentDetails = video.contentDetails
              
              // Parse duration
              const parseDuration = (duration: string) => {
                const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/)
                if (!match) return '0:00'
                
                const hours = parseInt(match[1] || '0')
                const minutes = parseInt(match[2] || '0')
                const seconds = parseInt(match[3] || '0')
                
                if (hours > 0) {
                  return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
                }
                return `${minutes}:${seconds.toString().padStart(2, '0')}`
              }
              
              youtubeMetadata = {
                title: snippet.title || 'Untitled Video',
                description: snippet.description || '',
                channelTitle: snippet.channelTitle || 'Unknown Channel',
                publishedAt: snippet.publishedAt,
                viewCount: statistics.viewCount || '0',
                likeCount: statistics.likeCount || '0',
                commentCount: statistics.commentCount || '0',
                tags: snippet.tags || [],
                categoryId: snippet.categoryId,
                defaultLanguage: snippet.defaultLanguage,
                duration: parseDuration(contentDetails.duration),
                definition: contentDetails.definition || 'sd'
              }
            }
          }
        }
      } catch (error) {
        console.warn('Failed to fetch YouTube metadata:', error)
      }
    }

    return NextResponse.json({
      ...analysis,
      youtubeMetadata
    })
  } catch (error) {
    console.error('Error in GET /api/analyses/[id]/metadata:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}