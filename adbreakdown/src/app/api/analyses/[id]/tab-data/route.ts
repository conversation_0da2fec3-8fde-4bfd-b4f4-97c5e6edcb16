import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// GET /api/analyses/{id}/tab-data?tab=... - Get specific tab data
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    const url = new URL(req.url)
    const tab = url.searchParams.get('tab')
    
    if (!tab) {
      return NextResponse.json(
        { error: 'Tab parameter is required' },
        { status: 400 }
      )
    }
    
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    
    // Get analysis ID for reports query
    const { data: basicAnalysis, error: basicError } = await supabase
      .from('ad_analyses')
      .select('id, emotions, key_themes, deciphered_script')
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)
      .single()

    if (basicError || !basicAnalysis) {
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      )
    }

    let responseData: any = { id: basicAnalysis.id }

    // Load data based on tab
    switch (tab) {
      case 'sentiment':
        responseData.emotions = basicAnalysis.emotions
        
        // Get emotion timeline reports
        const { data: emotionReports } = await supabase
          .from('analysis_reports')
          .select(`
            id,
            content,
            status,
            report_types (name)
          `)
          .eq('analysis_id', basicAnalysis.id)
          .eq('status', 'generated')
          .in('report_type_id', [
            // Get report type IDs for emotion_timeline
            await supabase
              .from('report_types')
              .select('id')
              .eq('name', 'emotion_timeline')
              .single()
              .then(({ data }) => data?.id)
          ].filter(Boolean))

        responseData.emotionReports = emotionReports || []
        break

      case 'script':
        responseData.deciphered_script = basicAnalysis.deciphered_script
        responseData.key_themes = basicAnalysis.key_themes
        break

      case 'visual':
        // Get visual analysis data
        const { data: visualData } = await supabase
          .from('ad_analyses')
          .select('scenes, color_palette, visual_appeal')
          .eq('id', basicAnalysis.id)
          .single()
        
        responseData = { ...responseData, ...visualData }
        break

      case 'audio':
        // Get audio analysis data
        const { data: audioData } = await supabase
          .from('ad_analyses')
          .select('music_mood, voice_tone, audio_quality')
          .eq('id', basicAnalysis.id)
          .single()
        
        responseData = { ...responseData, ...audioData }
        break

      case 'targeting':
        // Get targeting data
        const { data: targetingData } = await supabase
          .from('ad_analyses')
          .select('target_demographics, target_interests, target_behaviors')
          .eq('id', basicAnalysis.id)
          .single()
        
        responseData = { ...responseData, ...targetingData }
        break

      case 'competitor':
        // Get competitor comparison reports
        const { data: competitorReports } = await supabase
          .from('analysis_reports')
          .select(`
            id,
            content,
            status,
            report_types (name)
          `)
          .eq('analysis_id', basicAnalysis.id)
          .eq('status', 'generated')
          // Add competitor comparison report types when available
        
        responseData.competitorReports = competitorReports || []
        break

      default:
        return NextResponse.json(
          { error: 'Invalid tab parameter' },
          { status: 400 }
        )
    }

    return NextResponse.json(responseData)
  } catch (error) {
    console.error('Error in GET /api/analyses/[id]/tab-data:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}