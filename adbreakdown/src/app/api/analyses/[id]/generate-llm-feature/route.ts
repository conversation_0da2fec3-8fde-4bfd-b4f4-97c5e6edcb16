import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { supabase } from '@/lib/supabase'

export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Await params
    const { id } = await params
    
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get request body
    const body = await req.json()
    const { report_type_name } = body

    if (!report_type_name) {
      return NextResponse.json(
        { error: 'Report type is required' },
        { status: 400 }
      )
    }

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get user profile to check credits
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('credits_remaining, subscription_status')
      .eq('user_id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json(
        { error: 'Profile not found' },
        { status: 404 }
      )
    }

    // Get report type information
    const { data: reportType, error: reportTypeError } = await supabase
      .from('report_types')
      .select('id, credit_cost')
      .eq('name', report_type_name)
      .single()

    if (reportTypeError || !reportType) {
      return NextResponse.json(
        { error: 'Invalid report type' },
        { status: 400 }
      )
    }

    // Credit check disabled for testing
    console.log(`🧪 Testing mode: Bypassing credit check for ${report_type_name}`)
    // if (profile.credits_remaining < reportType.credit_cost) {
    //   return NextResponse.json(
    //     { 
    //       error: 'Insufficient credits',
    //       credits_required: reportType.credit_cost,
    //       credits_remaining: profile.credits_remaining
    //     },
    //     { status: 402 }
    //   )
    // }

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)

    // Verify analysis ownership
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select('id, status')
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)
      .single()

    if (analysisError || !analysis) {
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      )
    }

    // Check if analysis is completed
    if (analysis.status !== 'completed') {
      return NextResponse.json(
        { error: 'Analysis must be completed before generating additional features' },
        { status: 400 }
      )
    }

    // Check if this report already exists
    const { data: existingReport, error: existingReportError } = await supabase
      .from('analysis_reports')
      .select('id, status')
      .eq('analysis_id', id)
      .eq('report_type_id', reportType.id)
      .single()

    if (existingReport && existingReport.status === 'generated') {
      return NextResponse.json(
        { 
          message: 'Feature already generated',
          report_id: existingReport.id
        },
        { status: 200 }
      )
    }

    // Create or update analysis report
    let reportId: string
    if (existingReport) {
      // Update existing report to regenerating status
      const { data: updatedReport, error: updateError } = await supabase
        .from('analysis_reports')
        .update({ status: 'generating' })
        .eq('id', existingReport.id)
        .select('id')
        .single()
      
      if (updateError) {
        console.error('Error updating report:', updateError)
        return NextResponse.json(
          { error: 'Failed to update report' },
          { status: 500 }
        )
      }
      reportId = updatedReport.id
    } else {
      // Create new analysis report
      const { data: newReport, error: createError } = await supabase
        .from('analysis_reports')
        .insert({
          analysis_id: id,
          report_type_id: reportType.id,
          status: 'generating'
        })
        .select('id')
        .single()

      if (createError) {
        console.error('Error creating report:', createError)
        return NextResponse.json(
          { error: 'Failed to create report' },
          { status: 500 }
        )
      }
      reportId = newReport.id
    }

    // Trigger Supabase Function for LLM feature generation
    try {
      console.log(`Attempting to invoke Supabase function 'run-llm-feature-generation' for report type: ${report_type_name}`)
      const functionResponse = await supabase.functions.invoke('run-llm-feature-generation', {
        body: {
          analysis_id: id,
          report_id: reportId,
          report_type_name,
          user_id: user.id,
          credit_cost: reportType.credit_cost
        }
      })
      
      if (functionResponse.error) {
        console.error('Supabase function invocation error:', functionResponse.error)
        // Mark report as error
        await supabase
          .from('analysis_reports')
          .update({ status: 'error' })
          .eq('id', reportId)
      } else {
        console.log('Supabase function invoked successfully. Response:', functionResponse.data)
      }
    } catch (functionError) {
      console.error('Error calling Supabase function:', functionError)
      // Mark report as error
      await supabase
        .from('analysis_reports')
        .update({ status: 'error' })
        .eq('id', reportId)
    }

    return NextResponse.json(
      { 
        message: 'Feature generation started',
        report_id: reportId,
        analysis_id: id,
        estimated_credits: reportType.credit_cost
      },
      { status: 202 }
    )
  } catch (error) {
    console.error('Error in generate-llm-feature:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
