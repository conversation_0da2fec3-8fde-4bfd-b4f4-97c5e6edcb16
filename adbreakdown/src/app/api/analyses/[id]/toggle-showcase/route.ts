import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    // Handle both UUID and slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)

    // Get the analysis and verify ownership + public status
    const { data: analysis, error: fetchError } = await supabase
      .from('ad_analyses')
      .select('id, user_id, is_public, showcase, inferred_brand, title')
      .eq(isUUID ? 'id' : 'slug', id)
      .single()

    if (fetchError || !analysis) {
      return NextResponse.json({ error: 'Analysis not found' }, { status: 404 })
    }

    // Verify user owns this analysis
    if (analysis.user_id !== userId) {
      return NextResponse.json({ error: 'Not authorized to modify this analysis' }, { status: 403 })
    }

    // Verify analysis is public (only public analyses can be showcased)
    if (!analysis.is_public) {
      return NextResponse.json({ 
        error: 'Only public analyses can be featured in daily showcase' 
      }, { status: 400 })
    }

    // Toggle showcase status
    const newShowcaseStatus = !analysis.showcase

    const { data: updatedAnalysis, error: updateError } = await supabase
      .from('ad_analyses')
      .update({ 
        showcase: newShowcaseStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', analysis.id)
      .select('id, showcase, is_public, inferred_brand, title')
      .single()

    if (updateError) {
      console.error('Error updating showcase status:', updateError)
      return NextResponse.json({ 
        error: 'Failed to update showcase status' 
      }, { status: 500 })
    }

    // Log the action for monitoring
    console.log(`Analysis ${analysis.id} showcase status changed to ${newShowcaseStatus} by user ${userId}`)

    return NextResponse.json({
      success: true,
      analysis: updatedAnalysis,
      message: newShowcaseStatus 
        ? `"${analysis.inferred_brand || analysis.title}" has been added to showcase candidates`
        : `"${analysis.inferred_brand || analysis.title}" has been removed from showcase`
    })

  } catch (error) {
    console.error('Error in toggle showcase:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}