import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import CacheService from '@/lib/cache'

// GET /api/analyses/{id}/basic - Get basic analysis info for above-the-fold content (supports public access)
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    
    // Try cache first for public analyses
    const cacheKey = `basic:${id}`
    const cached = await CacheService.getPublicAnalysis(cacheKey)
    if (cached) {
      console.log('💨 Serving cached basic analysis:', id)
      return NextResponse.json(cached)
    }
    
    const supabase = createServerSupabaseClient()
    
    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    
    // First, check if analysis exists and is public
    const { data: publicAnalysis, error: publicError } = await supabase
      .from('ad_analyses')
      .select(`
        id,
        slug,
        status,
        title,
        inferred_brand,
        duration_formatted,
        thumbnail_url,
        overall_sentiment,
        youtube_url,
        created_at,
        updated_at,
        is_public
      `)
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('is_public', true)
      .single()

    // If it's a public analysis, serve it without authentication
    if (publicAnalysis && !publicError) {
      console.log('📖 Serving public basic analysis:', publicAnalysis.id)
      const response = {
        ...publicAnalysis,
        video_url: publicAnalysis.youtube_url,
        video_title: publicAnalysis.title,
        video_thumbnail_url: publicAnalysis.thumbnail_url
      }
      
      // Cache the public analysis for fast future access
      await CacheService.setPublicAnalysis(cacheKey, response)
      
      return NextResponse.json(response)
    }

    // If not public, require authentication for private analysis
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      )
    }

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    // Get private analysis with ownership check
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select(`
        id,
        slug,
        status,
        title,
        inferred_brand,
        duration_formatted,
        thumbnail_url,
        overall_sentiment,
        youtube_url,
        created_at,
        updated_at,
        is_public
      `)
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)
      .single()

    if (analysisError || !analysis) {
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      ...analysis,
      video_url: analysis.youtube_url,
      video_title: analysis.title,
      video_thumbnail_url: analysis.thumbnail_url
    })
  } catch (error) {
    console.error('Error in GET /api/analyses/[id]/basic:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}