import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

// Define interfaces for the emotion timeline data
interface EmotionTimelineEvent {
  startTimeSeconds: number
  endTimeSeconds: number
  audienceEmotion: 'Hu<PERSON>' | 'Joy' | 'Surprise' | 'Anger' | 'Sadness' | 'Fear' | 'Curiosity' | 'Neutral'
  intensity: 'Low' | 'Medium' | 'High' | 'Peak'
  eventDescription: string
  actorEmotion: 'Anger' | 'Joy' | 'Sadness' | 'Neutral' | null
}

interface AdvancedGeminiResponse {
  overallSentiment: 'Positive' | 'Negative' | 'Neutral' | 'Mixed'
  primaryAudienceEmotion: 'Humor' | 'Joy' | 'Surprise' | 'Anger' | 'Sadness' | 'Fear'
  analysisSummary: string
  timeline: EmotionTimelineEvent[]
}

export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Await params
    const { id } = await params
    
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const supabase = createServerSupabaseClient()
    const adAnalysisId = id

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // 1. Verify ownership
    const { data: adAnalysis, error: ownerError } = await supabase
      .from('ad_analyses')
      .select('user_id, youtube_url, status')
      .eq('id', adAnalysisId)
      .single()

    if (ownerError || !adAnalysis) {
      return NextResponse.json({ error: 'Analysis not found' }, { status: 404 })
    }

    if (adAnalysis.user_id !== user.id) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }

    // Check if analysis is completed
    if (adAnalysis.status !== 'completed') {
      return NextResponse.json({ 
        error: 'Analysis must be completed before generating emotion timeline' 
      }, { status: 400 })
    }

    // 2. Check user credits
    const { data: reportType, error: reportTypeError } = await supabase
      .from('report_types')
      .select('id, credit_cost')
      .eq('name', 'emotion_timeline')
      .single()

    if (reportTypeError || !reportType) {
      return NextResponse.json({ error: 'Report type not configured' }, { status: 500 })
    }

    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('credits_remaining')
      .eq('user_id', user.id)
      .single()

    if (profileError || !profile) {
      return NextResponse.json({ error: 'User profile not found' }, { status: 404 })
    }

    // Credit check disabled for testing
    console.log(`🧪 Testing mode: Bypassing credit check for emotion_timeline`)
    // if (profile.credits_remaining < reportType.credit_cost) {
    //   return NextResponse.json({ error: 'Insufficient credits' }, { status: 402 })
    // }

    // Check if this report already exists
    const { data: existingReport, error: existingReportError } = await supabase
      .from('analysis_reports')
      .select('id, status')
      .eq('analysis_id', adAnalysisId)
      .eq('report_type_id', reportType.id)
      .single()

    if (existingReport && existingReport.status === 'generated') {
      return NextResponse.json({
        message: 'Emotion timeline already generated',
        report_id: existingReport.id
      }, { status: 200 })
    }

    // 3. Create or update analysis_reports record
    let reportId: string
    if (existingReport) {
      // Update existing report to generating status
      const { data: updatedReport, error: updateError } = await supabase
        .from('analysis_reports')
        .update({ status: 'generating' })
        .eq('id', existingReport.id)
        .select('id')
        .single()
      
      if (updateError) {
        console.error('Error updating report:', updateError)
        return NextResponse.json({ error: 'Failed to update report' }, { status: 500 })
      }
      reportId = updatedReport.id
    } else {
      // Create new analysis report
      const { data: newReport, error: newReportError } = await supabase
        .from('analysis_reports')
        .insert({
          analysis_id: adAnalysisId,
          report_type_id: reportType.id,
          status: 'generating',
        })
        .select('id')
        .single()

      if (newReportError) {
        return NextResponse.json({ error: 'Failed to create report record' }, { status: 500 })
      }
      reportId = newReport.id
    }

    // 4. Generate emotion timeline using Gemini API
    try {
      console.log('🎭 Starting emotion timeline generation for analysis:', adAnalysisId)

      // Here we'll add the actual Gemini API call
      // For now, let's create mock data to test the flow
      const mockAnalysisData: AdvancedGeminiResponse = {
        overallSentiment: 'Positive',
        primaryAudienceEmotion: 'Joy',
        analysisSummary: 'This ad demonstrates a positive emotional arc with strong engagement throughout. The content successfully evokes joy and curiosity in viewers while maintaining a clear narrative structure.',
        timeline: [
          {
            startTimeSeconds: 0,
            endTimeSeconds: 5,
            audienceEmotion: 'Curiosity',
            intensity: 'Medium',
            eventDescription: 'Opening scene introduces the main character with intriguing visuals',
            actorEmotion: 'Neutral'
          },
          {
            startTimeSeconds: 5,
            endTimeSeconds: 15,
            audienceEmotion: 'Joy',
            intensity: 'High',
            eventDescription: 'Humorous interaction between characters creates positive emotional response',
            actorEmotion: 'Joy'
          },
          {
            startTimeSeconds: 15,
            endTimeSeconds: 25,
            audienceEmotion: 'Surprise',
            intensity: 'Peak',
            eventDescription: 'Unexpected plot twist captures audience attention',
            actorEmotion: 'Joy'
          },
          {
            startTimeSeconds: 25,
            endTimeSeconds: 30,
            audienceEmotion: 'Joy',
            intensity: 'High',
            eventDescription: 'Satisfying resolution with clear call-to-action',
            actorEmotion: 'Joy'
          }
        ]
      }

      // TODO: Replace with actual Gemini API call
      // const analysisData = await generateEmotionTimelineWithGemini(adAnalysis.youtube_url)
      const analysisData = mockAnalysisData

      // 5. Create the main sentiment_analyses record
      const { data: sentimentAnalysis, error: sentimentError } = await supabase
        .from('sentiment_analyses')
        .insert({
          ad_analysis_id: adAnalysisId,
          overall_sentiment: analysisData.overallSentiment,
          primary_audience_emotion: analysisData.primaryAudienceEmotion,
          analysis_summary: analysisData.analysisSummary,
        })
        .select('analysis_id')
        .single()

      if (sentimentError) {
        throw new Error(`Failed to create sentiment analysis record: ${sentimentError.message}`)
      }
      const newAnalysisId = sentimentAnalysis.analysis_id

      // 6. Insert all timeline events
      const timelineEventsToInsert = analysisData.timeline.map(event => ({
        analysis_id: newAnalysisId,
        start_time_seconds: event.startTimeSeconds,
        end_time_seconds: event.endTimeSeconds,
        audience_emotion: event.audienceEmotion,
        intensity: event.intensity,
        event_description: event.eventDescription,
        actor_emotion: event.actorEmotion,
      }))

      const { error: eventsError } = await supabase
        .from('emotion_timeline_events')
        .insert(timelineEventsToInsert)

      if (eventsError) {
        throw new Error(`Failed to insert emotion timeline events: ${eventsError.message}`)
      }

      // 7. Update the analysis_reports table with summary
      await supabase
        .from('analysis_reports')
        .update({
          content: {
            summary: analysisData.analysisSummary,
            overallSentiment: analysisData.overallSentiment,
            primaryAudienceEmotion: analysisData.primaryAudienceEmotion,
            timelineEventCount: analysisData.timeline.length,
            sentiment_analysis_id: newAnalysisId 
          },
          status: 'generated',
          generated_at: new Date().toISOString(),
        })
        .eq('id', reportId)

      console.log('✅ Emotion timeline analysis completed successfully:', reportId)

      // Credit deduction disabled for testing
      console.log(`🧪 Testing mode: Skipping credit deduction for emotion_timeline`)
      // const { error: creditError } = await supabase.rpc('decrement_credits', {
      //   user_id_param: user.id,
      //   amount: reportType.credit_cost,
      // })

      return NextResponse.json({ 
        success: true, 
        reportId,
        message: 'Emotion timeline generated successfully',
        timelineEventCount: analysisData.timeline.length,
        overallSentiment: analysisData.overallSentiment
      }, { status: 200 })

    } catch (generationError) {
      console.error('❌ Error during emotion timeline generation:', generationError)
      
      // Mark report as error
      await supabase
        .from('analysis_reports')
        .update({ status: 'error' })
        .eq('id', reportId)

      return NextResponse.json({ 
        error: 'Failed to generate emotion timeline',
        message: generationError instanceof Error ? generationError.message : 'Unknown error'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error generating emotion timeline:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}