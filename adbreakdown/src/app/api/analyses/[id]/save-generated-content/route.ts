import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { CacheService } from '@/lib/cache'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params
    const { id } = await params
    
    const supabase = createServerSupabaseClient()
    const { report_type_name, content } = await request.json()
    
    console.log('🔨 Saving generated content:', report_type_name, 'for analysis:', id)

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)

    // If it's a slug, we need to resolve it to a UUID first
    let actualId = id
    if (!isUUID) {
      const { data: analysis, error: resolveError } = await supabase
        .from('ad_analyses')
        .select('id')
        .eq('slug', id)
        .single()
      
      if (resolveError || !analysis) {
        throw new Error(`Analysis not found for slug: ${id}`)
      }
      actualId = analysis.id
    }
    
    // Map report type names to database field names
    const fieldMapping: Record<string, string> = {
      'marketing_copy': 'marketing_copy',
      'social_media_posts': 'social_media_posts', 
      'marketing_scorecard': 'marketing_scorecard',
      'seo_keywords': 'seo_keywords',
      'content_suggestions': 'content_suggestions'
    }
    
    const fieldName = fieldMapping[report_type_name]
    if (!fieldName) {
      throw new Error(`Unknown report type: ${report_type_name}`)
    }
    
    // Update the analysis record directly with the generated content
    const { error } = await supabase
      .from('ad_analyses')
      .update({ 
        [fieldName]: content,
        updated_at: new Date().toISOString()
      })
      .eq('id', actualId)

    if (error) {
      console.error('❌ Error updating analysis:', error)
      throw error
    }

    // Invalidate caches after updating analysis
    await CacheService.invalidateAnalysis(actualId)
    await CacheService.invalidatePattern(`private:analysis:${id}:*`)
    await CacheService.invalidatePattern(`public:analysis:${id}`)
    
    console.log('✅ Generated content saved successfully and caches invalidated')
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('❌ Save generated content error:', error)
    return NextResponse.json(
      { error: 'Failed to save generated content' },
      { status: 500 }
    )
  }
}