import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { CacheService } from '@/lib/cache'

// POST /api/analyses/{id}/toggle-public - Toggle public status of analysis
export async function POST(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Await params
    const { id } = await params
    
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const supabase = createServerSupabaseClient()

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id, clerk_id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if id is a UUID or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)

    // Get analysis with ownership check
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select('id, user_id, is_public, title, status')
      .eq(isUUID ? 'id' : 'slug', id)
      .eq('user_id', user.id)
      .single()

    if (analysisError || !analysis) {
      return NextResponse.json(
        { error: 'Analysis not found or access denied' },
        { status: 404 }
      )
    }

    // Only allow toggling for completed analyses
    if (analysis.status !== 'completed') {
      return NextResponse.json(
        { error: 'Only completed analyses can be made public' },
        { status: 400 }
      )
    }

    // Toggle the public status
    const newPublicStatus = !analysis.is_public

    const { data: updatedAnalysis, error: updateError } = await supabase
      .from('ad_analyses')
      .update({ 
        is_public: newPublicStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', analysis.id)
      .eq('user_id', user.id)
      .select('id, is_public, title')
      .single()

    if (updateError) {
      console.error('Error updating analysis public status:', updateError)
      return NextResponse.json(
        { error: 'Failed to update analysis visibility' },
        { status: 500 }
      )
    }

    // Invalidate all relevant caches after changing public status
    await CacheService.invalidateAnalysis(analysis.id)
    await CacheService.invalidatePattern(`private:analysis:${id}:*`)
    await CacheService.invalidatePattern(`private:analysis:${analysis.id}:*`)
    await CacheService.invalidatePattern(`public:analysis:${id}`)
    await CacheService.invalidatePattern(`public:analysis:${analysis.id}`)
    await CacheService.invalidatePattern('public-analyses:*') // Invalidate public lists
    
    console.log(`✅ ${newPublicStatus ? 'Made public' : 'Made private'} analysis and invalidated caches:`, updatedAnalysis?.id)
    
    return NextResponse.json({
      success: true,
      analysis: updatedAnalysis,
      message: `Analysis ${newPublicStatus ? 'made public' : 'made private'} successfully`
    })

  } catch (error) {
    console.error('Error in POST /api/analyses/[id]/toggle-public:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}