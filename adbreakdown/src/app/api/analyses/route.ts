import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'
import { 
  parseYouTubeUrl, 
  generateUniqueSlug, 
  isValidYouTubeUrl 
} from '@/lib/slug-utils'
import { validateYouTubeUrl } from '@/lib/youtube-validator'
import { CacheService } from '@/lib/cache'

// POST /api/analyses - Create new analysis
export async function POST(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Use service role client for user operations (bypasses RLS)
    const supabase = createServerSupabaseClient()

    // Get user from database
    console.log('🔍 Looking for user with Clerk ID:', userId)
    let { data: user, error: userError } = await supabase
      .from('users')
      .select('id, clerk_id')
      .eq('clerk_id', userId)
      .single()
      
    console.log('🔍 User query result:', { user, userError })

    if (userError || !user) {
      console.log('🔧 User not found in database, creating user for Clerk ID:', userId)
      
      // Try to create user automatically
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          clerk_id: userId,
          email: '<EMAIL>',
          first_name: 'User',
          last_name: 'Auto'
        })
        .select()
        .single()

      if (createError || !newUser) {
        console.error('❌ Failed to create user:', createError)
        return NextResponse.json({
          error: 'User account setup failed. Please contact support.'
        }, { status: 500 })
      }

      // Create profile for the new user
      await supabase
        .from('profiles')
        .insert({
          user_id: newUser.id,
          credits_remaining: 100,
          subscription_status: 'free'
        })

      console.log('✅ Auto-created user:', newUser.id)
      user = newUser
    }

    // Get request body
    const body = await req.json()
    const { youtube_url } = body

    if (!youtube_url) {
      return NextResponse.json(
        { error: 'YouTube URL is required' },
        { status: 400 }
      )
    }

    // Comprehensive YouTube URL validation
    const validationResult = await validateYouTubeUrl(youtube_url, 120) // 2 minutes limit
    
    if (!validationResult.isValid) {
      // If validation failed due to duration limit, return special response
      if (validationResult.error?.includes('exceeds the') && validationResult.duration) {
        return NextResponse.json(
          { 
            error: 'video_too_long',
            message: validationResult.error,
            videoData: {
              url: youtube_url,
              duration: validationResult.duration,
              metadata: validationResult.metadata
            }
          },
          { status: 413 } // Payload Too Large
        )
      }

      return NextResponse.json(
        { error: validationResult.error },
        { status: 400 }
      )
    }

    // If there's an existing public analysis, redirect to it
    if (validationResult.existingPublicAnalysis) {
      console.log('🔄 Found existing public analysis:', validationResult.existingPublicAnalysis.id)
      return NextResponse.json(
        { 
          analysis_id: validationResult.existingPublicAnalysis.id,
          slug: validationResult.existingPublicAnalysis.slug,
          status: 'completed',
          message: 'Public analysis already exists for this video',
          isPublic: true
        },
        { status: 200 }
      )
    }

    console.log('🔍 Validated YouTube URL:', validationResult)

    // Check if this user already has an analysis for this video
    const { data: existingUserAnalysis, error: existingUserError } = await supabase
      .from('ad_analyses')
      .select('id, slug, status')
      .eq('user_id', user!.id)
      .eq('youtube_video_id', validationResult.videoId)
      .single()

    if (existingUserAnalysis && !existingUserError) {
      console.log('🔄 User already has analysis for this video:', existingUserAnalysis.id)
      return NextResponse.json(
        { 
          analysis_id: existingUserAnalysis.id,
          slug: existingUserAnalysis.slug,
          status: existingUserAnalysis.status,
          message: 'Analysis already exists for this video'
        },
        { status: 200 }
      )
    }

    // Check if any analysis exists for this video (for potential sharing)
    const { data: existingAnalysis, error: existingError } = await supabase
      .from('ad_analyses')
      .select('id, slug, status, title, inferred_brand')
      .eq('youtube_video_id', validationResult.videoId)
      .eq('status', 'completed')
      .limit(1)
      .single()

    let analysisData: any = {
      user_id: user!.id,
      youtube_url: validationResult.normalizedUrl,
      youtube_video_id: validationResult.videoId,
      status: 'pending',
      duration_seconds: validationResult.duration?.seconds,
      duration_formatted: validationResult.duration?.formatted,
      title: validationResult.metadata?.title,
      thumbnail_url: validationResult.metadata?.thumbnailUrl
    }

    // If a completed analysis exists, we can optionally pre-populate some data
    // For now, we'll create a fresh analysis but could implement sharing later
    if (existingAnalysis && !existingError) {
      console.log('📝 Found existing completed analysis, creating new user copy')
      // Could copy title, brand, etc. here if desired
      // analysisData.title = existingAnalysis.title
      // analysisData.inferred_brand = existingAnalysis.inferred_brand
    }

    // Generate initial slug (will be updated when title/brand are available)
    const initialSlug = await generateUniqueSlug(
      null, // brand not available yet
      validationResult.metadata?.title || null, // title from metadata if available
      validationResult.videoId!,
      supabase,
      'video-analysis'
    )

    analysisData.slug = initialSlug

    // Create new ad analysis record
    console.log('🔨 Creating analysis record for user:', user!.id)
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .insert(analysisData)
      .select()
      .single()
      
    console.log('🔨 Analysis creation result:', { analysis, analysisError })

    if (analysisError) {
      console.error('Error creating analysis:', analysisError)
      return NextResponse.json(
        { error: 'Failed to create analysis' },
        { status: 500 }
      )
    }

    // Cache disabled - no invalidation needed
    // await CacheService.invalidatePattern(`dashboard:user:${user!.id}:analyses:*`)
    console.log('✅ Analysis created for user:', user!.id)

    return NextResponse.json(
      { 
        analysis_id: analysis.id,
        slug: analysis.slug,
        status: 'pending'
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('Error in POST /api/analyses:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/analyses - List analyses for user with pagination
export async function GET(req: NextRequest) {
  try {
    // Get authenticated user
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Use service role client for user operations (bypasses RLS)
    const supabase = createServerSupabaseClient()

    // Get pagination parameters
    const url = new URL(req.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const offset = (page - 1) * limit

    // Get user from database
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', userId)
      .single()

    if (userError || !user) {
      console.log('🧪 Testing mode: User not found in database, returning empty results')
      return NextResponse.json({ 
        analyses: [],
        pagination: {
          page,
          limit,
          hasMore: false
        }
      })
    }

    // Cache disabled for real-time updates
    // const cacheKey = `user:${user.id}:analyses:${page}:${limit}`
    // const cachedData = await CacheService.get(cacheKey, { prefix: 'dashboard', ttl: 60 * 10 }) // 10 minutes

    // if (cachedData) {
    //   console.log('📊 Serving cached dashboard analyses for user:', user.id)
    //   return NextResponse.json(cachedData)
    // }
    
    console.log('📊 Fetching fresh dashboard analyses for user:', user.id)

    // Get user's analyses with pagination
    const { data: analyses, error: analysesError } = await supabase
      .from('ad_analyses')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (analysesError) {
      console.error('Error fetching analyses:', analysesError)
      return NextResponse.json(
        { error: 'Failed to fetch analyses' },
        { status: 500 }
      )
    }

    // Get user's remaining credits
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('credits_remaining')
      .eq('user_id', user.id)
      .single()

    if (profileError) {
      console.error('Error fetching profile:', profileError)
      // Continue without credits, or handle as a more critical error
    }

    const responseData = { 
      analyses,
      pagination: {
        page,
        limit,
        hasMore: analyses.length === limit
      },
      credits_remaining: profile?.credits_remaining ?? 0
    }

    // Cache disabled for real-time updates
    // await CacheService.set(cacheKey, responseData, { prefix: 'dashboard', ttl: 60 * 10 })
    
    console.log('✅ Returning fresh dashboard analyses for user:', user.id)
    
    const response = NextResponse.json(responseData)
    
    // Prevent any caching of dashboard data
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    
    return response
  } catch (error) {
    console.error('Error in GET /api/analyses:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
