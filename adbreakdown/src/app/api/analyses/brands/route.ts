import { NextResponse } from 'next/server'
import { OptimizedAnalysisQueries } from '@/lib/optimizedQueries'

// GET /api/analyses/brands - Get all available filter options
export async function GET() {
  try {
    const [brands, celebrities, years] = await Promise.all([
      OptimizedAnalysisQueries.getAvailableBrands(),
      OptimizedAnalysisQueries.getAvailableCelebrities(),
      OptimizedAnalysisQueries.getAvailableYears()
    ])
    
    return NextResponse.json({
      brands,
      celebrities,
      years,
      durations: [
        { value: 'short', label: 'Short (≤30s)' },
        { value: 'medium', label: 'Medium (31-60s)' },
        { value: 'long', label: 'Long (60s+)' }
      ]
    }, {
      headers: {
        'Cache-Control': 'public, s-maxage=3600, stale-while-revalidate=7200', // 1hr cache, 2hr stale
      },
    })

  } catch (error) {
    console.error('Error in GET /api/analyses/brands:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}