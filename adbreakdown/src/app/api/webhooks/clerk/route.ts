import { Webhook } from 'svix'
import { headers } from 'next/headers'
import { WebhookEvent } from '@clerk/nextjs/server'
import { createServerSupabaseClient } from '@/lib/supabase'

export async function POST(req: Request) {
  // You can find this in the Clerk Dashboard -> Webhooks -> choose the webhook
  const WEBHOOK_SECRET = process.env.CLERK_WEBHOOK_SECRET

  if (!WEBHOOK_SECRET) {
    throw new Error('Please add CLERK_WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local')
  }

  // Get the headers
  const headerPayload = await headers()
  const svix_id = headerPayload.get("svix-id")
  const svix_timestamp = headerPayload.get("svix-timestamp")
  const svix_signature = headerPayload.get("svix-signature")

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response('Error occured -- no svix headers', {
      status: 400
    })
  }

  // Get the body
  const payload = await req.text()
  const body = JSON.parse(payload)

  // Create a new Svix instance with your secret.
  const wh = new Webhook(WEBHOOK_SECRET)

  let evt: WebhookEvent

  // Verify the payload with the headers
  try {
    evt = wh.verify(payload, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent
  } catch (err) {
    console.error('Error verifying webhook:', err)
    return new Response('Error occured', {
      status: 400
    })
  }

  // Handle the webhook
  const eventType = evt.type
  const supabase = createServerSupabaseClient()

  try {
    switch (eventType) {
      case 'user.created':
        // Create user and profile records
        const { data: user, error: userError } = await supabase
          .from('users')
          .insert({
            clerk_id: evt.data.id,
            email: evt.data.email_addresses[0]?.email_address || '',
            first_name: evt.data.first_name || '',
            last_name: evt.data.last_name || ''
          })
          .select()
          .single()

        if (userError) {
          console.error('Error creating user:', userError)
          return new Response('Error creating user', { status: 500 })
        }

        // Create profile with default credits
        const { error: profileError } = await supabase
          .from('profiles')
          .insert({
            user_id: user.id,
            credits_remaining: 100, // Free tier starts with 100 credits
            subscription_status: 'free'
          })

        if (profileError) {
          console.error('Error creating profile:', profileError)
          return new Response('Error creating profile', { status: 500 })
        }

        console.log('User created successfully in Supabase with Clerk ID:', evt.data.id)
        break

      case 'user.updated':
        // Update user record
        const { error: updateError } = await supabase
          .from('users')
          .update({
            email: evt.data.email_addresses[0]?.email_address || '',
            first_name: evt.data.first_name || '',
            last_name: evt.data.last_name || ''
          })
          .eq('clerk_id', evt.data.id)

        if (updateError) {
          console.error('Error updating user:', updateError)
          return new Response('Error updating user', { status: 500 })
        }

        console.log('User updated successfully:', evt.data.id)
        break

      case 'user.deleted':
        // Delete user record (profile will be cascade deleted)
        const { error: deleteError } = await supabase
          .from('users')
          .delete()
          .eq('clerk_id', evt.data.id || '')

        if (deleteError) {
          console.error('Error deleting user:', deleteError)
          return new Response('Error deleting user', { status: 500 })
        }

        console.log('User deleted successfully:', evt.data.id)
        break

      default:
        console.log('Unhandled webhook event:', eventType)
    }

    return new Response('Webhook processed successfully', { status: 200 })
  } catch (error) {
    console.error('Error processing webhook:', error)
    return new Response('Error processing webhook', { status: 500 })
  }
}
