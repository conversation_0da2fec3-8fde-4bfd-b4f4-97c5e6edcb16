import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import crypto from 'crypto'
import { supabase } from '@/lib/supabase'

interface LemonSqueezyWebhook {
  meta: {
    event_name: string
    custom_data?: Record<string, any>
  }
  data: {
    type: string
    id: string
    attributes: {
      store_id: number
      customer_id: number
      order_id?: number
      order_item_id?: number
      product_id: number
      variant_id: number
      product_name: string
      variant_name: string
      user_name?: string
      user_email: string
      status: string
      status_formatted: string
      card_brand?: string
      card_last_four?: string
      pause?: any
      cancelled: boolean
      trial_ends_at?: string
      billing_anchor?: number
      urls: {
        update_payment_method: string
        customer_portal: string
      }
      renews_at?: string
      ends_at?: string
      created_at: string
      updated_at: string
      test_mode: boolean
    }
  }
}

// Subscription plan configurations
const SUBSCRIPTION_PLANS = {
  starter: { credits: 50, price: 9.99 },
  pro: { credits: 150, price: 19.99 },
  enterprise: { credits: 500, price: 49.99 }
}

export async function POST(req: NextRequest) {
  try {
    const rawBody = await req.text()
    const signature = (await headers()).get('x-signature')
    
    // Verify webhook signature
    const secret = process.env.LEMON_SQUEEZY_WEBHOOK_SECRET
    if (!secret) {
      console.error('LEMON_SQUEEZY_WEBHOOK_SECRET not configured')
      return NextResponse.json({ error: 'Webhook secret not configured' }, { status: 500 })
    }

    if (signature) {
      const expectedSignature = crypto
        .createHmac('sha256', secret)
        .update(rawBody)
        .digest('hex')
      
      const providedSignature = signature.replace('sha256=', '')
      
      if (!crypto.timingSafeEqual(
        Buffer.from(expectedSignature, 'hex'),
        Buffer.from(providedSignature, 'hex')
      )) {
        console.error('Invalid webhook signature')
        return NextResponse.json({ error: 'Invalid signature' }, { status: 401 })
      }
    }

    const webhook: LemonSqueezyWebhook = JSON.parse(rawBody)
    const { meta, data } = webhook

    console.log('Lemon Squeezy webhook received:', meta.event_name, data.id)

    // Get user by email
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('email', data.attributes.user_email)
      .single()

    if (userError || !user) {
      console.error('User not found for email:', data.attributes.user_email)
      // Still return success to avoid webhook retries
      return NextResponse.json({ message: 'User not found, webhook logged' }, { status: 200 })
    }

    // Handle different webhook events
    switch (meta.event_name) {
      case 'subscription_created':
        await handleSubscriptionCreated(user.id, data)
        break
      
      case 'subscription_updated':
        await handleSubscriptionUpdated(user.id, data)
        break
      
      case 'subscription_cancelled':
        await handleSubscriptionCancelled(user.id, data)
        break
      
      case 'subscription_resumed':
        await handleSubscriptionResumed(user.id, data)
        break
      
      case 'subscription_expired':
        await handleSubscriptionExpired(user.id, data)
        break
      
      case 'subscription_paused':
        await handleSubscriptionPaused(user.id, data)
        break
      
      case 'subscription_unpaused':
        await handleSubscriptionUnpaused(user.id, data)
        break

      case 'subscription_payment_success':
        await handlePaymentSuccess(user.id, data)
        break

      case 'subscription_payment_failed':
        await handlePaymentFailed(user.id, data)
        break
      
      default:
        console.log('Unhandled webhook event:', meta.event_name)
    }

    return NextResponse.json({ message: 'Webhook processed successfully' }, { status: 200 })
  } catch (error) {
    console.error('Error processing Lemon Squeezy webhook:', error)
    return NextResponse.json(
      { error: 'Error processing webhook' }, 
      { status: 500 }
    )
  }
}

async function handleSubscriptionCreated(userId: string, data: LemonSqueezyWebhook['data']) {
  const planName = getPlanFromVariant(data.attributes.variant_name)
  const planConfig = SUBSCRIPTION_PLANS[planName as keyof typeof SUBSCRIPTION_PLANS]
  
  if (!planConfig) {
    console.error('Unknown plan variant:', data.attributes.variant_name)
    return
  }

  // Update user profile
  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: 'active',
      subscription_plan: planName,
      subscription_period_end: data.attributes.renews_at,
      credits_remaining: planConfig.credits,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)

  if (error) {
    console.error('Error updating profile for subscription creation:', error)
  } else {
    console.log(`Subscription created for user ${userId}: ${planName} plan`)
  }
}

async function handleSubscriptionUpdated(userId: string, data: LemonSqueezyWebhook['data']) {
  const planName = getPlanFromVariant(data.attributes.variant_name)
  const status = data.attributes.cancelled ? 'cancelled' : 'active'

  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: status,
      subscription_plan: planName,
      subscription_period_end: data.attributes.renews_at || data.attributes.ends_at,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)

  if (error) {
    console.error('Error updating profile for subscription update:', error)
  } else {
    console.log(`Subscription updated for user ${userId}: ${status}`)
  }
}

async function handleSubscriptionCancelled(userId: string, data: LemonSqueezyWebhook['data']) {
  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: 'cancelled',
      subscription_period_end: data.attributes.ends_at,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)

  if (error) {
    console.error('Error updating profile for subscription cancellation:', error)
  } else {
    console.log(`Subscription cancelled for user ${userId}`)
  }
}

async function handleSubscriptionResumed(userId: string, data: LemonSqueezyWebhook['data']) {
  const planName = getPlanFromVariant(data.attributes.variant_name)

  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: 'active',
      subscription_plan: planName,
      subscription_period_end: data.attributes.renews_at,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)

  if (error) {
    console.error('Error updating profile for subscription resume:', error)
  } else {
    console.log(`Subscription resumed for user ${userId}`)
  }
}

async function handleSubscriptionExpired(userId: string, data: LemonSqueezyWebhook['data']) {
  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: 'expired',
      subscription_plan: null,
      subscription_period_end: null,
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)

  if (error) {
    console.error('Error updating profile for subscription expiration:', error)
  } else {
    console.log(`Subscription expired for user ${userId}`)
  }
}

async function handleSubscriptionPaused(userId: string, data: LemonSqueezyWebhook['data']) {
  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: 'paused',
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)

  if (error) {
    console.error('Error updating profile for subscription pause:', error)
  }
}

async function handleSubscriptionUnpaused(userId: string, data: LemonSqueezyWebhook['data']) {
  const { error } = await supabase
    .from('profiles')
    .update({
      subscription_status: 'active',
      updated_at: new Date().toISOString()
    })
    .eq('user_id', userId)

  if (error) {
    console.error('Error updating profile for subscription unpause:', error)
  }
}

async function handlePaymentSuccess(userId: string, data: LemonSqueezyWebhook['data']) {
  const planName = getPlanFromVariant(data.attributes.variant_name)
  const planConfig = SUBSCRIPTION_PLANS[planName as keyof typeof SUBSCRIPTION_PLANS]
  
  if (planConfig) {
    // Refresh credits for successful payment
    const { error } = await supabase.rpc('add_credits', {
      user_id_param: userId,
      amount: planConfig.credits
    })

    if (error) {
      console.error('Error adding credits for payment success:', error)
    } else {
      console.log(`Credits refreshed for user ${userId}: ${planConfig.credits} credits`)
    }
  }
}

async function handlePaymentFailed(userId: string, data: LemonSqueezyWebhook['data']) {
  // For now, just log the failure
  // In production, you might want to send notifications or implement grace periods
  console.log(`Payment failed for user ${userId}, subscription ${data.id}`)
}

function getPlanFromVariant(variantName: string): string {
  const name = variantName.toLowerCase()
  if (name.includes('starter')) return 'starter'
  if (name.includes('pro')) return 'pro'
  if (name.includes('enterprise')) return 'enterprise'
  return 'starter' // default fallback
}
