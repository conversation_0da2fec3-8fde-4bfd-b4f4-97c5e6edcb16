'use client'

import Navigation from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Bot, Sparkles, Zap, Target, TrendingUp, Clock } from 'lucide-react'
import Link from 'next/link'

export default function AdAgentPage() {
  const features = [
    {
      icon: <Bot className="h-8 w-8 text-blue-600" />,
      title: 'AI-Powered Agent',
      description: 'Your personal AI assistant that understands video advertising and marketing strategies.'
    },
    {
      icon: <Sparkles className="h-8 w-8 text-purple-600" />,
      title: 'Creative Generation',
      description: 'Generate ad scripts, storyboards, and creative concepts based on successful patterns.'
    },
    {
      icon: <Target className="h-8 w-8 text-green-600" />,
      title: 'Smart Targeting',
      description: 'Get AI-recommended audience segments and targeting strategies for maximum impact.'
    },
    {
      icon: <TrendingUp className="h-8 w-8 text-orange-600" />,
      title: 'Performance Optimization',
      description: 'Continuously learn from your campaigns and suggest improvements in real-time.'
    },
    {
      icon: <Zap className="h-8 w-8 text-yellow-600" />,
      title: 'Instant Insights',
      description: 'Get immediate feedback on your ad concepts before you invest in production.'
    },
    {
      icon: <Clock className="h-8 w-8 text-indigo-600" />,
      title: '24/7 Availability',
      description: 'Your AI agent works around the clock, ready to help whenever inspiration strikes.'
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <Badge variant="secondary" className="bg-blue-100 text-blue-700 px-4 py-2 text-lg">
              <Sparkles className="h-4 w-4 mr-2" />
              Coming Soon
            </Badge>
          </div>
          
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            Meet Your AI Ad Agent
          </h1>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            The future of advertising is here. Our AI Agent will be your personal marketing assistant, 
            helping you create, optimize, and scale video ads like never before.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-blue-600 hover:bg-blue-700" disabled>
              <Sparkles className="h-5 w-5 mr-2" />
              Join Waitlist
            </Button>
            <Link href="/ad">
              <Button size="lg" variant="outline">
                Explore Ad Library
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            What to Expect from Ad Agent
          </h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex justify-center mb-4">
                    {feature.icon}
                  </div>
                  <CardTitle className="text-xl mb-2">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Timeline Section */}
        <div className="bg-gray-50 rounded-2xl p-8 mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">
            Development Timeline
          </h2>
          
          <div className="max-w-3xl mx-auto">
            <div className="space-y-6">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <div className="w-6 h-6 bg-green-600 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Phase 1: Core Analysis Engine</h3>
                  <p className="text-gray-600">✅ Completed - Advanced video ad analysis capabilities</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <div className="w-6 h-6 bg-blue-600 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Phase 2: AI Agent Development</h3>
                  <p className="text-gray-600">🔄 In Progress - Building conversational AI and creative generation</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                  <div className="w-6 h-6 bg-gray-400 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Phase 3: Beta Testing</h3>
                  <p className="text-gray-600">⏳ Q2 2025 - Limited beta access for early adopters</p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                  <div className="w-6 h-6 bg-gray-400 rounded-full"></div>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Phase 4: Public Launch</h3>
                  <p className="text-gray-600">🚀 Q3 2025 - Full public release with all features</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Transform Your Ad Strategy?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            While you wait for Ad Agent, start analyzing video ads today with our current platform.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/sign-up">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Start Analyzing Ads Now
              </Button>
            </Link>
            <Link href="/contact">
              <Button size="lg" variant="outline">
                Get Early Access Updates
              </Button>
            </Link>
          </div>
        </div>
      </main>
    </div>
  )
}