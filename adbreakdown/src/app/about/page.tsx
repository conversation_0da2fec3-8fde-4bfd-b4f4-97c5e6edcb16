import Navigation from '@/components/Navigation'

// Static generation - no revalidation needed
export const revalidate = false
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Target, Brain, Users, Zap, Award, Globe } from 'lucide-react'
import Link from 'next/link'

export default function AboutPage() {
  const values = [
    {
      icon: <Brain className="h-8 w-8 text-blue-600" />,
      title: 'AI-Powered Insights',
      description: 'We leverage cutting-edge artificial intelligence to transform subjective ad feedback into objective, actionable insights.'
    },
    {
      icon: <Target className="h-8 w-8 text-green-600" />,
      title: 'Data-Driven Decisions',
      description: 'Every recommendation is backed by data analysis, helping marketers make informed decisions about their advertising strategies.'
    },
    {
      icon: <Users className="h-8 w-8 text-purple-600" />,
      title: 'Community First',
      description: 'We believe in the power of shared knowledge. Our community platform allows marketers to learn from each other.'
    },
    {
      icon: <Zap className="h-8 w-8 text-yellow-600" />,
      title: 'Speed & Efficiency',
      description: 'Get comprehensive ad analysis in under 60 seconds, allowing you to iterate quickly and optimize continuously.'
    }
  ]

  const stats = [
    { number: '50K+', label: 'Ads Analyzed' },
    { number: '97%', label: 'Analysis Accuracy' },
    { number: '10K+', label: 'Active Users' },
    { number: '4.2x', label: 'Avg. ROI Improvement' }
  ]

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 mb-6">
            About Breakdown.Ad
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            We&apos;re on a mission to democratize video advertising insights through AI-powered analysis, 
            helping marketers of all sizes create more effective and engaging advertisements.
          </p>
        </div>

        {/* Mission Section */}
        <div className="mb-16">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">Our Mission</h2>
              <p className="text-lg text-gray-600 mb-6">
                Traditional ad feedback is subjective, slow, and expensive. We&apos;re changing that by providing 
                instant, objective analysis powered by advanced AI that understands what makes video ads successful.
              </p>
              <p className="text-lg text-gray-600 mb-6">
                Whether you&apos;re a solo marketer, small agency, or enterprise brand, our platform gives you 
                the insights needed to create ads that truly resonate with your audience.
              </p>
              <Link href="/sign-up">
                <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                  Start Your Free Analysis
                </Button>
              </Link>
            </div>
            <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8">
              <div className="grid grid-cols-2 gap-6">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-3xl font-bold text-blue-600 mb-2">{stat.number}</div>
                    <div className="text-gray-600">{stat.label}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Values Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            What Drives Us
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-4">
                    {value.icon}
                    <CardTitle className="text-xl">{value.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600 text-base">
                    {value.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Story Section */}
        <div className="bg-gray-50 rounded-2xl p-8 mb-16">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">
              Our Story
            </h2>
            <div className="prose prose-lg mx-auto text-gray-600">
              <p className="mb-6">
                Breakdown.Ad was born from a simple frustration: why was it so hard to get objective 
                feedback on video advertisements? As marketers ourselves, we experienced firsthand 
                the challenges of relying on subjective opinions and expensive focus groups.
              </p>
              <p className="mb-6">
                We realized that with advances in AI and machine learning, we could analyze the 
                elements that make ads successful - from sentiment and emotion to visual appeal 
                and targeting effectiveness - all in real-time.
              </p>
              <p className="mb-6">
                Today, we&apos;re proud to serve thousands of marketers worldwide, from individual 
                creators to Fortune 500 companies, helping them make data-driven decisions 
                about their video advertising strategies.
              </p>
            </div>
          </div>
        </div>

        {/* Technology Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">
            Our Technology
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardHeader>
                <Award className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                <CardTitle>Advanced AI Models</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Our proprietary AI models are trained on millions of successful ads to identify 
                  patterns and predict performance.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Globe className="h-12 w-12 text-green-600 mx-auto mb-4" />
                <CardTitle>Real-time Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Get comprehensive analysis in under 60 seconds using our cloud-based 
                  processing infrastructure.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Users className="h-12 w-12 text-purple-600 mx-auto mb-4" />
                <CardTitle>Community Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Learn from a community of marketers sharing their successful ad strategies 
                  and insights.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Transform Your Ad Strategy?
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of marketers who are already using AI-powered insights to create 
            better, more effective video advertisements.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/sign-up">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Start Free Analysis
              </Button>
            </Link>
            <Link href="/ad">
              <Button size="lg" variant="outline">
                Explore Community
              </Button>
            </Link>
          </div>
        </div>
      </main>
    </div>
  )
}