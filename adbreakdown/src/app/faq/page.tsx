'use client'

import Navigation from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { HelpCircle, MessageCircle } from 'lucide-react'
import Link from 'next/link'

export default function FAQPage() {
  const faqs = [
    {
      category: 'General Questions',
      questions: [
        {
          question: 'What is AdBreakdown?',
          answer: 'AdBreakdown is an AI-powered SaaS platform that provides in-depth analysis of YouTube video advertisements. We help you understand what makes an ad effective, generate new creative assets, and optimize your ad campaigns using data-driven insights.'
        },
        {
          question: 'How does AdBreakdown work?',
          answer: 'You simply paste a public YouTube video ad URL into our platform. Our AI (powered by Google Gemini) analyzes the video\'s content, including visuals, audio, and transcript, to provide comprehensive reports and generate new marketing materials.'
        },
        {
          question: 'Is AdBreakdown free to use?',
          answer: 'AdBreakdown offers various subscription plans, including options for free analysis or a free trial to get started. Certain advanced AI features and higher usage limits may require a paid subscription. Please refer to our Pricing page for more details.'
        },
        {
          question: 'What kind of videos can I analyze?',
          answer: 'You can analyze any *public* YouTube video. Please ensure the video is publicly accessible and complies with YouTube\'s terms of service. We cannot analyze private, unlisted, or age-restricted videos.'
        }
      ]
    },
    {
      category: 'Features & Analysis',
      questions: [
        {
          question: 'What insights does AdBreakdown provide?',
          answer: 'Our analysis reports cover a wide range of insights, including:\n*   **Expert Marketing Analysis**: A 10-point breakdown of the ad\'s strategy and effectiveness.\n*   **Sentiment & Emotion Analysis**: Understanding the overall emotional tone and specific emotions conveyed in the ad.\n*   **Script Analysis**: Full transcript, summary, and key themes from the video\'s spoken content.\n*   **Visual Analysis**: Insights into visual appeal, color palette, and scene breakdowns.\n*   **Audio Analysis**: Mood of music, voice tone, and overall audio quality.\n*   **Targeting Recommendations**: AI-inferred demographics, interests, and behaviors for your target audience.\n*   **Competitor Comparison**: (Currently based on inferred data) How your ad stacks up against industry benchmarks.'
        },
        {
          question: 'What are LLM-powered features?',
          answer: 'LLM (Large Language Model) powered features are advanced AI capabilities that generate new creative assets and deeper insights based on your ad analysis. These include:\n*   **Marketing Copy & Headlines Generation**: Create compelling text for your campaigns.\n*   **Social Media Posts Generation**: Get ready-to-use posts tailored for different platforms.\n*   **Marketing Scorecard**: A quantitative evaluation of your ad\'s marketing parameters.\n*   **SEO Keyword Extraction**: Identify relevant keywords to improve your ad\'s discoverability.\n*   **Content Improvement Suggestions**: Actionable advice to enhance your video content.'
        },
        {
          question: 'Do LLM-powered features cost extra?',
          answer: 'Yes, generating LLM-powered content typically consumes additional credits. The exact credit cost is displayed next to each feature button. You can acquire more credits through our subscription plans or by purchasing credit packs.'
        },
        {
          question: 'What is the Daily Showcase?',
          answer: 'The Daily Showcase is a curated section of our website featuring a single, outstanding ad analysis each day. It\'s fully accessible to everyone, even without an account, allowing you to explore high-quality analyses and learn from successful campaigns.'
        },
        {
          question: 'What is Public Analysis (`/ad-library`)?',
          answer: 'The `/ad-library` page allows you to browse a collection of public ad analyses shared by other users. These analyses are made public by their creators and offer a valuable resource for market research and learning. You do not need to be logged in to view public analyses.'
        }
      ]
    },
    {
      category: 'Account & Billing',
      questions: [
        {
          question: 'How do I create an account?',
          answer: 'You can sign up for a free account using your email address or by connecting your Google or GitHub account. Our authentication is handled securely by Clerk.'
        },
        {
          question: 'How do subscriptions work?',
          answer: 'Subscriptions provide access to our Service and a certain number of credits. They renew automatically on a monthly or annual basis. You can manage or cancel your subscription through your account settings.'
        },
        {
          question: 'What happens if I run out of credits?',
          answer: 'If you run out of credits, you won\'t be able to perform new analyses or generate LLM-powered content until you acquire more. You can upgrade your subscription plan or purchase additional credit packs.'
        },
        {
          question: 'Are my credits refundable?',
          answer: 'Generally, credits are non-refundable and do not roll over to the next billing cycle. Please check your specific subscription plan details for any exceptions or promotional terms.'
        }
      ]
    },
    {
      category: 'Privacy & Data',
      questions: [
        {
          question: 'What information does AdBreakdown collect?',
          answer: 'We collect information you provide (like your email for account creation, YouTube URLs for analysis), data collected automatically (usage data, device info, log data), and information from third-party services (YouTube API for video metadata, Clerk for authentication, Lemon Squeezy for billing, Google Gemini API for AI processing). For full details, please see our [Privacy Policy](#adbreakdown-privacy-policy).'
        },
        {
          question: 'How is my data used?',
          answer: 'Your data is used to provide and improve the Service, personalize your experience, manage billing, communicate with you, and for security and legal compliance. We use aggregated and anonymized analysis data to improve our AI models.'
        },
        {
          question: 'Is my analysis data private?',
          answer: 'By default, your ad analyses are private and only accessible by you when you are logged into your account. You have the option to make your analyses public using a toggle within the platform. If you choose to make an analysis public, it will be visible to all users on our `ad-library` and potentially `daily-showcase` pages.'
        },
        {
          question: 'Do you share my data with third parties?',
          answer: 'We share data with necessary service providers (e.g., Clerk, Lemon Squeezy, Google Gemini API, YouTube API) to operate the Service. We may also share aggregated or anonymized data. We will only share your specific analysis data publicly if you explicitly choose to make it public. For more details, please refer to our [Privacy Policy](#adbreakdown-privacy-policy).'
        },
        {
          question: 'How long do you retain my data?',
          answer: 'We retain your personal information and analysis data for as long as necessary to provide the Service, comply with legal obligations, resolve disputes, and enforce our agreements. Specific retention periods vary based on data type and purpose. For example, analysis results for active accounts are generally retained indefinitely unless you request deletion.'
        },
        {
          question: 'How can I manage my data or delete my account?',
          answer: 'You have rights regarding your data, including access, rectification, and erasure. You can manage some of your information through your account settings. To exercise your data rights or delete your account, please contact us at [<EMAIL>].'
        }
      ]
    },
    {
      category: 'Legal & Terms',
      questions: [
        {
          question: 'What are the Terms and Conditions of using AdBreakdown?',
          answer: 'Our Terms and Conditions outline the rules for using our Service, including account responsibilities, subscription terms, acceptable use policies, intellectual property rights, and limitations of liability. By using AdBreakdown, you agree to these terms. We encourage you to read the full [Terms and Conditions](#adbreakdown-terms-and-conditions) document.'
        },
        {
          question: 'What is my responsibility regarding YouTube videos?',
          answer: 'When you use AdBreakdown to analyze YouTube videos, you agree to comply with the [YouTube Terms of Service](https://www.youtube.com/t/terms) and [Google Privacy Policy](https://policies.google.com/privacy). You are responsible for ensuring your use of YouTube videos and the derived data complies with all applicable YouTube policies.'
        },
        {
          question: 'Can I use the AI-generated content for my own marketing?',
          answer: 'Yes, you own the AI-generated content created through your use of the Service. However, AdBreakdown retains a license to use this content for the purpose of operating, improving, and promoting the Service, including training our AI models, provided such use does not identify you or your specific ad campaigns without your explicit consent.'
        },
        {
          question: 'What if the AI analysis is inaccurate?',
          answer: 'AI models are probabilistic and may occasionally produce inaccurate or unexpected outputs. AdBreakdown does not warrant the accuracy, completeness, or reliability of any AI-generated analysis, insights, or content. AI-generated content is for informational and creative assistance purposes only and should not be relied upon as factual or professional advice.'
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-white">
      <Navigation />
      
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center mb-6">
            <div className="p-3 bg-blue-100 rounded-full">
              <HelpCircle className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-gray-600">
            Find answers to common questions about AdBreakdown
          </p>
        </div>

        {/* FAQ Sections */}
        <div className="space-y-12">
          {faqs.map((category, categoryIndex) => (
            <div key={categoryIndex}>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {category.category}
              </h2>
              
              <Accordion type="single" collapsible className="space-y-4">
                {category.questions.map((faq, questionIndex) => (
                  <AccordionItem 
                    key={questionIndex} 
                    value={`${categoryIndex}-${questionIndex}`}
                    className="border border-gray-200 rounded-lg px-6"
                  >
                    <AccordionTrigger className="text-left hover:no-underline">
                      <span className="font-semibold text-gray-900">
                        {faq.question}
                      </span>
                    </AccordionTrigger>
                    <AccordionContent className="text-gray-600 pb-4">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </div>
          ))}
        </div>

        {/* Contact Section */}
        <div className="mt-16 text-center bg-gray-50 rounded-2xl p-8">
          <MessageCircle className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Still Have Questions?
          </h2>
          <p className="text-gray-600 mb-6">
            Can&apos;t find what you&apos;re looking for? Our support team is here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Contact Support
              </Button>
            </Link>
            <Link href="/ad-library">
              <Button size="lg" variant="outline">
                Explore Community
              </Button>
            </Link>
          </div>
        </div>

        {/* Quick Links */}
        <div className="mt-12 text-center">
          <p className="text-gray-500 mb-4">Popular pages:</p>
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Link href="/pricing" className="text-blue-600 hover:text-blue-800">
              Pricing
            </Link>
            <Link href="/about" className="text-blue-600 hover:text-blue-800">
              About Us
            </Link>
            <Link href="/terms" className="text-blue-600 hover:text-blue-800">
              Terms of Service
            </Link>
            <Link href="/privacy" className="text-blue-600 hover:text-blue-800">
              Privacy Policy
            </Link>
          </div>
        </div> {/* <-- Add this closing div */}
      </main>
    </div>
  )
}