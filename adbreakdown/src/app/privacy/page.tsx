'use client'

import Navigation from '@/components/Navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Shield, Calendar, Database, Share2, Lock, Eye, Globe, Clock } from 'lucide-react'
import Link from 'next/link'

const privacyData = {
  lastUpdated: "July 5, 2025",
  title: "Privacy Policy",
  subtitle: "Your privacy and data protection are our top priorities",
  sections: [
    {
      id: "intro",
      icon: Shield,
      title: "Introduction",
      description: "How we protect your privacy",
      content: [
        "This Privacy Policy describes how AdBreakdown (\"we,\" \"us,\" or \"our\") collects, uses, and discloses your information when you use our AI-powered video ad analysis SaaS platform.",
        "We are committed to protecting your privacy and ensuring transparency about our data practices.",
        "By using our Service, you agree to the collection and use of information in accordance with this policy."
      ]
    },
    {
      id: "collection",
      icon: Database,
      title: "Information We Collect",
      description: "Types of data we gather to provide our service",
      subsections: [
        {
          title: "Information You Provide Directly",
          points: [
            "Account Information: Email address and social login information (handled by Clerk)",
            "Payment Information: Subscription details (handled by Lemon Squeezy - we don't store full payment card details)",
            "Submitted Content: YouTube video URLs and resulting analysis data including transcripts, AI insights, and user-generated content"
          ]
        },
        {
          title: "Information We Collect Automatically",
          points: [
            "Usage Data: Pages visited, features used, time and date of visits, interactions with AI analysis tools",
            "Device Information: IP address, browser type, operating system, unique device identifiers",
            "Log Data: Information your browser sends when visiting our Service"
          ]
        },
        {
          title: "Information from Third-Party Services",
          points: [
            "YouTube Data: Publicly available metadata via YouTube API Services",
            "Authentication Data: Basic account information from Clerk",
            "Subscription Data: Plan details from Lemon Squeezy",
            "AI Processing Data: Content sent to Google Gemini API for analysis"
          ]
        }
      ]
    },
    {
      id: "usage",
      icon: Eye,
      title: "How We Use Your Information",
      description: "The purposes for which we process your data",
      content: [
        "To Provide and Maintain the Service: Operating and delivering all features of AdBreakdown",
        "To Personalize Your Experience: Tailoring the Service to your preferences and displaying relevant content",
        "To Improve and Develop the Service: Understanding user interactions, identifying trends, and developing new features",
        "For Billing and Account Management: Managing subscriptions, processing payments, tracking credit usage",
        "For Communication: Sending service-related announcements, updates, security alerts, and support messages",
        "For Security and Fraud Prevention: Protecting service integrity and detecting unauthorized activities",
        "For Legal Compliance: Complying with applicable laws, regulations, and legal processes"
      ]
    },
    {
      id: "sharing",
      icon: Share2,
      title: "How We Share Your Information",
      description: "When and with whom we share your data",
      content: [
        "With Service Providers: Third-party vendors for hosting, data storage, payment processing, authentication, and AI processing",
        "With Your Consent: When you give explicit consent, such as making an analysis public",
        "Publicly Shared Analyses: When you toggle an analysis to public, it becomes accessible on the /ad-library page and /daily-showcase page",
        "For Legal Reasons: When required by law or in response to valid requests by public authorities",
        "Business Transfers: In connection with mergers, acquisitions, or asset transfers",
        "Aggregated or Anonymized Data: Statistics and trends that cannot reasonably identify you"
      ]
    },
    {
      id: "retention",
      icon: Clock,
      title: "Data Retention",
      description: "How long we keep your information",
      content: [
        "We retain personal information and analysis data for as long as necessary to provide the Service, comply with legal obligations, resolve disputes, and enforce agreements.",
        "Analysis results for active users might be retained indefinitely unless a deletion request is made.",
        "Data associated with terminated accounts might be retained for 1-3 years for legal/audit purposes before anonymization or deletion.",
        "Specific retention periods depend on the type of data and its purpose."
      ]
    },
    {
      id: "rights",
      icon: Lock,
      title: "Your Data Rights",
      description: "Your rights regarding your personal information",
      content: [
        "Access: Request a copy of the personal information we hold about you",
        "Rectification: Request correction of any inaccurate or incomplete personal information",
        "Erasure: Request deletion of your personal information, subject to legal obligations",
        "Restriction of Processing: Request limitation of processing in certain circumstances",
        "Data Portability: Receive your personal information in a structured, machine-readable format",
        "Object to Processing: Object to processing of your personal information in certain circumstances",
        "Withdraw Consent: Withdraw consent for processing based on consent at any time"
      ]
    },
    {
      id: "security",
      icon: Shield,
      title: "Security of Your Information",
      description: "How we protect your data",
      content: [
        "We implement reasonable technical and organizational measures to protect your personal information from unauthorized access, use, alteration, or destruction.",
        "Our security measures include encryption, access controls, and regular security assessments.",
        "However, no method of transmission over the Internet or electronic storage is 100% secure.",
        "We cannot guarantee absolute security, but we continuously work to improve our security practices."
      ]
    },
    {
      id: "changes",
      icon: Globe,
      title: "Changes to This Policy",
      description: "How we handle policy updates",
      content: [
        "We may update this Privacy Policy from time to time to reflect changes in our practices or for legal, operational, or regulatory reasons.",
        "We will notify you of any changes by posting the new Privacy Policy on this page and updating the \"Last Updated\" date.",
        "You are advised to review this Privacy Policy periodically for any changes.",
        "Changes become effective when posted on this page."
      ]
    }
  ]
}

export default function PrivacyPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      <Navigation />
      
      <main className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex justify-center mb-6">
            <div className="p-4 bg-green-100 rounded-full">
              <Shield className="h-10 w-10 text-green-600" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            {privacyData.title}
          </h1>
          <p className="text-lg text-gray-600 mb-2">
            {privacyData.subtitle}
          </p>
          <div className="flex items-center justify-center text-sm text-gray-500">
            <Calendar className="h-4 w-4 mr-2" />
            <span>Last updated: {privacyData.lastUpdated}</span>
          </div>
        </div>

        {/* Privacy Sections Grid */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          {privacyData.sections.map((section) => {
            const IconComponent = section.icon
            return (
              <Card key={section.id} className="shadow-md hover:shadow-lg transition-shadow">
                <CardHeader className="pb-4">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="p-2 bg-green-50 rounded-lg">
                      <IconComponent className="h-5 w-5 text-green-600" />
                    </div>
                    <CardTitle className="text-xl">{section.title}</CardTitle>
                  </div>
                  <CardDescription className="text-gray-600">
                    {section.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {section.content && (
                    <div className="space-y-3">
                      {section.content.map((item, index) => (
                        <p key={index} className="text-sm text-gray-700 leading-relaxed">
                          • {item}
                        </p>
                      ))}
                    </div>
                  )}
                  
                  {section.subsections && (
                    <div className="space-y-6">
                      {section.subsections.map((subsection, index) => (
                        <div key={index} className="border-l-4 border-green-200 pl-4">
                          <h4 className="font-semibold text-gray-900 mb-2">{subsection.title}</h4>
                          <div className="space-y-2">
                            {subsection.points.map((point, pointIndex) => (
                              <p key={pointIndex} className="text-sm text-gray-700 leading-relaxed">
                                • {point}
                              </p>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Contact and Rights Section */}
        <Card className="shadow-lg mb-12">
          <CardHeader>
            <CardTitle className="text-2xl text-center text-green-700">Exercise Your Rights</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="text-center">
              <p className="text-gray-600 mb-6">
                To exercise any of your data rights or if you have questions about this Privacy Policy, please contact us:
              </p>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="bg-green-50 p-6 rounded-lg border border-green-200">
                  <h3 className="font-semibold text-green-800 mb-3">Email Support</h3>
                  <p className="text-sm text-green-700">
                    <EMAIL>
                  </p>
                  <p className="text-xs text-green-600 mt-2">
                    We will respond to your request in accordance with applicable law
                  </p>
                </div>
                <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                  <h3 className="font-semibold text-blue-800 mb-3">Data Deletion</h3>
                  <p className="text-sm text-blue-700">
                    You can delete your account and data at any time through your dashboard settings
                  </p>
                  <p className="text-xs text-blue-600 mt-2">
                    Some data may be retained for legal compliance
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Third-Party Services Notice */}
        <Card className="shadow-lg mb-12 border-yellow-200">
          <CardHeader>
            <CardTitle className="text-xl text-center text-yellow-700">Third-Party Services</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-yellow-50 p-6 rounded-lg">
              <p className="text-sm text-yellow-800 mb-4">
                Our Service integrates with the following third-party services, each with their own privacy policies:
              </p>
              <div className="grid sm:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-semibold text-yellow-900 mb-2">Authentication & Payments</h4>
                  <ul className="space-y-1 text-yellow-700">
                    <li>• Clerk (Authentication)</li>
                    <li>• Lemon Squeezy (Payments)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-yellow-900 mb-2">Data Processing</h4>
                  <ul className="space-y-1 text-yellow-700">
                    <li>• Google Gemini API (AI Analysis)</li>
                    <li>• YouTube API Services</li>
                  </ul>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer Actions */}
        <div className="text-center">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/terms">
              <Button variant="outline" size="lg">
                Terms of Service
              </Button>
            </Link>
            <Link href="/contact">
              <Button variant="outline" size="lg">
                Contact Us
              </Button>
            </Link>
            <Link href="/">
              <Button size="lg" className="bg-green-600 hover:bg-green-700">
                Back to Home
              </Button>
            </Link>
          </div>

        </div>
      </main>
    </div>
  )
}