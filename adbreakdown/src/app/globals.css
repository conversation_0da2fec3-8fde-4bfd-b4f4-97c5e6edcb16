@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    font-family: var(--font-inter); /* Default font */ --chart-1: 12 76% 61%; --chart-2: 173 58% 39%; --chart-3: 197 37% 24%; --chart-4: 43 74% 66%; --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.font-caprasimo {
  font-family: var(--font-caprasimo);
}

/* Enhanced Script Container Styling */
@layer components {
  .enhanced-script-content {
    @apply max-w-none;
    line-height: 1.7;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
  
  .enhanced-script-content h1 {
    @apply scroll-mt-20;
  }
  
  .enhanced-script-content h2 {
    @apply scroll-mt-20;
  }
  
  .enhanced-script-content h3 {
    @apply scroll-mt-20;
  }
  
  .enhanced-script-content h4 {
    @apply scroll-mt-20;
  }
  
  .enhanced-script-content p {
    @apply text-justify;
  }
  
  .enhanced-script-content ul li {
    @apply text-justify;
  }
  
  .enhanced-script-content ol li {
    @apply text-justify;
  }
  
  /* Better spacing for nested lists */
  .enhanced-script-content li ul,
  .enhanced-script-content li ol {
    @apply mt-2 mb-2;
  }
  
  /* Improved code block styling */
  .enhanced-script-content pre {
    overflow-x: auto;
  }
  
  /* Better table styling */
  .enhanced-script-content table {
    @apply shadow-sm;
  }
  
  /* Improved blockquote styling */
  .enhanced-script-content blockquote {
    @apply shadow-sm;
  }

  .generated-content {
    @apply text-sm font-normal leading-relaxed tracking-normal;
  }

  .generated-content table {
    @apply w-full border-collapse shadow-sm;
  }

  .generated-content .table-container {
    @apply rounded-lg overflow-hidden;
  }

  .generated-content th,
  .generated-content td {
    @apply border border-gray-200 p-3 text-left;
  }

  .generated-content th {
    @apply bg-gray-100 font-semibold p-4 whitespace-nowrap;
  }

  .generated-content tbody tr:nth-child(even) {
    @apply bg-gray-50;
  }

  /* Custom emoji and icon styling */
  .enhanced-script-content h1 .emoji,
  .enhanced-script-content h2 .emoji,
  .enhanced-script-content h3 .emoji,
  .enhanced-script-content h4 .emoji {
    @apply inline-block mr-2;
  }
}