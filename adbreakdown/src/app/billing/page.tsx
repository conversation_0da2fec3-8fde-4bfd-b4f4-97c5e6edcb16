'use client'

import React, { useState, useEffect, Suspense } from 'react'
import { useSearchParams } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Check, Crown, Zap, Rocket, ArrowLeft } from 'lucide-react'
import Link from 'next/link'
import PageHeader from '@/components/PageHeader'

interface UserProfile {
  subscription_status: string | null
  subscription_plan: string | null
  subscription_period_end: string | null
  credits_remaining: number
}

interface PricingTier {
  id: string
  name: string
  price: number
  credits: number
  features: string[]
  popular?: boolean
  icon: React.ReactNode
  description: string
  lemonSqueezyVariantId: string
}

// Component that uses useSearchParams - needs to be wrapped in Suspense
function SearchParamsHandler({ setSuccessMessage }: { setSuccessMessage: (message: string) => void }) {
  const searchParams = useSearchParams()

  useEffect(() => {
    if (searchParams && searchParams.get('success') === 'true') {
      setSuccessMessage('🎉 Subscription activated successfully! Your credits have been updated.')
      // Clear the success parameter from URL
      window.history.replaceState({}, '', '/billing')
    }
  }, [searchParams, setSuccessMessage])

  return null
}

const PRICING_TIERS: PricingTier[] = [
  {
    id: 'starter',
    name: 'Starter',
    price: 9.99,
    credits: 50,
    features: [
      '50 analysis credits per month',
      'Basic AI analysis reports',
      'Marketing copy generation',
      'Social media post suggestions',
      'Email support'
    ],
    icon: <Zap className="h-6 w-6" />,
    description: 'Perfect for small businesses and content creators',
    lemonSqueezyVariantId: process.env.NEXT_PUBLIC_LEMON_SQUEEZY_STARTER_VARIANT_ID || ''
  },
  {
    id: 'pro',
    name: 'Pro',
    price: 19.99,
    credits: 150,
    features: [
      '150 analysis credits per month',
      'Advanced AI analysis reports',
      'All LLM-powered features',
      'Marketing scorecard & SEO keywords',
      'Content improvement suggestions',
      'Priority support',
      'API access'
    ],
    popular: true,
    icon: <Crown className="h-6 w-6" />,
    description: 'Ideal for marketing teams and agencies',
    lemonSqueezyVariantId: process.env.NEXT_PUBLIC_LEMON_SQUEEZY_PRO_VARIANT_ID || ''
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 49.99,
    credits: 500,
    features: [
      '500 analysis credits per month',
      'Premium AI analysis reports',
      'All Pro features included',
      'Custom analysis workflows',
      'Team management & collaboration',
      'White-label options',
      'Dedicated account manager',
      'Custom integrations'
    ],
    icon: <Rocket className="h-6 w-6" />,
    description: 'Built for large organizations and enterprises',
    lemonSqueezyVariantId: process.env.NEXT_PUBLIC_LEMON_SQUEEZY_ENTERPRISE_VARIANT_ID || ''
  }
]

export default function BillingPage() {
  const { user, isAuthenticated, loading: authLoading } = useAuth()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [processingCheckout, setProcessingCheckout] = useState<string | null>(null)
  const [successMessage, setSuccessMessage] = useState('')

  useEffect(() => {
    const fetchProfile = async () => {
      if (!isAuthenticated || !user) return

      try {
        setLoading(true)
        const response = await fetch(`/api/users/profile`)
        if (response.ok) {
          const data = await response.json()
          setProfile(data.profile)
        } else {
          setError('Failed to load profile information')
        }
      } catch (err) {
        setError('Error loading profile')
        console.error('Error fetching profile:', err)
      } finally {
        setLoading(false)
      }
    }

    if (isAuthenticated && !authLoading) {
      fetchProfile()
    }
  }, [isAuthenticated, authLoading, user])

  const handleSubscribe = async (tier: PricingTier) => {
    if (!user) return

    try {
      setProcessingCheckout(tier.id)
      setError('')

      // Create checkout session with Lemon Squeezy
      const response = await fetch('/api/billing/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          variant_id: tier.lemonSqueezyVariantId,
          user_email: user.primaryEmailAddress?.emailAddress,
          user_name: user.fullName,
          custom_data: {
            user_id: user.id,
            plan: tier.id
          }
        })
      })

      const data = await response.json()

      if (response.ok && data.checkout_url) {
        // Redirect to Lemon Squeezy checkout
        window.location.href = data.checkout_url
      } else {
        setError(data.error || 'Failed to create checkout session')
      }
    } catch (err) {
      setError('Error creating checkout session')
      console.error('Checkout error:', err)
    } finally {
      setProcessingCheckout(null)
    }
  }

  const getStatusBadge = (status: string | null) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'cancelled':
        return <Badge className="bg-yellow-100 text-yellow-800">Cancelled</Badge>
      case 'expired':
        return <Badge className="bg-red-100 text-red-800">Expired</Badge>
      case 'paused':
        return <Badge className="bg-gray-100 text-gray-800">Paused</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">Free</Badge>
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading billing information...</p>
        </div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="mb-4">Please sign in to access billing.</p>
          <Link href="/sign-in">
            <Button>Sign In</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Search params handler wrapped in Suspense */}
      <Suspense fallback={null}>
        <SearchParamsHandler setSuccessMessage={setSuccessMessage} />
      </Suspense>
      
      {/* Header */}
      <PageHeader 
        title="Billing & Subscriptions"
        subtitle="Manage your plan and credits"
        showBackButton={true}
        backButtonText="Back to Dashboard"
        backButtonHref="/dashboard"
      />

      <main className="container mx-auto px-4 py-8 max-w-6xl">
        {successMessage && (
          <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded mb-6">
            {successMessage}
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {/* Current Subscription Status */}
        {profile && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Current Subscription</CardTitle>
              <CardDescription>Your current plan and usage information</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <div className="text-sm font-medium text-gray-600 mb-1">Status</div>
                  {getStatusBadge(profile.subscription_status)}
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-600 mb-1">Plan</div>
                  <div className="font-semibold capitalize">
                    {profile.subscription_plan || 'Free'}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-600 mb-1">Credits Remaining</div>
                  <div className="text-2xl font-bold text-blue-600">
                    {profile.credits_remaining}
                  </div>
                </div>
              </div>
              {profile.subscription_period_end && (
                <div className="mt-4 pt-4 border-t">
                  <div className="text-sm text-gray-600">
                    {profile.subscription_status === 'cancelled' ? 'Expires' : 'Renews'} on{' '}
                    <span className="font-medium">
                      {formatDate(profile.subscription_period_end)}
                    </span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Pricing Tiers */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Choose Your Plan
          </h1>
          <p className="text-gray-600 max-w-2xl mx-auto">
            Unlock the full power of AI-driven video ad analysis with our flexible pricing plans.
            All plans include our core analysis features with varying credit allowances.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-6 mb-8">
          {PRICING_TIERS.map((tier) => (
            <Card
              key={tier.id}
              className={`relative ${
                tier.popular ? 'border-blue-500 shadow-lg scale-105' : 'border-gray-200'
              }`}
            >
              {tier.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-blue-600 text-white">Most Popular</Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-4">
                <div className="flex justify-center mb-3 text-blue-600">
                  {tier.icon}
                </div>
                <CardTitle className="text-xl">{tier.name}</CardTitle>
                <CardDescription className="text-sm">
                  {tier.description}
                </CardDescription>
                <div className="mt-4">
                  <div className="text-3xl font-bold text-gray-900">
                    ${tier.price}
                    <span className="text-lg font-normal text-gray-600">/month</span>
                  </div>
                  <div className="text-sm text-gray-600 mt-1">
                    {tier.credits} credits included
                  </div>
                </div>
              </CardHeader>

              <CardContent className="pt-0">
                <ul className="space-y-3 mb-6">
                  {tier.features.map((feature, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <Check className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button
                  onClick={() => handleSubscribe(tier)}
                  disabled={
                    processingCheckout === tier.id ||
                    (profile?.subscription_plan === tier.id && profile?.subscription_status === 'active')
                  }
                  className={`w-full ${
                    tier.popular
                      ? 'bg-blue-600 hover:bg-blue-700'
                      : 'bg-gray-900 hover:bg-gray-800'
                  }`}
                >
                  {processingCheckout === tier.id ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin h-4 w-4 border-b-2 border-white rounded-full"></div>
                      Processing...
                    </div>
                  ) : profile?.subscription_plan === tier.id && profile?.subscription_status === 'active' ? (
                    'Current Plan'
                  ) : (
                    `Subscribe to ${tier.name}`
                  )}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* FAQ Section */}
        <Card>
          <CardHeader>
            <CardTitle>Frequently Asked Questions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <h4 className="font-medium mb-2">How do credits work?</h4>
                <p className="text-sm text-gray-600">
                  Each AI-powered feature (marketing copy, social posts, scorecard, etc.) costs 1 credit.
                  Your credit allowance resets monthly on your billing date.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Can I change plans anytime?</h4>
                <p className="text-sm text-gray-600">
                  Yes! You can upgrade or downgrade your plan at any time. Changes take effect at your next billing cycle.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">What happens if I run out of credits?</h4>
                <p className="text-sm text-gray-600">
                  You can still perform basic video analysis, but LLM-powered features will be unavailable until your credits reset or you upgrade your plan.
                </p>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Is there a free trial?</h4>
                <p className="text-sm text-gray-600">
                  New users get 5 free credits to try our platform. No credit card required to get started.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  )
}