import Navigation from '@/components/Navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import { Clock, ArrowLeft } from 'lucide-react'

export default function CookiesPage() {
  return (
    <div className="min-h-screen bg-white">
      <Navigation />

      <main className="container mx-auto px-4 py-16 max-w-2xl">
        <div className="text-center">
          <Card className="p-12">
            <CardContent className="space-y-6">
              <div className="w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto">
                <Clock className="h-10 w-10 text-orange-600" />
              </div>
              
              <div className="space-y-4">
                <h1 className="text-3xl font-bold text-gray-900">
                  Cookie Policy Coming Soon
                </h1>
                <p className="text-lg text-gray-600">
                  We&apos;re preparing our comprehensive cookie policy to ensure transparency about how we use cookies and similar technologies.
                </p>
                <p className="text-sm text-gray-500">
                  In the meantime, please refer to our privacy policy for information about data collection and usage.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
                <Link href="/privacy">
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    View Privacy Policy
                  </Button>
                </Link>
                <Link href="/ad-library">
                  <Button variant="outline">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    View Ad Library
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  )
}