import React from 'react'
import { createClient } from '@supabase/supabase-js'
import SharedAnalysisPage from '@/components/analysis/SharedAnalysisPage'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import Navigation from '@/components/Navigation'

// Use service role key for public data access
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

interface DailyShowcaseData {
  id: string
  analysis_id: string
  featured_date: string
  excerpt: string
  key_insights: string[]
  created_at: string
  updated_at: string
  analysis: any
}

async function getFeaturedAnalysis(): Promise<DailyShowcaseData | null> {
  try {
    const today = new Date().toISOString().split('T')[0]
    
    // Get all showcases and filter for today or most recent
    const { data: allShowcases, error: showcaseError } = await supabase
      .from('daily_showcases')
      .select(`
        id,
        analysis_id,
        featured_date,
        excerpt,
        key_insights,
        created_at
      `)
      .order('featured_date', { ascending: false })
    
    if (showcaseError || !allShowcases || allShowcases.length === 0) {
      return null
    }

    let currentShowcase = allShowcases.find(s => s.featured_date === today) || allShowcases[0]
    
    // Fetch the analysis data
    const { data: analysis, error: analysisError } = await supabase
      .from('ad_analyses')
      .select(`
        *,
        reports:analysis_reports(
          id,
          report_types(*),
          content,
          status,
          created_at
        )
      `)
      .eq('id', currentShowcase.analysis_id)
      .eq('is_public', true)
      .eq('showcase', true)
      .single()
    
    if (analysisError || !analysis) {
      return null
    }

    return {
      ...currentShowcase,
      updated_at: currentShowcase.created_at, // Use created_at as fallback for updated_at
      analysis
    }
  } catch (error) {
    console.error('Error fetching featured analysis:', error)
    return null
  }
}

export default async function FeaturedPageServer() {
  const showcaseData = await getFeaturedAnalysis()

  if (!showcaseData) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navigation />
        <div className="container mx-auto px-4 py-8 max-w-6xl">
          <Card className="text-center">
            <CardHeader>
              <CardTitle>Featured Analysis Not Available</CardTitle>
              <CardDescription>
                No featured analysis is currently available. Please check back later.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                We update our featured analysis daily with the most interesting video ad breakdowns.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Use the shared component with preloaded data and featured mode enabled
  return (
    <SharedAnalysisPage
      analysisId={showcaseData.analysis_id}
      isFeaturedMode={true}
      preloadedAnalysis={showcaseData.analysis}
    />
  )
}