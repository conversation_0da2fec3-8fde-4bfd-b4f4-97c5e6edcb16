import { generateMetadata as generateSEOMetadata } from '@/lib/seo'

export const metadata = generateSEOMetadata({
  title: 'Ad Analysis Frameworks - Breakdown.Ad',
  description: 'Learn the most powerful frameworks in advertising: AIDA, ADPLAN, PAS, and more. Structure your creative and strategic ad analysis with proven methodologies.',
  keywords: [
    'advertising frameworks',
    'ad analysis',
    'marketing frameworks',
    'AIDA framework',
    'PAS copywriting',
    'advertising strategy',
    'creative analysis',
    'marketing methodology'
  ],
  canonicalUrl: '/frameworks'
})

export default function FrameworksLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}