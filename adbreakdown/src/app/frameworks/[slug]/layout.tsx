import { generateMetadata as generateSEOMetadata } from '@/lib/seo'
import { getFrameworkBySlug } from '@/lib/frameworks'
import { notFound } from 'next/navigation'

interface Props {
  params: Promise<{ slug: string }>
}

export async function generateMetadata({ params }: Props) {
  const { slug } = await params
  const framework = getFrameworkBySlug(slug)
  
  if (!framework) {
    return generateSEOMetadata({
      title: 'Framework Not Found',
      description: 'The requested advertising framework could not be found.',
      noIndex: true
    })
  }

  return generateSEOMetadata({
    title: `${framework.name} Framework - Ad Analysis Guide`,
    description: `Learn how to apply the ${framework.name} framework to your ad analysis. ${framework.summary}. Includes examples, use cases, and comparisons.`,
    keywords: [
      `${framework.name} framework`,
      'ad analysis',
      framework.category,
      ...framework.tags,
      'advertising strategy',
      'marketing framework'
    ],
    canonicalUrl: `/frameworks/${framework.slug}`,
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": `${framework.name} Framework - Ad Analysis Guide`,
      "description": framework.description,
      "author": {
        "@type": "Organization",
        "name": "Breakdown.Ad"
      },
      "publisher": {
        "@type": "Organization",
        "name": "Breakdown.Ad",
        "url": "https://breakdown.ad"
      },
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": `https://breakdown.ad/frameworks/${framework.slug}`
      },
      "datePublished": "2024-01-01",
      "dateModified": "2024-01-01",
      "articleSection": "Advertising Frameworks",
      "keywords": framework.tags.join(", "),
      "about": {
        "@type": "Thing",
        "name": `${framework.name} Framework`,
        "description": framework.summary
      }
    }
  })
}

export default function FrameworkDetailLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}