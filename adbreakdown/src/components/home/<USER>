'use client'

import { useEffect, useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Play, TrendingUp, Eye, Clock, X } from 'lucide-react'

interface Analysis {
  id: string
  slug: string
  title: string
  inferred_brand: string
  video_thumbnail_url: string
  thumbnail_url: string
  video_url: string
  duration_seconds: number
  overall_sentiment: number
  view_count: number
  created_at: string
}

export default function AnalysisOfTheDay() {
  const [analysis, setAnalysis] = useState<Analysis | null>(null)
  const [loading, setLoading] = useState(true)
  const [showVideoPlayer, setShowVideoPlayer] = useState(false)
  const [youtubeVideoId, setYoutubeVideoId] = useState<string | null>(null)

  useEffect(() => {
    fetchFeaturedAnalysis()
  }, [])

  const fetchFeaturedAnalysis = async () => {
    try {
      const response = await fetch('/api/featured/simple')
      
      if (response.ok) {
        const data = await response.json()
        if (data.current && data.current.analysis) {
          setAnalysis(data.current.analysis)
        } else {
          // Try to fetch a random public analysis as fallback
          const fallbackResponse = await fetch('/api/analyses/public?limit=1')
          if (fallbackResponse.ok) {
            const fallbackData = await fallbackResponse.json()
            if (fallbackData.analyses && fallbackData.analyses.length > 0) {
              setAnalysis(fallbackData.analyses[0])
            }
          }
        }
      }
    } catch (error) {
      console.error('Error fetching featured analysis:', error)
    } finally {
      setLoading(false)
    }
  }

  const extractYouTubeVideoId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  const getYouTubeThumbnail = (videoId: string, quality: 'default' | 'hqdefault' | 'mqdefault' | 'sddefault' | 'maxresdefault' = 'hqdefault'): string => {
    return `https://img.youtube.com/vi/${videoId}/${quality}.jpg`
  }

  const getThumbnailUrl = (analysis: Analysis): string => {
    if (analysis.thumbnail_url && analysis.thumbnail_url.trim() !== '') {
      return analysis.thumbnail_url
    }
    
    if (analysis.video_thumbnail_url && analysis.video_thumbnail_url.trim() !== '') {
      return analysis.video_thumbnail_url
    }
    
    if (analysis.video_url && analysis.video_url.trim() !== '') {
      const videoId = extractYouTubeVideoId(analysis.video_url)
      if (videoId) {
        return getYouTubeThumbnail(videoId, 'hqdefault')
      }
    }
    
    return ''
  }

  const handleThumbnailClick = () => {
    if (analysis?.video_url) {
      const videoId = extractYouTubeVideoId(analysis.video_url)
      if (videoId) {
        setYoutubeVideoId(videoId)
        setShowVideoPlayer(true)
      }
    }
  }

  const getSentimentPercentage = (sentiment: number) => {
    return Math.round(sentiment * 100)
  }

  const formatViewCount = (viewCount: number | undefined) => {
    if (!viewCount) return '0'
    if (viewCount >= 1000000) {
      return `${(viewCount / 1000000).toFixed(1)}M`
    }
    if (viewCount >= 1000) {
      return `${(viewCount / 1000).toFixed(1)}K`
    }
    return viewCount.toLocaleString()
  }
  if (loading) {
    return (
      <div className="mb-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Featured Analysis</h2>
          <p className="text-gray-600 text-lg">Discover today&apos;s featured ad analysis</p>
        </div>

        <div className="bg-gray-50 rounded-3xl p-12">
          <Card className="max-w-5xl mx-auto bg-white border-0 rounded-2xl overflow-hidden shadow-lg">
            <div className="md:flex">
              <div className="md:w-2/5 bg-gradient-to-br from-gray-100 to-gray-200 animate-pulse" />
              <div className="md:w-3/5 p-8">
                <div className="space-y-4">
                  <div className="flex gap-2">
                    <div className="h-6 w-20 bg-gray-200 animate-pulse rounded-full" />
                    <div className="h-6 w-16 bg-gray-200 animate-pulse rounded-full" />
                  </div>
                  <div className="h-8 bg-gray-200 animate-pulse rounded" />
                  <div className="h-4 bg-gray-200 animate-pulse rounded w-3/4" />
                  <div className="h-4 bg-gray-200 animate-pulse rounded w-2/3" />
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    )
  }

  if (!analysis) {
    return (
      <div className="mb-20">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Featured Analysis</h2>
          <p className="text-gray-600 text-lg">Discover today&apos;s featured ad analysis</p>
        </div>

        <div className="bg-gray-50 rounded-3xl p-12">
          <Card className="max-w-5xl mx-auto bg-white border-0 rounded-2xl overflow-hidden shadow-lg">
            <CardContent className="p-12 text-center">
              <div className="text-6xl mb-4">🎬</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                No Featured Analysis Today
              </h3>
              <p className="text-gray-600 mb-6">
                Explore our ad library to discover amazing analyses from our community
              </p>
              <Link href="/ad-library">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  Browse Ad Library
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="mb-20">
      <div className="text-center mb-12">
        <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Featured Analysis</h2>
        <p className="text-gray-600 text-lg">Discover today&apos;s featured ad analysis</p>
      </div>

      <div className="bg-gray-50 rounded-3xl p-12">
        <Card className="max-w-5xl mx-auto bg-white border-0 rounded-2xl overflow-hidden shadow-lg">
          <div className="md:flex">
            <div className="md:w-2/5 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center overflow-hidden relative">
              {showVideoPlayer && youtubeVideoId ? (
                <div className="aspect-video w-full h-full">
                  <iframe
                    src={`https://www.youtube.com/embed/${youtubeVideoId}?autoplay=1&rel=0`}
                    title="YouTube video player"
                    frameBorder="0"
                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                    allowFullScreen
                    className="w-full h-full"
                  />
                </div>
              ) : (
                <div className="relative w-full h-full flex items-center justify-center cursor-pointer group" onClick={handleThumbnailClick}>
                  {(() => {
                    const thumbnailUrl = getThumbnailUrl(analysis)
                    
                    if (thumbnailUrl.includes('placeholder')) {
                      return (
                        <div className="relative">
                          <div className="w-24 h-24 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl">
                            <Play className="w-12 h-12 text-white ml-1" />
                          </div>
                          
                        </div>
                      )
                    }
                    
                    return (
                      <div className="relative w-full h-full">
                        <Image
                          src={thumbnailUrl}
                          alt={analysis.title || 'Video thumbnail'}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            if (analysis.video_url && analysis.video_url.trim() !== '') {
                              const videoId = extractYouTubeVideoId(analysis.video_url)
                              if (videoId) {
                                if (target.src.includes('hqdefault')) {
                                  target.src = getYouTubeThumbnail(videoId, 'mqdefault')
                                  return
                                } else if (target.src.includes('mqdefault')) {
                                  target.src = getYouTubeThumbnail(videoId, 'default')
                                  return
                                }
                              }
                            }
                            target.src = 'https://via.placeholder.com/400x225/e5e7eb/6b7280?text=Video+Thumbnail'
                          }}
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                          <div className="bg-white bg-opacity-90 rounded-full p-3 group-hover:scale-110 transition-transform duration-300">
                            <Play className="h-6 w-6 text-blue-600" />
                          </div>
                        </div>

                      </div>
                    )
                  })()}
                </div>
              )}
            </div>
            <div className="md:w-3/5 p-8">
              <CardHeader className="p-0 mb-6">
                <div className="flex items-center gap-2 mb-3">
                  <Badge className="bg-green-100 text-green-700 hover:bg-green-100">High Performance</Badge>
                  <Badge className="bg-blue-100 text-blue-700 hover:bg-blue-100">Trending</Badge>
                  <Badge className="bg-purple-100 text-purple-700 hover:bg-purple-100">Featured</Badge>
                </div>
                <CardTitle className="text-2xl font-bold text-gray-900 mb-2">
                  {analysis.title}
                </CardTitle>
                <CardDescription className="text-gray-600 text-lg">
                  Deep dive into {analysis.inferred_brand}&apos;s creative strategy and discover what makes this campaign stand out from the competition
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <div className="grid grid-cols-2 gap-6 mb-6">


                </div>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Clock className="w-4 h-4" />
                    <span>{analysis.inferred_brand} • Campaign Analysis</span>
                  </div>
                  <Link href={`/ad/${analysis.slug || analysis.id}`}>
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white">View Analysis</Button>
                  </Link>
                </div>
              </CardContent>
            </div>
          </div>
        </Card>
      </div>

      
    </div>
  )
}