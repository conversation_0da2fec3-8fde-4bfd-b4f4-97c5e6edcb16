
'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Eye, ThumbsUp, MessageSquare, Calendar, Hash, Globe } from 'lucide-react'

interface CampaignAnalysisProps {
  youtubeMetadata: any
  videoMetadata: any
}

const CampaignAnalysis: React.FC<CampaignAnalysisProps> = ({ youtubeMetadata, videoMetadata }) => {
  if (!youtubeMetadata) return null

  return (
    <Card className="flex flex-col h-full w-full overflow-hidden">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-base mb-1 flex items-center gap-2">
              <Globe className="h-4 w-4 text-red-600" />
              {youtubeMetadata.channelTitle}
            </CardTitle>
            <CardDescription className="text-xs text-gray-600">
              Published {new Date(youtubeMetadata.publishedAt).toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'short', 
                day: 'numeric' 
              })}
            </CardDescription>
          </div>
          {videoMetadata.inferredBrandName && videoMetadata.inferredBrandName !== 'N/A' && (
            <Badge variant="outline" className="ml-2 text-xs">
              Brand: {videoMetadata.inferredBrandName}
            </Badge>
          )}
        </div>
      </CardHeader>
      <CardContent className="flex-grow overflow-y-auto p-4 pt-0 space-y-3 min-h-0">
        {/* Video Stats */}
        <div className="grid grid-cols-2 gap-2">
          <div className="text-center p-2 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Eye className="h-3 w-3 text-gray-600" />
              <span className="text-xs font-medium text-gray-600">Views</span>
            </div>
            <div className="text-sm font-bold text-gray-900">
              {parseInt(youtubeMetadata.viewCount).toLocaleString()}
            </div>
          </div>
          <div className="text-center p-2 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <ThumbsUp className="h-3 w-3 text-gray-600" />
              <span className="text-xs font-medium text-gray-600">Likes</span>
            </div>
            <div className="text-sm font-bold text-gray-900">
              {parseInt(youtubeMetadata.likeCount).toLocaleString()}
            </div>
          </div>
          <div className="text-center p-2 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <MessageSquare className="h-3 w-3 text-gray-600" />
              <span className="text-xs font-medium text-gray-600">Comments</span>
            </div>
            <div className="text-sm font-bold text-gray-900">
              {parseInt(youtubeMetadata.commentCount).toLocaleString()}
            </div>
          </div>
          <div className="text-center p-2 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Calendar className="h-3 w-3 text-gray-600" />
              <span className="text-xs font-medium text-gray-600">Duration</span>
            </div>
            <div className="text-sm font-bold text-gray-900">
              {youtubeMetadata?.duration || videoMetadata.duration}
            </div>
          </div>
        </div>

        {/* Video Description */}
        {youtubeMetadata.description && (
          <div>
            <h4 className="font-medium mb-2 text-gray-900 text-sm">Video Description</h4>
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="text-xs text-gray-700 leading-relaxed whitespace-pre-wrap">
                {youtubeMetadata.description.length > 200 
                  ? `${youtubeMetadata.description.substring(0, 200)}...` 
                  : youtubeMetadata.description
                }
              </p>
            </div>
          </div>
        )}

        {/* Tags */}
        {youtubeMetadata.tags && youtubeMetadata.tags.length > 0 && (
          <div>
            <h4 className="font-medium mb-2 text-gray-900 flex items-center gap-2 text-sm">
              <Hash className="h-3 w-3" />
              Video Tags
            </h4>
            <div className="flex flex-wrap gap-1">
              {youtubeMetadata.tags.slice(0, 6).map((tag: string, index: number) => (
                <Badge key={index} variant="secondary" className="text-xs px-2 py-0.5">
                  {tag.length > 12 ? `${tag.substring(0, 12)}...` : tag}
                </Badge>
              ))}
              {youtubeMetadata.tags.length > 6 && (
                <Badge variant="outline" className="text-xs px-2 py-0.5">
                  +{youtubeMetadata.tags.length - 6} more
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default CampaignAnalysis
