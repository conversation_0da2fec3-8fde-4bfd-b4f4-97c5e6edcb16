'use client'

import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ChevronDown, ChevronUp } from 'lucide-react'
import { LucideIcon } from 'lucide-react'

interface AnalysisInsightCardProps {
  title: string
  content: string
  icon: LucideIcon
  variant: 'primary' | 'insight' | 'success' | 'warning'
  className?: string
}

export default function AnalysisInsightCard({ 
  title, 
  content, 
  icon: Icon, 
  variant,
  className = '' 
}: AnalysisInsightCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  
  const variants = {
    primary: {
      background: 'bg-gradient-to-r from-purple-50 to-blue-50',
      border: 'border-l-4 border-purple-500',
      titleColor: 'text-purple-900',
      iconColor: 'text-purple-600',
      contentColor: 'text-purple-800',
      buttonColor: 'text-purple-600 hover:text-purple-800'
    },
    insight: {
      background: 'bg-gradient-to-r from-blue-50 to-green-50',
      border: 'border-l-4 border-blue-500',
      titleColor: 'text-blue-900',
      iconColor: 'text-blue-600',
      contentColor: 'text-blue-800',
      buttonColor: 'text-blue-600 hover:text-blue-800'
    },
    success: {
      background: 'bg-gradient-to-r from-green-50 to-emerald-50',
      border: 'border-l-4 border-green-500',
      titleColor: 'text-green-900',
      iconColor: 'text-green-600',
      contentColor: 'text-green-800',
      buttonColor: 'text-green-600 hover:text-green-800'
    },
    warning: {
      background: 'bg-gradient-to-r from-red-50 to-orange-50',
      border: 'border-l-4 border-red-500',
      titleColor: 'text-red-900',
      iconColor: 'text-red-600',
      contentColor: 'text-red-800',
      buttonColor: 'text-red-600 hover:text-red-800'
    }
  }

  const currentVariant = variants[variant]
  const shouldShowToggle = content.length > 200 // Show toggle for longer content

  return (
    <Card className={`${currentVariant.background} ${currentVariant.border} shadow-sm hover:shadow-md transition-all duration-300 ${isExpanded ? 'h-auto' : 'h-64'} ${className}`}>
      <CardContent className="p-6 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center space-x-3 mb-4">
          <Icon className={`w-5 h-5 ${currentVariant.iconColor}`} />
          <h3 className={`font-semibold text-lg ${currentVariant.titleColor}`}>
            {title}
          </h3>
        </div>
        
        {/* Content */}
        <div className={`flex-1 pl-8 flex flex-col ${isExpanded ? 'min-h-0' : ''}`}>
          <div className={`${isExpanded ? 'flex-1' : 'flex-1 overflow-hidden'}`}>
            <p 
              className={`${currentVariant.contentColor} leading-relaxed transition-all duration-300 ${
                !isExpanded && shouldShowToggle 
                  ? 'overflow-hidden' 
                  : ''
              }`}
              style={
                !isExpanded && shouldShowToggle 
                  ? {
                      display: '-webkit-box',
                      WebkitLineClamp: 4,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden'
                    }
                  : {}
              }
            >
              {content}
            </p>
          </div>
          
          {/* Show More/Less Button */}
          {shouldShowToggle && (
            <div className="mt-3 pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className={`${currentVariant.buttonColor} h-auto p-0 text-sm font-medium`}
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="w-4 h-4 mr-1" />
                    Show Less
                  </>
                ) : (
                  <>
                    <ChevronDown className="w-4 h-4 mr-1" />
                    Show More
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}