
'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const ProcessingStatusCard = () => {
  const [currentStep, setCurrentStep] = useState(0)
  
  const processingSteps = [
    { title: "Analyzing YouTube ad", description: "Extracting video content and metadata" },
    { title: "Generating script summary", description: "Processing dialogue and narrative flow" },
    { title: "Analyzing themes & emotions", description: "Identifying key themes and emotional triggers" },
    { title: "Processing music & voice tone", description: "Analyzing audio elements and vocal characteristics" },
    { title: "Creating targeting recommendations", description: "Developing demographic and behavioral insights" },
    { title: "Writing expert review", description: "Generating comprehensive marketing analysis" },
    { title: "Finalizing analysis", description: "Preparing comprehensive report" }
  ]

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentStep((prev) => (prev + 1) % processingSteps.length)
    }, 3000) // Change step every 3 seconds

    return () => clearInterval(interval)
  }, [processingSteps.length])

  const currentStepData = processingSteps[currentStep]

  return (
    <Card className="mb-8 text-center">
      <CardHeader>
        <CardTitle className="flex items-center justify-center gap-2">
          <div className="animate-spin h-5 w-5 border-2 border-blue-600 border-t-transparent rounded-full"></div>
          Analysis in Progress
        </CardTitle>
        <CardDescription>The AI is analyzing your video ad. This page will update automatically.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <div className="text-lg font-medium text-blue-600">{currentStepData.title}</div>
          <div className="text-sm text-gray-600">{currentStepData.description}</div>
        </div>
        
        {/* Progress indicator */}
        <div className="space-y-2">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-1000 ease-out"
              style={{ 
                width: `${((currentStep + 1) / processingSteps.length) * 100}%` 
              }}
            ></div>
          </div>
          <div className="text-xs text-gray-500">
            Step {currentStep + 1} of {processingSteps.length} • Estimated time: 3-5 minutes
          </div>
        </div>

        {/* Steps indicator */}
        <div className="flex justify-center space-x-2">
          {processingSteps.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-colors duration-300 ${
                index <= currentStep ? 'bg-blue-600' : 'bg-gray-300'
              }`}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

export default ProcessingStatusCard
