'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  TrendingUp, Users, Target, Lightbulb
} from 'lucide-react'

interface TabsComponentProps {
  parsedData: any
}

export default function TabsComponent({ parsedData }: TabsComponentProps) {
  if (!parsedData) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Analysis data not available</p>
      </div>
    )
  }

  return (
    <Tabs defaultValue="audience-market" className="w-full">
      <TabsList className="grid w-full grid-cols-4">
        <TabsTrigger value="audience-market" className="flex items-center gap-2">
          <Users className="w-4 h-4" />
          Audience & Market
        </TabsTrigger>
        <TabsTrigger value="brand-strategy" className="flex items-center gap-2">
          <Target className="w-4 h-4" />
          Brand & Strategy
        </TabsTrigger>
        <TabsTrigger value="creative-culture" className="flex items-center gap-2">
          <Lightbulb className="w-4 h-4" />
          Creative & Culture
        </TabsTrigger>
        <TabsTrigger value="patterns-predictions" className="flex items-center gap-2">
          <TrendingUp className="w-4 h-4" />
          Patterns & Predictions
        </TabsTrigger>
      </TabsList>
      
      {/* Tab 1: Audience & Market */}
      <TabsContent value="audience-market" className="space-y-6">
        <div className="space-y-4">
          {/* Target Audience */}
          {parsedData.strategic_deep_dive?.brand_and_market_context?.target_audience_evidence && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Users className="w-4 h-4 mr-2" />
                  Target Audience
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.strategic_deep_dive.brand_and_market_context.target_audience_evidence}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Competitive Landscape */}
          {parsedData.strategic_deep_dive?.brand_and_market_context?.competitive_landscape_evidence && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Target className="w-4 h-4 mr-2" />
                  Competitive Landscape
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.strategic_deep_dive.brand_and_market_context.competitive_landscape_evidence}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      {/* Tab 2: Brand & Strategy */}
      <TabsContent value="brand-strategy" className="space-y-6">
        <div className="space-y-4">
          {/* Brand Identity */}
          {parsedData.strategic_deep_dive?.brand_strategy && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Target className="w-4 h-4 mr-2" />
                  Brand Identity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {parsedData.strategic_deep_dive.brand_strategy.brand_position && (
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                      <h4 className="text-xs md:text-sm font-medium text-gray-800 mb-2">Brand Position</h4>
                      <p className="text-gray-700 leading-relaxed">
                        {parsedData.strategic_deep_dive.brand_strategy.brand_position}
                      </p>
                    </div>
                  )}
                  {parsedData.strategic_deep_dive.brand_strategy.brand_archetype && (
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                      <h4 className="text-xs md:text-sm font-medium text-gray-800 mb-2">Brand Archetype</h4>
                      <p className="text-gray-700 leading-relaxed">
                        {parsedData.strategic_deep_dive.brand_strategy.brand_archetype}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* The Hypothetical Brief */}
          {parsedData.strategic_deep_dive?.brand_strategy?.hypothetical_brief && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Lightbulb className="w-4 h-4 mr-2" />
                  The Hypothetical Brief
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.strategic_deep_dive.brand_strategy.hypothetical_brief}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      {/* Tab 3: Creative & Culture */}
      <TabsContent value="creative-culture" className="space-y-6">
        <div className="space-y-4">
          {/* The Creative Game Plan */}
          {parsedData.strategic_deep_dive?.creative_and_cultural_context?.creative_game_plan && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Lightbulb className="w-4 h-4 mr-2" />
                  The Creative Game Plan
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.strategic_deep_dive.creative_and_cultural_context.creative_game_plan}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* The Cultural Hook */}
          {parsedData.strategic_deep_dive?.creative_and_cultural_context?.cultural_hook && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  The Cultural Hook
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.strategic_deep_dive.creative_and_cultural_context.cultural_hook}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>

      {/* Tab 4: Patterns & Predictions */}
      <TabsContent value="patterns-predictions" className="space-y-6">
        <div className="space-y-4">
          {/* Industry Patterns */}
          {parsedData.internal_signals?.pattern_recognition && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  Industry Patterns
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.internal_signals.pattern_recognition}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Performance Predictions */}
          {parsedData.internal_signals?.prediction_factors && (
            <Card className="border-gray-200">
              <CardHeader>
                <CardTitle className="flex items-center text-gray-900 text-sm md:text-base font-medium">
                  <Target className="w-4 h-4 mr-2" />
                  Performance Predictions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                  <p className="text-gray-700 leading-relaxed">
                    {parsedData.internal_signals.prediction_factors}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </TabsContent>
    </Tabs>
  )
}