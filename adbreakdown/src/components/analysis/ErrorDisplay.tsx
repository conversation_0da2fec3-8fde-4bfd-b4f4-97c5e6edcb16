'use client'

import React from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

interface ErrorDisplayProps {
  error: string
  onDismiss?: () => void
}

export default function ErrorDisplay({ error, onDismiss }: ErrorDisplayProps) {
  if (!error) return null

  return (
    <div className="bg-red-100 border border-red-400 text-red-700 p-4 rounded-lg mb-6">
      <div className="flex justify-between items-center">
        <span>{error}</span>
        <div className="flex items-center gap-2">
          {error.includes('Insufficient credits') && (
            <Link href="/billing">
              <Button size="sm">Upgrade Plan</Button>
            </Link>
          )}
          {onDismiss && (
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={onDismiss}
              className="text-red-700 hover:text-red-800"
            >
              ×
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}