'use client'

import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Play, ThumbsUp, ThumbsDown, Meh } from 'lucide-react'
import Image from 'next/image'
import YouTube from 'react-youtube'
import { VideoPlayerSkeleton } from '@/components/ui/skeleton'

interface VideoPlayerSectionProps {
  analysis: any
  youtubeVideoId: string | null
  loading?: boolean
}

export default function VideoPlayerSection({ 
  analysis, 
  youtubeVideoId, 
  loading = false 
}: VideoPlayerSectionProps) {
  const [showVideo, setShowVideo] = useState(false)

  const getSentimentIcon = (sentiment: number) => {
    if (sentiment >= 0.7) return <ThumbsUp className="h-5 w-5 text-green-600" />
    if (sentiment >= 0.4) return <Meh className="h-5 w-5 text-yellow-600" />
    return <ThumbsDown className="h-5 w-5 text-red-600" />
  }

  const getSentimentColor = (sentiment: number) => {
    if (sentiment >= 0.7) return "text-green-600"
    if (sentiment >= 0.4) return "text-yellow-600"
    return "text-red-600"
  }

  const youtubeOpts = {
    height: '100%',
    width: '100%',
    playerVars: {
      autoplay: 0,
      modestbranding: 1,
      rel: 0,
    },
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="p-0">
          <VideoPlayerSkeleton />
          <div className="p-6">
            <div className="h-6 bg-gray-200 rounded mb-2 animate-pulse"></div>
            <div className="flex gap-2">
              <div className="h-4 bg-gray-200 rounded w-24 animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded w-16 animate-pulse"></div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardContent className="p-0">
        <div className="relative bg-black rounded-t-lg overflow-hidden aspect-video">
          {showVideo && youtubeVideoId ? (
            <div className="absolute inset-0">
              <YouTube
                videoId={youtubeVideoId}
                opts={youtubeOpts}
                className="w-full h-full"
                iframeClassName="w-full h-full rounded-t-lg"
              />
            </div>
          ) : (
            <>
              <Image 
                src={analysis.thumbnail_url || `https://i.ytimg.com/vi/${youtubeVideoId}/hqdefault.jpg`}
                alt={analysis.title || 'Video thumbnail'}
                fill
                priority
                className="object-cover" 
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <Button 
                  size="lg" 
                  className="rounded-full w-16 h-16" 
                  onClick={() => setShowVideo(true)}
                  disabled={!youtubeVideoId}
                >
                  <Play className="h-6 w-6" />
                </Button>
              </div>
            </>
          )}
        </div>
        <div className="p-6">
          <h1 className="text-xl font-bold mb-2 leading-tight">
            {analysis.title || 'Loading...'}
          </h1>
          <div className="flex flex-col gap-2">
            {analysis.overall_sentiment !== null && (
              <div className="flex items-center gap-2">
                {getSentimentIcon(analysis.overall_sentiment)}
                <span className={`font-semibold text-sm ${getSentimentColor(analysis.overall_sentiment)}`}>
                  Overall Sentiment: {(analysis.overall_sentiment * 100).toFixed(0)}%
                </span>
              </div>
            )}
            {analysis.duration_formatted && (
              <Badge variant="secondary" className="w-fit">
                Duration: {analysis.duration_formatted}
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}