'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { BookText } from 'lucide-react'

interface Citation {
  url: string;
  title: string;
  publication_date?: string;
}

interface CitationsCardProps {
  citations: Citation[];
}

const CitationsCard: React.FC<CitationsCardProps> = ({ citations }) => {
  if (!citations || citations.length === 0) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center text-base">
          <BookText className="w-5 h-5 mr-2 text-gray-600" />
          Related Links
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-0"> {/* Reduced from space-y-2 to space-y-1 */}
          {citations.map((citation, index) => (
            <li key={index} className="overflow-hidden whitespace-nowrap text-ellipsis">
              <a
                href={citation.url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline text-xs"
                title={citation.title}
              >
                {citation.title || citation.url}
              </a>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  )
}

export default CitationsCard
