
'use client'

import React, { Suspense, lazy } from 'react'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Play } from 'lucide-react'

const LazyYouTube = lazy(() => import('react-youtube'))

interface VideoPlayerProps {
  showVideo: boolean
  youtubeVideoId: string | null
  videoMetadata: {
    thumbnail: string
    title: string
    duration: string
  }
  onShowVideo: () => void
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ 
  showVideo, 
  youtubeVideoId, 
  videoMetadata, 
  onShowVideo 
}) => {
  const youtubeOpts = {
    height: '100%',
    width: '100%',
    playerVars: {
      autoplay: 0,
      modestbranding: 1,
      rel: 0,
    },
  }

  return (
    <Card className="h-full w-full flex flex-col">
      <CardContent className="p-0 flex-1 flex flex-col">
        <div className="relative bg-black rounded-lg overflow-hidden flex-1 min-h-0">
          {showVideo && youtubeVideoId ? (
            <div className="absolute inset-0">
              <Suspense fallback={<Skeleton className="w-full h-full" />}>
                <LazyYouTube
                  videoId={youtubeVideoId}
                  opts={youtubeOpts}
                  className="w-full h-full"
                  iframeClassName="w-full h-full rounded-lg"
                />
              </Suspense>
            </div>
          ) : (
            <>
              <Image 
                src={videoMetadata.thumbnail} 
                alt={videoMetadata.title} 
                fill
                priority
                className="object-cover" 
                sizes="(max-width: 1024px) 100vw, 66vw"
                placeholder="blur"
                blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkrHB0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
              />
              <div className="absolute inset-0 flex items-center justify-center">
                <Button 
                  size="lg" 
                  className="rounded-full w-16 h-16" 
                  onClick={onShowVideo}
                  disabled={!youtubeVideoId}
                >
                  <Play className="h-6 w-6" />
                </Button>
              </div>
            </>
          )}
        </div>
        

      </CardContent>
    </Card>
  )
}

export default VideoPlayer
