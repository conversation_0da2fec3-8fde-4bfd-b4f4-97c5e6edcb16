'use client'

import React from 'react'
import { BarChart3, Target, Lightbulb, Heart, Zap, <PERSON>, Brain } from 'lucide-react'
import ScoreCard from './ScoreCard'

interface ScorecardData {
  overall_impact_score: number
  strategic_foundation: number
  creative_execution: number
  emotional_resonance: number
  cta_clarity: number
  brand_integration: number
  memorability_factor: number
}

interface PerformanceScorecardProps {
  scorecard: ScorecardData
}

export default function PerformanceScorecard({ scorecard }: PerformanceScorecardProps) {
  if (!scorecard) return null

  return (
    <div className="bg-white rounded-xl p-6 shadow-sm">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Overall Score - Big Square */}
        <div className="lg:col-span-1 flex flex-col items-center justify-center bg-white rounded-xl p-6 shadow-sm border border-gray-200 aspect-square">
          <div className="text-center w-full">
            <div className="relative w-3/4 mx-auto">
              <svg className="w-full h-full transform -rotate-90" viewBox="0 0 120 120">
                <circle cx="60" cy="60" r="50" fill="none" stroke="#e5e7eb" strokeWidth="8"/>
                <circle 
                  cx="60" cy="60" r="50" fill="none" 
                  stroke="#10B981" strokeWidth="8"
                  strokeLinecap="round"
                  strokeDasharray={`${(scorecard.overall_impact_score || 0) * 31.4}, 314`}
                  className="transition-all duration-1000"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-5xl font-bold text-green-600">{scorecard.overall_impact_score || 0}</div>
                  <div className="text-sm text-gray-600">Overall</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* 6 Small Score Cards */}
        <div className="lg:col-span-2 grid grid-cols-3 grid-rows-2 gap-4 h-full">
          <ScoreCard label="Strategic Foundation" score={scorecard.strategic_foundation} icon={Target} />
          <ScoreCard label="Creative Execution" score={scorecard.creative_execution} icon={Lightbulb} />
          <ScoreCard label="Emotional Resonance" score={scorecard.emotional_resonance} icon={Heart} />
          <ScoreCard label="Call-to-Action" score={scorecard.cta_clarity} icon={Zap} />
          <ScoreCard label="Brand Integration" score={scorecard.brand_integration} icon={Award} />
          <ScoreCard label="Memorability" score={scorecard.memorability_factor} icon={Brain} />
        </div>
      </div>
    </div>
  )
}