'use client'

import React from 'react'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'

interface RatingLogicModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function RatingLogicModal({ isOpen, onClose }: RatingLogicModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Understanding the AI Rating Logic</DialogTitle>
          <DialogDescription>
            These ratings are generated by our experimental AI model. They are designed to provide a quick, high-level assessment based on various marketing principles.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4 text-sm text-gray-700">
          <p className="mb-2">The AI analyzes the ad based on a proprietary framework, evaluating elements such as brand integration, emotional resonance, and call-to-action clarity. Each score reflects the AI&apos;s interpretation of how effectively the ad performs in that specific area.</p>
          <p className="mb-2">Please remember that these ratings are a machine-generated interpretation and should be used as a guide. The AI is continuously learning and evolving, so its assessments may improve over time. Always combine these insights with your own expert judgment.</p>
          <p>For detailed justifications, hover over the individual scores in the scorecard.</p>
        </div>
      </DialogContent>
    </Dialog>
  )
}
