'use client'

import React from 'react'
import Image from 'next/image'
import { Quote } from 'lucide-react'

interface OverallImpressionTestimonialProps {
  impression: string
}

export default function OverallImpressionTestimonial({ impression }: OverallImpressionTestimonialProps) {
  return (
    <div className="w-full p-6">
      {/* Header */}
      <div className="flex items-start space-x-4">
        {/* Avatar */}
        <div className="relative flex-shrink-0">
          <Image
            src="/Gemini_Generated_Image_bzln87bzln87bzln.png"
            alt="AI Expert Avatar"
            width={48}
            height={48}
            className="w-12 h-12 rounded-full object-cover ring-2 ring-slate-700/70 transition hover:ring-violet-500"
          />
        </div>

        {/* Bubble */}
        <div className="flex-1">
          <div className="relative bg-gradient-to-br from-violet-600 via-indigo-600 to-blue-600 rounded-2xl rounded-tl-md px-5 py-4 shadow-lg">
            {/* Quotation Icon */}
            <Quote className="absolute -top-4 -left-4 w-8 h-8 text-violet-400/70" />
            {/* Message */}
            <p className="leading-relaxed text-white/90 text-[20px] tracking-tight">
              “{impression}”
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
