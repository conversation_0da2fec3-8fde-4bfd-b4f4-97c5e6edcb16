'use client'

import React from 'react'
import { LucideIcon } from 'lucide-react'

interface ScoreCardProps {
  label: string
  score: number
  icon: LucideIcon
}

const ScoreCard = ({ label, score, icon: Icon }: ScoreCardProps) => {
  const getColorScheme = (score: number) => {
    if (score <= 4) {
      return {
        icon: 'text-red-600',
        text: 'text-red-600',
      };
    } else if (score <= 7) {
      return {
        icon: 'text-yellow-600',
        text: 'text-yellow-600',
      };
    } else {
      return {
        icon: 'text-green-600',
        text: 'text-green-600',
      };
    }
  }

  const currentColor = getColorScheme(score);

  return (
    <div className="bg-white rounded-xl p-3 border border-gray-200 shadow-sm hover:shadow-md transition-shadow h-full flex flex-col justify-between">
      <div className="flex-grow flex items-center justify-center">
        <span className={`text-4xl font-bold ${currentColor.text}`}>{score}</span>
      </div>
      <div className="flex items-center mt-1">
        <Icon className={`w-4 h-4 mr-2 ${currentColor.icon}`} />
        <p className="text-xs text-gray-600 font-medium leading-tight">{label}</p>
      </div>
    </div>
  )
}

export default ScoreCard
