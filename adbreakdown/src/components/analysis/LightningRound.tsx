'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Building, Tag, Clock, Crown, Target, ChevronDown, ChevronUp, FileText
} from 'lucide-react'

interface LightningRoundData {
  ad_title?: string
  brand?: string
  product_category?: string
  parent_entity?: string
  campaign_category?: string
  runtime?: string | number
  central_insight?: string
  celebrity?: string
}

interface LightningRoundProps {
  lightningRound: LightningRoundData | null
  youtubeMetadata?: any
  videoMetadata?: any
}

export default function LightningRound({ lightningRound, youtubeMetadata, videoMetadata }: LightningRoundProps) {
  const [showVideoDescription, setShowVideoDescription] = useState(false)
  if (!lightningRound) {
    return (
      <Card className="h-full w-full flex flex-col">
        <CardContent className="flex-1 overflow-hidden p-0">
          <div className="h-full flex items-center justify-center px-6">
            <p className="text-gray-600 text-sm text-center">Loading</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const formatRuntime = (runtime: string | number | undefined) => {
    if (!runtime) return 'N/A'
    if (typeof runtime === 'number') {
      const minutes = Math.floor(runtime / 60)
      const seconds = runtime % 60
      return `${minutes}:${seconds.toString().padStart(2, '0')}`
    }
    return runtime.toString()
  }

  return (
    <Card className="w-full"> {/* Flexible height */}
      <CardContent className="p-4">
        <div className="space-y-3">
        {/* Ad Title 
        {lightningRound.ad_title && (
          <div className="flex items-center space-x-3">
            <FileText className="w-4 h-4 text-indigo-600 flex-shrink-0" />
            <div>
              <p className="text-xs text-gray-500 uppercase tracking-wide">Ad Title</p>
              <p className="text-sm font-semibold text-gray-900">{lightningRound.ad_title}</p>
            </div>
          </div>
        )}
        */}

        {/* Brand */}
        {lightningRound.brand && (
          <div className="flex items-center space-x-3">
            <Building className="w-4 h-4 text-blue-600 flex-shrink-0" />
            <div>
              <p className="text-sm font-base text-gray-900">{lightningRound.brand}</p>
            </div>
          </div>
        )}

        {/* Product Category */}
        {lightningRound.product_category && (
          <div className="flex items-center space-x-3">
            <Tag className="w-4 h-4 text-green-600 flex-shrink-0" />
            <div>
              <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200">
                {lightningRound.product_category}
              </Badge>
            </div>
          </div>
        )}

        {/* Parent Entity 
        {lightningRound.parent_entity && (
          <div className="flex items-center space-x-3">
            <Users className="w-4 h-4 text-purple-600 flex-shrink-0" />
            <div>
              <p className="text-xs text-gray-500 uppercase tracking-wide">Parent Entity</p>
              <p className="text-sm font-medium text-gray-900">{lightningRound.parent_entity}</p>
            </div>
          </div>
        )}
        */}

        {/* Campaign Category */}
        {lightningRound.campaign_category && (
          <div className="flex items-center space-x-3">
            <Target className="w-4 h-4 text-orange-600 flex-shrink-0" />
            <div>
              <Badge variant="secondary" className="bg-orange-50 text-orange-700 border-orange-200">
                {lightningRound.campaign_category}
              </Badge>
            </div>
          </div>
        )}

        {/* Runtime */}
        {lightningRound.runtime && (
          <div className="flex items-center space-x-3">
            <Clock className="w-4 h-4 text-red-600 flex-shrink-0" />
            <div>
              <p className="text-sm font-base text-gray-900">{formatRuntime(lightningRound.runtime)}</p>
            </div>
          </div>
        )}

        {/* Celebrity */}
        {lightningRound.celebrity && (
          <div className="flex items-center space-x-3">
            <Crown className="w-4 h-4 text-amber-600 flex-shrink-0" />
            <div>
              <p className="text-sm font-base text-gray-900">{lightningRound.celebrity}</p>
            </div>
          </div>
        )}

        {/* Central Insight 
        {lightningRound.central_insight && (
          <div className="mt-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
            <div className="flex items-start space-x-3">
              <Lightbulb className="w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-xs text-blue-600 uppercase tracking-wide font-semibold mb-2">Central Insight</p>
                <p className="text-sm text-blue-900 leading-relaxed">{lightningRound.central_insight}</p>
              </div>
            </div>
          </div>
        )}
        */}

        {/* Video Description Dropdown */}
        {youtubeMetadata && (
          <div className="border-t pt-3 mt-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowVideoDescription(!showVideoDescription)}
              className="w-full justify-between text-sm"
            >
              <div className="flex items-center">
                <FileText className="w-4 h-4 mr-2" />
                Video Details
              </div>
              {showVideoDescription ? (
                <ChevronUp className="w-4 h-4" />
              ) : (
                <ChevronDown className="w-4 h-4" />
              )}
            </Button>
            
            {showVideoDescription && (
              <div className="mt-3 space-y-3">
                {/* Video Stats */}
                <div className="grid grid-cols-2 gap-2">
                  {youtubeMetadata.viewCount && (
                    <div className="text-center p-2 bg-gray-50 rounded-lg">
                      <div className="text-xs text-gray-600 mb-1">Views</div>
                      <div className="text-sm font-bold text-gray-900">
                        {parseInt(youtubeMetadata.viewCount).toLocaleString()}
                      </div>
                    </div>
                  )}
                  {youtubeMetadata.likeCount && (
                    <div className="text-center p-2 bg-gray-50 rounded-lg">
                      <div className="text-xs text-gray-600 mb-1">Likes</div>
                      <div className="text-sm font-bold text-gray-900">
                        {parseInt(youtubeMetadata.likeCount).toLocaleString()}
                      </div>
                    </div>
                  )}
                  {youtubeMetadata.commentCount && (
                    <div className="text-center p-2 bg-gray-50 rounded-lg">
                      <div className="text-xs text-gray-600 mb-1">Comments</div>
                      <div className="text-sm font-bold text-gray-900">
                        {parseInt(youtubeMetadata.commentCount).toLocaleString()}
                      </div>
                    </div>
                  )}
                  {youtubeMetadata.duration && (
                    <div className="text-center p-2 bg-gray-50 rounded-lg">
                      <div className="text-xs text-gray-600 mb-1">Duration</div>
                      <div className="text-sm font-bold text-gray-900">
                        {youtubeMetadata.duration}
                      </div>
                    </div>
                  )}
                </div>

                {/* Video Description */}
                {youtubeMetadata.description && (
                  <div>
                    <h4 className="font-medium mb-2 text-gray-900 text-sm">Description</h4>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <p className="text-xs text-gray-700 leading-relaxed">
                        {youtubeMetadata.description.length > 150 
                          ? `${youtubeMetadata.description.substring(0, 150)}...` 
                          : youtubeMetadata.description
                        }
                      </p>
                    </div>
                  </div>
                )}

                {/* Channel Info */}
                {youtubeMetadata.channelTitle && (
                  <div>
                    <h4 className="font-medium mb-2 text-gray-900 text-sm">Channel</h4>
                    <div className="flex items-center space-x-2">
                      <Building className="w-4 h-4 text-gray-600" />
                      <span className="text-sm text-gray-700">{youtubeMetadata.channelTitle}</span>
                    </div>
                  </div>
                )}

                {/* Tags */}
                {youtubeMetadata.tags && youtubeMetadata.tags.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-2 text-gray-900 text-sm">Tags</h4>
                    <div className="flex flex-wrap gap-1">
                      {youtubeMetadata.tags.slice(0, 4).map((tag: string, index: number) => (
                        <Badge key={index} variant="secondary" className="text-xs px-2 py-0.5">
                          {tag.length > 10 ? `${tag.substring(0, 10)}...` : tag}
                        </Badge>
                      ))}
                      {youtubeMetadata.tags.length > 4 && (
                        <Badge variant="outline" className="text-xs px-2 py-0.5">
                          +{youtubeMetadata.tags.length - 4} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
        </div>
      </CardContent>
    </Card>
  )
}