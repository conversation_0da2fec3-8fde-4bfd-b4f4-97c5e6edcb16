'use client';

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MessageSquare } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeSanitize from 'rehype-sanitize'

interface ScriptGridSectionProps {
  analysis: any
  enhancedScript: any
  enhancedScriptLoading: boolean
  generateEnhancedScript: () => void
  checkCredits: (amount: number) => boolean
}

const ScriptGridSection: React.FC<ScriptGridSectionProps> = ({ 
  analysis, 
  enhancedScript, 
  enhancedScriptLoading, 
  generateEnhancedScript,
  checkCredits
}) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Enhanced Script Analysis - Takes 2 columns */}
      <div className="lg:col-span-2">
        <Card className="h-full flex flex-col">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Enhanced Script Analysis
                </CardTitle>
                <CardDescription>
                  AI-powered analysis with detailed insights and recommendations
                </CardDescription>
              </div>
              {!enhancedScript && !enhancedScriptLoading && (
                <Button 
                  onClick={generateEnhancedScript}
                  disabled={!checkCredits(3)}
                  size="sm"
                  className="ml-4"
                >
                  {enhancedScriptLoading ? 'Generating...' : 'Generate Enhanced Analysis (3 Credits)'}
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="flex-1">
            {enhancedScriptLoading ? (
              <div className="h-full min-h-96 bg-gray-50 rounded-lg p-4 flex items-center justify-center">
                <div className="text-center">
                  <div className="animate-spin h-6 w-6 border-2 border-blue-600 border-t-transparent rounded-full mx-auto mb-2"></div>
                  <p className="text-sm text-gray-600">Generating enhanced script analysis...</p>
                </div>
              </div>
            ) : enhancedScript ? (
              <div className="h-full min-h-96 overflow-y-auto">
                <div className="enhanced-script-content">
                  <ReactMarkdown 
                    remarkPlugins={[remarkGfm]} 
                    rehypePlugins={[rehypeSanitize]}
                    components={{
                      h1: ({children}) => <h1 className="text-lg font-bold text-gray-900 leading-tight tracking-tight mb-3 pb-2 border-b border-gray-200">{children}</h1>,
                      h2: ({children}) => <h2 className="text-base font-semibold text-gray-900 leading-tight tracking-tight mt-4 mb-2 flex items-center gap-2">{children}</h2>,
                      h3: ({children}) => <h3 className="text-sm font-semibold text-gray-900 leading-tight tracking-tight mt-3 mb-2 bg-gray-50 px-3 py-1 rounded border-l-2 border-blue-400">{children}</h3>,
                      p: ({children}) => <p className="text-sm text-gray-900 leading-relaxed tracking-normal mb-2">{children}</p>,
                      ul: ({children}) => <ul className="text-sm text-gray-900 leading-relaxed space-y-1 mb-2 pl-1">{children}</ul>,
                      li: ({children}) => <li className="text-sm text-gray-900 leading-relaxed tracking-normal pl-2 relative before:content-['•'] before:text-blue-500 before:font-bold before:absolute before:-left-3">{children}</li>,
                      strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                      em: ({children}) => <em className="italic text-gray-700">{children}</em>
                    }}
                  >
                    {enhancedScript.enhanced_script || 'No enhanced script content available.'}
                  </ReactMarkdown>
                </div>
              </div>
            ) : (
              <div className="h-full min-h-96 bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center justify-center">
                <div className="text-center">
                  <MessageSquare className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-600 mb-3">Generate enhanced script analysis for deeper insights</p>
                  <Button 
                    onClick={generateEnhancedScript}
                    disabled={!checkCredits(3)}
                    size="sm"
                  >
                    Generate Analysis (3 Credits)
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Transcript & Summary - Takes 1 column */}
      <div className="space-y-6 h-full flex flex-col">
        {/* Transcript */}
        <Card className="flex-1">
          <CardHeader>
            <CardTitle className="text-base">Transcript</CardTitle>
            <CardDescription className="text-sm">Full transcript of the ad content</CardDescription>
          </CardHeader>
          <CardContent className="flex-1">
            {analysis?.transcript ? (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 h-full overflow-y-auto">
                <div className="text-sm text-gray-900 leading-relaxed tracking-normal">
                  <p className="text-gray-900 leading-relaxed whitespace-pre-line mb-0">{analysis.transcript}</p>
                </div>
              </div>
            ) : (
              <div className="text-sm text-gray-500 italic bg-gray-50 border border-gray-200 rounded-lg p-3">
                Transcript will be available once the video analysis is completed.
              </div>
            )}
          </CardContent>
        </Card>

        {/* Summary */}
        <Card className="flex-1">
          <CardHeader>
            <CardTitle className="text-base">Summary</CardTitle>
            <CardDescription className="text-sm">Comprehensive summary of the ad content</CardDescription>
          </CardHeader>
          <CardContent className="flex-1">
            {analysis?.summary ? (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 h-full overflow-y-auto">
                <div className="text-sm text-gray-900 leading-relaxed tracking-normal">
                  <p className="text-gray-900 leading-relaxed whitespace-pre-line mb-0">{analysis.summary}</p>
                </div>
              </div>
            ) : (
              <div className="text-sm text-gray-500 italic bg-gray-50 border border-gray-200 rounded-lg p-3">
                Summary will be available once the video analysis is completed.
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default ScriptGridSection