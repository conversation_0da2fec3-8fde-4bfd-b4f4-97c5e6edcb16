
'use client';

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { MessageSquare } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeSanitize from 'rehype-sanitize'

interface ScriptTabProps {
  analysis: any
  enhancedScript: any
  enhancedScriptLoading: boolean
  generateEnhancedScript: () => void
  checkCredits: (amount: number) => boolean
}

const ScriptTab: React.FC<ScriptTabProps> = ({ 
  analysis, 
  enhancedScript, 
  enhancedScriptLoading, 
  generateEnhancedScript,
  checkCredits
}) => {
  return (
    <>
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Transcript
          </CardTitle>
          <CardDescription>
            Full transcript of the ad content
          </CardDescription>
        </CardHeader>
        <CardContent>
          {analysis?.transcript ? (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 max-h-96 overflow-y-auto">
              <div className="text-sm text-gray-900 leading-relaxed tracking-normal">
                <p className="text-gray-900 leading-relaxed whitespace-pre-line mb-0">{analysis.transcript}</p>
              </div>
            </div>
          ) : (
            <div className="text-sm text-gray-500 italic bg-gray-50 border border-gray-200 rounded-lg p-4">
              Transcript will be available once the video analysis is completed.
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Summary
          </CardTitle>
          <CardDescription>
            Comprehensive summary of the ad content and message
          </CardDescription>
        </CardHeader>
        <CardContent>
          {analysis?.summary ? (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="text-sm text-gray-900 leading-relaxed tracking-normal">
                <p className="text-gray-900 leading-relaxed whitespace-pre-line mb-0">{analysis.summary}</p>
              </div>
            </div>
          ) : (
            <div className="text-gray-500 italic bg-gray-50 border border-gray-200 rounded-lg p-4">
              Summary will be available once the video analysis is completed.
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Pro Script Analysis
          </CardTitle>
          <CardDescription>
            Generate a detailed scene-by-scene script analysis with sentiment tracking and creative insights
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {!enhancedScript ? (
            <div className="text-center py-8">
              <h4 className="font-medium mb-3 text-gray-900">Advanced Script Analysis</h4>
              <p className="text-gray-600 mb-6 max-w-md mx-auto">
                Get a comprehensive scene-by-scene breakdown of your ad with expert analysis of dialogue, 
                visual elements, emotions, and creative insights.
              </p>
              <Button 
                onClick={generateEnhancedScript} 
                disabled={enhancedScriptLoading || !checkCredits(3)}
                size="lg"
              >
                {enhancedScriptLoading ? 'Generating Pro Analysis...' : 'Generate Pro Script Analysis (3 Credits)'}
              </Button>
              {!checkCredits(3) && (
                <p className="text-sm text-red-600 mt-2">
                  Insufficient credits. Please upgrade your plan to continue.
                </p>
              )}
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="font-medium text-gray-900">Enhanced Script Analysis</h4>
                <Badge variant="outline" className="text-xs">
                  Generated
                </Badge>
              </div>
              <div className="enhanced-script-container bg-white border border-gray-200 rounded-lg shadow-sm p-6">
                <div className="enhanced-script-content">
                  <ReactMarkdown 
                    remarkPlugins={[remarkGfm]} 
                    rehypePlugins={[rehypeSanitize]}
                    components={{
                    h1: ({children}) => <h1 className="text-lg font-bold text-gray-900 leading-tight tracking-tight mb-3 pb-2 border-b border-gray-200">{children}</h1>,
                    h2: ({children}) => <h2 className="text-base font-semibold text-gray-900 leading-tight tracking-tight mt-4 mb-2 flex items-center gap-2">{children}</h2>,
                    h3: ({children}) => <h3 className="text-sm font-semibold text-gray-900 leading-tight tracking-tight mt-3 mb-2 bg-gray-50 px-3 py-1 rounded border-l-2 border-blue-400">{children}</h3>,
                    h4: ({children}) => <h4 className="text-sm font-medium text-gray-900 leading-tight tracking-tight mt-3 mb-1 flex items-center gap-1">{children}</h4>,
                    h5: ({children}) => <h5 className="text-sm font-medium text-gray-900 leading-tight tracking-normal mt-2 mb-1">{children}</h5>,
                    p: ({children}) => <p className="text-sm text-gray-900 leading-relaxed tracking-normal mb-2">{children}</p>,
                    ul: ({children}) => <ul className="text-sm text-gray-900 leading-relaxed space-y-1 mb-2 pl-1">{children}</ul>,
                    ol: ({children}) => <ol className="text-sm text-gray-900 leading-relaxed space-y-1 mb-2 pl-1">{children}</ol>,
                    li: ({children}) => <li className="text-sm text-gray-900 leading-relaxed tracking-normal pl-2 relative before:content-['•'] before:text-blue-500 before:font-bold before:absolute before:-left-3">{children}</li>,
                    strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                    em: ({children}) => <em className="italic text-gray-700">{children}</em>,
                    code: ({children}) => <code className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-sm font-mono">{children}</code>,
                    pre: ({children}) => <pre className="bg-gray-50 border border-gray-200 rounded-lg p-4 overflow-x-auto text-sm font-mono mb-2">{children}</pre>,
                    blockquote: ({children}) => <blockquote className="border-l-4 border-blue-400 bg-blue-50 pl-4 py-2 my-2 italic text-sm text-gray-900 leading-relaxed">{children}</blockquote>,
                    hr: () => <hr className="border-gray-300 my-4" />,
                    table: ({children}) => <div className="overflow-x-auto mb-2"><table className="min-w-full border-collapse border border-gray-300">{children}</table></div>,
                    thead: ({children}) => <thead className="bg-gray-50">{children}</thead>,
                    tbody: ({children}) => <tbody>{children}</tbody>,
                    tr: ({children}) => <tr className="border-t border-gray-200">{children}</tr>,
                    th: ({children}) => <th className="border border-gray-300 px-4 py-2 text-left font-semibold text-sm text-gray-900">{children}</th>,
                    td: ({children}) => <td className="border border-gray-300 px-4 py-2 text-sm text-gray-900">{children}</td>,
                  }}
                  >
                    {enhancedScript.enhanced_script || 'No enhanced script content available.'}
                  </ReactMarkdown>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </>
  )
}

export default ScriptTab
