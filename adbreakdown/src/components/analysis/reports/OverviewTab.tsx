
'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { TrendingUp, Eye, Volume2, Target, Users } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeSanitize from 'rehype-sanitize'

interface OverviewTabProps {
  marketingAnalysis: string
  detailedAnalysisData: any
}

const OverviewTab: React.FC<OverviewTabProps> = ({ marketingAnalysis, detailedAnalysisData }) => {
  const getSentimentColor = (sentiment: number) => {
    if (sentiment >= 0.7) return "text-green-600"
    if (sentiment >= 0.4) return "text-yellow-600"
    return "text-red-600"
  }

  return (
    <>
      {marketingAnalysis && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Expert Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-gray-900 leading-relaxed tracking-normal space-y-3">
              <ReactMarkdown 
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeSanitize]}
                components={{
                  h1: ({node, ...props}) => <h1 className="text-lg font-semibold text-gray-900 leading-tight tracking-tight mb-2" {...props} />,
                  h2: ({node, ...props}) => <h2 className="text-base font-medium text-gray-900 leading-tight tracking-tight mb-2 mt-3" {...props} />,
                  h3: ({node, ...props}) => <h3 className="text-sm font-medium text-gray-900 leading-tight tracking-tight mb-1 mt-3" {...props} />,
                  p: ({node, ...props}) => <p className="text-base text-gray-900 leading-relaxed tracking-normal mb-2" {...props} />,
                  ul: ({node, ...props}) => <ul className="text-sm text-gray-900 leading-relaxed space-y-1 ml-4 mb-2" {...props} />,
                  ol: ({node, ...props}) => <ol className="text-sm text-gray-900 leading-relaxed space-y-1 ml-4 mb-2" {...props} />,
                  li: ({node, ...props}) => <li className="text-sm text-gray-900 leading-relaxed tracking-normal" {...props} />,
                  strong: ({node, ...props}) => <strong className="font-semibold text-gray-900" {...props} />,
                  em: ({node, ...props}) => <em className="italic text-gray-700" {...props} />
                }}
              >
                {marketingAnalysis}
              </ReactMarkdown>
            </div>
          </CardContent>
        </Card>
      )}
      {detailedAnalysisData && (
        <>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Sentiment Score
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">
                  {(detailedAnalysisData.overallSentiment * 100).toFixed(0)}%
                </div>
                <p className="text-sm text-gray-600">Overall positive</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Eye className="h-4 w-4" />
                  Visual Appeal
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{detailedAnalysisData.visualAnalysis.visualAppeal}/10</div>
                <p className="text-sm text-gray-600">Professional quality</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Volume2 className="h-4 w-4" />
                  Audio Quality
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{detailedAnalysisData.audioAnalysis.audioQuality}/10</div>
                <p className="text-sm text-gray-600">High quality</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm font-medium flex items-center gap-2">
                  <Target className="h-4 w-4" />
                  Target Match
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">92%</div>
                <p className="text-sm text-gray-600">Well targeted</p>
              </CardContent>
            </Card>
          </div>

          {/* Competitor Comparison */}
          <Card className="relative">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Competitor Comparison
              </CardTitle>
              <CardDescription>How your ad performs against similar competitor ads</CardDescription>
            </CardHeader>
            <CardContent className="relative">
              <div className="space-y-4 blur-sm">
                {detailedAnalysisData.competitorComparison.map((competitor: any, index: number) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-700 font-bold">
                        {competitor.name.charAt(0)}
                      </div>
                      <div>
                        <div className="font-medium">{competitor.name}</div>
                        <div className="text-sm text-gray-600">Competitor</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-4">
                      <div className="text-center">
                        <div className="text-sm font-medium">Sentiment</div>
                        <div className={getSentimentColor(competitor.sentiment)}>
                          {(competitor.sentiment * 100).toFixed(0)}%
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-sm font-medium">Effectiveness</div>
                        <div className="text-blue-600">{competitor.effectiveness.toFixed(1)}/10</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              {/* Coming Soon Overlay */}
              <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg">
                <div className="text-center p-6">
                  <div className="text-2xl font-bold text-gray-800 mb-2">Coming Soon</div>
                  <div className="text-sm text-gray-600 max-w-xs">
                    Advanced competitor analysis and benchmarking features are in development
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </>
  )
}

export default OverviewTab
