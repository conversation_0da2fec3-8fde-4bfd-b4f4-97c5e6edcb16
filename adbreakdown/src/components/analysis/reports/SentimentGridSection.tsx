'use client';

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { TrendingUp, Heart, Smile, Frown, <PERSON>h, Zap, ThumbsDown } from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeSanitize from 'rehype-sanitize'

interface SentimentGridSectionProps {
  detailedAnalysisData: any
  emotionTimeline: any
}

const SentimentGridSection: React.FC<SentimentGridSectionProps> = ({ detailedAnalysisData, emotionTimeline }) => {
  const getSentimentColor = (sentiment: number) => {
    if (sentiment >= 0.7) return "text-green-600 bg-green-50"
    if (sentiment >= 0.4) return "text-yellow-600 bg-yellow-50"
    return "text-red-600 bg-red-50"
  }

  const getEmotionIcon = (emotion: string) => {
    switch (emotion.toLowerCase()) {
      case 'joy': return <Smile className="h-4 w-4" />
      case 'sadness': return <Frown className="h-4 w-4" />
      case 'surprise': return <Zap className="h-4 w-4" />
      case 'anger': return <ThumbsDown className="h-4 w-4" />
      case 'fear': return <Frown className="h-4 w-4" />
      case 'disgust': return <ThumbsDown className="h-4 w-4" />
      case 'humor': return <Smile className="h-4 w-4" />
      default: return <Heart className="h-4 w-4" />
    }
  }

  const getEmotionColor = (emotion: string) => {
    switch (emotion.toLowerCase()) {
      case 'joy': return "text-yellow-600 bg-yellow-50"
      case 'sadness': return "text-blue-600 bg-blue-50"
      case 'surprise': return "text-purple-600 bg-purple-50"
      case 'anger': return "text-red-600 bg-red-50"
      case 'fear': return "text-gray-600 bg-gray-50"
      case 'disgust': return "text-orange-600 bg-orange-50"
      case 'humor': return "text-green-600 bg-green-50"
      default: return "text-gray-600 bg-gray-50"
    }
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Emotion Distribution - Takes 1 column */}
      <div>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-base">
              <Heart className="h-5 w-5" />
              Emotion Distribution
            </CardTitle>
            <CardDescription className="text-sm">Breakdown of emotional content</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Overall Sentiment */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Overall Sentiment</span>
                <Badge className={`text-xs ${getSentimentColor(detailedAnalysisData.overallSentiment)}`}>
                  {Math.round(detailedAnalysisData.overallSentiment * 100)}%
                </Badge>
              </div>
              <Progress value={detailedAnalysisData.overallSentiment * 100} className="h-2" />
            </div>

            {/* Individual Emotions */}
            <div className="space-y-3">
              <h4 className="font-medium text-sm">Emotion Breakdown</h4>
              {Object.entries(detailedAnalysisData.emotions || {}).map(([emotion, value]) => (
                <div key={emotion} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`p-1 rounded ${getEmotionColor(emotion)}`}>
                        {getEmotionIcon(emotion)}
                      </div>
                      <span className="text-sm font-medium capitalize">{emotion}</span>
                    </div>
                    <span className="text-sm font-semibold">{String(value)}%</span>
                  </div>
                  <Progress value={Number(value)} className="h-1.5" />
                </div>
              ))}
            </div>

            {/* Dominant Emotion */}
            {emotionTimeline?.primaryAudienceEmotion && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <h4 className="font-medium text-sm text-blue-900 mb-1">Primary Emotion</h4>
                <p className="text-sm text-blue-800 capitalize">{emotionTimeline.primaryAudienceEmotion}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Sentiment Analysis - Takes 2 columns */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Detailed Sentiment Analysis
            </CardTitle>
            <CardDescription>Comprehensive emotional journey and sentiment insights</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Analysis Summary */}
            {emotionTimeline?.analysisSummary && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h4 className="font-medium text-sm mb-3">Analysis Summary</h4>
                <div className="text-sm text-gray-900 leading-relaxed tracking-normal space-y-2">
                  <ReactMarkdown 
                    remarkPlugins={[remarkGfm]} 
                    rehypePlugins={[rehypeSanitize]}
                    components={{
                      p: ({children}) => <p className="text-sm text-gray-900 leading-relaxed tracking-normal mb-2">{children}</p>,
                      strong: ({children}) => <strong className="font-semibold text-gray-900">{children}</strong>,
                      em: ({children}) => <em className="italic text-gray-700">{children}</em>
                    }}
                  >
                    {emotionTimeline.analysisSummary}
                  </ReactMarkdown>
                </div>
              </div>
            )}

            {/* Sentiment Metrics Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Overall Sentiment Card */}
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <TrendingUp className={`h-6 w-6 ${getSentimentColor(detailedAnalysisData.overallSentiment).split(' ')[0]}`} />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Overall Score</p>
                    <p className={`text-2xl font-bold ${getSentimentColor(detailedAnalysisData.overallSentiment).split(' ')[0]}`}>
                      {Math.round(detailedAnalysisData.overallSentiment * 100)}%
                    </p>
                  </div>
                </div>
              </div>

              {/* Emotional Intensity */}
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <Heart className="h-6 w-6 text-purple-600" />
                  <div>
                    <p className="text-sm font-medium text-gray-700">Emotional Intensity</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {emotionTimeline?.overallSentiment ? Math.round(emotionTimeline.overallSentiment * 100) : 'N/A'}%
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Sentiment Timeline Preview */}
            {detailedAnalysisData.scriptAnalysis?.sentimentTimeline && (
              <div className="space-y-3">
                <h4 className="font-medium text-sm">Sentiment Journey</h4>
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                    {detailedAnalysisData.scriptAnalysis.sentimentTimeline.slice(0, 4).map((point: any, index: number) => (
                      <div key={index} className="text-center">
                        <p className="text-xs text-gray-500 mb-1">{point.timeSeconds}s</p>
                        <div className={`text-lg font-bold ${getSentimentColor(point.sentiment).split(' ')[0]}`}>
                          {Math.round(point.sentiment * 100)}%
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Coming Soon Overlay for Timeline */}
            <div className="relative">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 blur-sm">
                <h4 className="font-medium text-sm mb-3">Advanced Emotion Timeline</h4>
                <div className="h-32 bg-gradient-to-r from-blue-100 to-purple-100 rounded"></div>
              </div>
              <div className="absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm rounded-lg">
                <div className="text-center p-4">
                  <div className="text-lg font-bold text-gray-800 mb-1">Coming Soon</div>
                  <div className="text-sm text-gray-600">Interactive emotion timeline visualization</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

export default SentimentGridSection