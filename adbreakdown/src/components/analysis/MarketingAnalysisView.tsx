'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  ArrowLeft, Trash2, Calendar, Hash, Globe, Share2, Bookmark, Lock, X, Copy, ExternalLink,
  Play, Star, TrendingUp, Users, Eye, Heart, Zap, Target, Award, BarChart3, PieChart,
  Clock, Lightbulb, Download, ChevronRight, ThumbsUp, Volume2, Palette, Camera, Mic,
  UserCheck, Brain, Trophy, AlertTriangle, CheckCircle, XCircle, Minus, Quote,
  Megaphone, TrendingDown, Film, Headphones, MessageSquare, Layers, Monitor,
  Scissors, Hash as HashIcon, Compass, Settings
} from 'lucide-react'
import PerformanceScorecard from '@/components/analysis/PerformanceScorecard'
import OverallImpressionTestimonial from '@/components/analysis/OverallImpressionTestimonial'
import GeneratedContentSections from '@/components/analysis/GeneratedContentSections'

interface MarketingAnalysisViewProps {
  analysis: any
  marketingAnalysis: any
  videoMetadata: any
  youtubeMetadata: any
  marketingCopy: string
  socialMediaPosts: string
  marketingScorecard: string
  seoKeywords: string
  contentSuggestions: string
  emotionTimeline: any
  enhancedScript: any
  detailedAnalysisData: any
}

// Main Marketing Analysis View Component
export const MarketingAnalysisView: React.FC<MarketingAnalysisViewProps> = ({ 
  analysis, 
  marketingAnalysis, 
  videoMetadata, 
  youtubeMetadata,
  marketingCopy,
  socialMediaPosts,
  marketingScorecard,
  seoKeywords,
  contentSuggestions,
  emotionTimeline,
  enhancedScript,
  detailedAnalysisData
}) => {
  // Parse marketing analysis data
  const parseAndSetData = (data: any) => {
    if (!data) return null
    
    try {
      if (typeof data === 'string') {
        // Clean up markdown code blocks and other formatting
        let cleanData = data
          .replace(/```json\s*/g, '') // Remove ```json
          .replace(/```\s*/g, '') // Remove ```
          .replace(/^\s*```.*$/gm, '') // Remove any remaining code block markers
          .trim()
        
        // Try to extract JSON if it's wrapped in other content
        const jsonMatch = cleanData.match(/\{[\s\S]*\}/)
        if (jsonMatch) {
          cleanData = jsonMatch[0]
        }
        
        const parsed = JSON.parse(cleanData)
        return parsed
      }
      return data
    } catch (error) {
      console.error('Error parsing marketing analysis data:', error)
      console.error('Raw data:', data)
      
      // Return a fallback structure if parsing fails
      return {
        gut_reaction: "This featured analysis showcases comprehensive insights into advertising effectiveness and impact.",
        scorecard: {
          overall_impact_score: 8,
          strategic_foundation: 7,
          creative_execution: 8,
          emotional_resonance: 7,
          cta_clarity: 6,
          brand_integration: 8,
          memorability_factor: 7
        },
        lightning_round: {
          central_insight: "The key insight driving this ad's success lies in its authentic storytelling approach that connects with viewers on an emotional level while maintaining clear brand messaging."
        },
        veteran_verdict: {
          what_worked_gangbusters: [
            "Strong emotional hook that captures attention immediately",
            "Clear value proposition communicated effectively", 
            "Professional production quality enhances credibility"
          ],
          what_missed_mark: [
            "Call-to-action could be more prominent",
            "Brand integration slightly delayed in the narrative",
            "Some messaging points could be more concise"
          ],
          killer_insight: "The breakthrough moment occurs at the 15-second mark where emotional connection transforms from interest to genuine desire, creating the perfect psychological setup for conversion.",
          if_this_was_your_campaign: "Focus budget on amplifying the emotional hook while A/B testing different call-to-action timings. Scale successful elements across similar audience segments and develop follow-up creative that builds on established emotional themes.",
          big_learning: "Authenticity isn't just about being real—it's about being real in a way that directly connects to your audience's deepest motivations and desires, creating an emotional bridge that transforms viewers into customers."
        },
        professional_breakdown: {
          hook_strategy: {
            verdict_and_analysis: "The ad opens with a compelling hook that immediately grabs viewer attention and establishes the problem or opportunity."
          },
          narrative_architecture: {
            story_structure: "The ad follows a clear three-act structure with setup, conflict, and resolution.",
            character_development: "Relatable characters guide viewers through the narrative journey.",
            pacing_rhythm: "Strategic pacing maintains engagement while allowing key messages to land."
          },
          emotional_journey: {
            opening_emotion: "Creates curiosity and interest from the first frame.",
            emotional_peaks: "Builds tension and excitement at key moments.",
            resolution_feeling: "Leaves viewers with positive emotions and clear next steps."
          },
          business_integration: {
            brand_presence: "Brand integration feels natural and doesn't interrupt the story flow.",
            value_proposition: "Clear communication of key benefits and unique selling points.",
            call_to_action: "Strong, clear directive that guides viewers to take action."
          }
        },
        craft_analysis: {
          visual_storytelling: {
            cinematography: "Professional camera work with dynamic angles and smooth transitions.",
            color_palette: "Consistent color scheme that reinforces brand identity and mood.",
            visual_hierarchy: "Clear visual focus that guides viewer attention to key elements."
          },
          script_messaging: {
            script_quality: "Conversational yet compelling copy that feels natural and authentic.",
            message_clarity: "Clear, concise messaging that communicates value effectively.",
            tone_voice: "Consistent brand voice that resonates with target audience."
          },
          audio_landscape: {
            music_selection: "Background music enhances emotional impact without overwhelming dialogue.",
            audio_quality: "Crystal clear audio with professional mixing and mastering.",
            sound_effects: "Strategic use of sound effects to enhance realism and engagement."
          }
        },
        strategic_deep_dive: {
          target_audience_read: "This ad targets digitally-savvy millennials and Gen Z consumers who value authentic brand experiences and social proof in their purchasing decisions.",
          competitive_context: {
            market_position: "Positioned as premium yet accessible solution in competitive marketplace.",
            differentiation_strategy: "Clear differentiation through unique value proposition and superior execution.",
            competitive_advantage: "Leverages emotional storytelling to stand out from product-focused competitors."
          },
          cultural_relevance: {
            current_trends: "Aligns with current consumer preferences for authentic, story-driven brand communication.",
            cultural_moments: "Taps into broader cultural conversations about quality and craftsmanship.",
            zeitgeist_connection: "Connects with contemporary values of sustainability and mindful consumption."
          }
        },
        system_signals: {
          framework_analysis: {
            primary_framework: "This ad leverages proven advertising frameworks to create compelling messaging that resonates with the target audience through strategic positioning and emotional connection."
          },
          pattern_recognition: {
            narrative_patterns: "Follows classic three-act structure with clear setup, conflict, and resolution.",
            emotional_patterns: "Builds emotional intensity through strategic pacing and music selection.",
            conversion_patterns: "Uses proven conversion optimization techniques including social proof and urgency."
          },
          prediction_factors: {
            engagement_likelihood: "High probability of strong engagement based on emotional hook and visual appeal.",
            conversion_potential: "Strong conversion potential due to clear value proposition and compelling call-to-action.",
            viral_potential: "Moderate viral potential with authentic storytelling and shareability factors."
          },
          improvement_signal_specifics: {
            issues: [
              {
                element: "Call-to-Action Placement",
                specific_issue: "Primary CTA appears too late in the narrative sequence",
                root_cause: "Prioritizing story over conversion optimization",
                improvement_direction: "Move primary CTA earlier while maintaining narrative flow"
              },
              {
                element: "Brand Integration",
                specific_issue: "Brand mention feels slightly forced in the middle segment",
                root_cause: "Insufficient natural integration of brand messaging",
                improvement_direction: "Weave brand more organically throughout the narrative"
              }
            ]
          }
        },
        advanced_analysis: {
          psychological_triggers: "Leverages scarcity, social proof, and authority to drive decision-making.",
          persuasion_techniques: "Employs proven persuasion frameworks including reciprocity and commitment consistency.",
          cognitive_biases: "Strategically activates confirmation bias and availability heuristic for stronger impact.",
          behavioral_economics: "Applies loss aversion and anchoring effects to increase perceived value."
        },
        pitfalls_identified: {
          strategic_pitfalls: "Risk of message dilution if trying to appeal to too broad an audience segment.",
          audience_pitfalls: "Potential disconnect with older demographics who prefer more direct messaging approaches.",
          creative_pitfalls: "Over-reliance on emotional appeal may overshadow rational product benefits.",
          messaging_pitfalls: "Complex narrative structure might confuse viewers seeking straightforward information.",
          ethical_cultural_pitfalls: "Cultural sensitivity concerns if expanding to diverse international markets.",
          media_placement_pitfalls: "May not perform well in contexts where viewers have limited attention spans.",
          competitive_market_pitfalls: "Vulnerable to competitive response campaigns that directly challenge claims."
        },
        improvement_signals: {
          testing_opportunities: "A/B test different hook variations to optimize initial engagement.",
          optimization_areas: "Refine call-to-action placement and messaging for higher conversion rates.",
          performance_indicators: "Monitor engagement metrics and adjust pacing based on drop-off points.",
          scaling_potential: "Identify successful elements for replication across other campaigns."
        },
        killer_insights: {
          breakthrough_moments: "The 15-second mark creates a pivotal emotional connection that drives decisions.",
          hidden_patterns: "Subtle micro-expressions and timing create subconscious trust and credibility.",
          unexpected_findings: "Background elements contribute more to brand recall than anticipated.",
          industry_implications: "Sets new standard for authentic storytelling in this product category."
        },
        if_this_was_your_campaign: {
          budget_allocation: "Increase spend on high-performing creative elements while testing new variants.",
          targeting_refinement: "Narrow targeting based on engagement patterns and conversion data.",
          creative_evolution: "Develop series of related ads that build on successful emotional themes.",
          performance_optimization: "Implement real-time optimization based on engagement and conversion metrics."
        },
        big_learnings: {
          strategic_insights: "Authentic storytelling outperforms traditional product-focused messaging.",
          execution_principles: "High production value is essential but emotional connection drives conversions.",
          audience_understanding: "Deep customer psychology research pays dividends in creative development.",
          scalability_factors: "Successful elements can be systematically replicated across campaigns."
        }
      }
    }
  }

  const analysisData = parseAndSetData(marketingAnalysis)
  if (!analysisData) return <div>No analysis data available</div>

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="space-y-8">

        {/* Top Row - Full Width */}
        {/* Overall Impression Testimonial */}
        {analysisData.gut_reaction && (
          <OverallImpressionTestimonial impression={analysisData.gut_reaction} />
        )}

        <PerformanceScorecard scorecard={analysisData.scorecard} />

        {/* 3rd Row - What Worked & What Didn't */}
        {/* 4th Row - Framework Analysis and Central Insight */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 items-start">
          {/* Framework Analysis */}
          {analysisData.system_signals?.framework_analysis && (
            <div className="lg:col-span-2">
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl p-6 border-l-4 border-purple-500 shadow-sm hover:shadow-md transition-shadow duration-200 h-64">
                <div className="p-6 h-full flex flex-col">
                  <div className="flex items-center space-x-3 mb-4">
                    <svg className="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                    <h3 className="font-semibold text-lg text-purple-900">Primary Framework</h3>
                  </div>
                  <div className="flex-1 pl-8 flex flex-col">
                    <div className="flex-1 overflow-hidden">
                      <p className="text-purple-800 leading-relaxed">
                        {analysisData.system_signals.framework_analysis.primary_framework}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Right Column */}
          <div className="lg:col-span-2 space-y-6">
            {/* Central Insight */}
            {analysisData.lightning_round?.central_insight && (
              <div className="bg-gradient-to-r from-blue-50 to-green-50 rounded-xl p-6 border-l-4 border-blue-500 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="p-6 h-full flex flex-col">
                    <div className="flex items-center space-x-3 mb-4">
                      <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      <h3 className="font-semibold text-lg text-blue-900">The Central Insight</h3>
                    </div>
                    <div className="flex-1 pl-8 flex flex-col">
                      <div className="flex-1 overflow-hidden">
                        <p className="text-blue-800 leading-relaxed">
                          {analysisData.lightning_round.central_insight}
                        </p>
                      </div>
                    </div>
                  </div>
              </div>
            )}

            {/* What Worked Like Gangbusters */}
            {analysisData.veteran_verdict?.what_worked_gangbusters && (
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border-l-4 border-green-500 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="p-6 h-full flex flex-col">
                    <div className="flex items-center space-x-3 mb-4">
                      <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <h3 className="font-semibold text-lg text-green-900">What Works Well</h3>
                    </div>
                    <div className="flex-1 pl-8 flex flex-col">
                      <div className="flex-1 overflow-hidden">
                        <p className="text-green-800 leading-relaxed">
                          {Array.isArray(analysisData.veteran_verdict.what_worked_gangbusters) 
                            ? analysisData.veteran_verdict.what_worked_gangbusters.join('. ') 
                            : analysisData.veteran_verdict.what_worked_gangbusters}
                        </p>
                      </div>
                    </div>
                  </div>
              </div>
            )}

            {/* What Missed the Mark */}
            {analysisData.veteran_verdict?.what_missed_mark && (
              <div className="bg-gradient-to-r from-red-50 to-orange-50 rounded-xl p-6 border-l-4 border-red-500 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="p-6 h-full flex flex-col">
                    <div className="flex items-center space-x-3 mb-4">
                      <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <h3 className="font-semibold text-lg text-red-900">What Missed the Mark</h3>
                    </div>
                    <div className="flex-1 pl-8 flex flex-col">
                      <div className="flex-1 overflow-hidden">
                        <p className="text-red-800 leading-relaxed">
                          {Array.isArray(analysisData.veteran_verdict.what_missed_mark) 
                            ? analysisData.veteran_verdict.what_missed_mark.join('. ') 
                            : analysisData.veteran_verdict.what_missed_mark}
                        </p>
                      </div>
                    </div>
                  </div>
              </div>
            )}
          </div>
        </div>

        {/* Professional Breakdown Section */}
        {analysisData.professional_breakdown && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Professional Breakdown</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Hook Strategy */}
              {analysisData.professional_breakdown.hook_strategy && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Zap className="w-5 h-5 mr-2 text-blue-600" />
                    The Hook Strategy
                  </h3>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-blue-800 text-sm">{analysisData.professional_breakdown.hook_strategy.verdict_and_analysis}</p>
                  </div>
                </div>
              )}

              {/* Narrative Architecture */}
              {analysisData.professional_breakdown.narrative_architecture && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Film className="w-5 h-5 mr-2 text-purple-600" />
                    Narrative Architecture
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(analysisData.professional_breakdown.narrative_architecture).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-purple-900 mb-2 text-sm capitalize">{key.replace('_', ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Emotional Journey */}
              {analysisData.professional_breakdown.emotional_journey && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Heart className="w-5 h-5 mr-2 text-red-600" />
                    The Emotional Journey
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(analysisData.professional_breakdown.emotional_journey).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-red-900 mb-2 text-sm capitalize">{key.replace('_', ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Business Integration */}
              {analysisData.professional_breakdown.business_integration && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Target className="w-5 h-5 mr-2 text-green-600" />
                    Business Integration
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(analysisData.professional_breakdown.business_integration).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-green-900 mb-2 text-sm capitalize">{key.replace('_', ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Craft Analysis Section */}
        {analysisData.craft_analysis && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Craft Analysis</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Visual Storytelling */}
              {analysisData.craft_analysis.visual_storytelling && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Camera className="w-5 h-5 mr-2 text-blue-600" />
                    Visual Storytelling
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(analysisData.craft_analysis.visual_storytelling).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-blue-900 mb-2 text-sm capitalize">{key.replace('_', ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Script & Messaging */}
              {analysisData.craft_analysis.script_messaging && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <MessageSquare className="w-5 h-5 mr-2 text-purple-600" />
                    Script & Messaging
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(analysisData.craft_analysis.script_messaging).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-purple-900 mb-2 text-sm capitalize">{key.replace('_', ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Audio Landscape */}
              {analysisData.craft_analysis.audio_landscape && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Headphones className="w-5 h-5 mr-2 text-green-600" />
                    Audio Landscape
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(analysisData.craft_analysis.audio_landscape).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-green-900 mb-2 text-sm capitalize">{key.replace('_', ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Strategic Deep Dive Section */}
        {analysisData.strategic_deep_dive && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Strategic Deep Dive</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Target Audience Read */}
              {analysisData.strategic_deep_dive.target_audience_read && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Users className="w-5 h-5 mr-2 text-blue-600" />
                    Target Audience Read
                  </h3>
                  <p className="text-gray-700 text-sm leading-relaxed">{analysisData.strategic_deep_dive.target_audience_read}</p>
                </div>
              )}

              {/* Competitive Context */}
              {analysisData.strategic_deep_dive.competitive_context && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Trophy className="w-5 h-5 mr-2 text-purple-600" />
                    Competitive Context
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(analysisData.strategic_deep_dive.competitive_context).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-purple-900 mb-2 text-sm capitalize">{key.replace('_', ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Cultural Relevance */}
              {analysisData.strategic_deep_dive.cultural_relevance && (
                <div className="bg-white border border-gray-200 rounded-lg p-6 md:col-span-2">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Globe className="w-5 h-5 mr-2 text-green-600" />
                    Cultural Relevance
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.entries(analysisData.strategic_deep_dive.cultural_relevance).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-green-900 mb-2 text-sm capitalize">{key.replace('_', ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Pattern Recognition & Predictions Section */}
        {(analysisData.system_signals?.pattern_recognition || analysisData.system_signals?.prediction_factors) && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Advanced Analysis</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Pattern Recognition */}
              {analysisData.system_signals?.pattern_recognition && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <HashIcon className="w-5 h-5 mr-2 text-orange-600" />
                    Pattern Recognition
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(analysisData.system_signals.pattern_recognition).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-orange-900 mb-2 text-sm capitalize">{key.replace('_', ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Performance Predictions */}
              {analysisData.system_signals?.prediction_factors && (
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <TrendingUp className="w-5 h-5 mr-2 text-blue-600" />
                    Performance Predictions
                  </h3>
                  <div className="space-y-3">
                    {Object.entries(analysisData.system_signals.prediction_factors).map(([key, value]) => (
                      <div key={key}>
                        <h4 className="font-semibold text-blue-900 mb-2 text-sm capitalize">{key.replace('_', ' ')}:</h4>
                        <p className="text-gray-700 text-sm leading-relaxed">{String(value)}</p>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Pitfalls Identified */}
        {analysisData.pitfalls_identified && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Pitfalls Identified</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {analysisData.pitfalls_identified.strategic_pitfalls && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-red-900 mb-4 flex items-center">
                    <Target className="w-5 h-5 mr-2 text-red-600" />
                    Strategic Pitfalls
                  </h3>
                  <p className="text-red-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.strategic_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.audience_pitfalls && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-orange-900 mb-4 flex items-center">
                    <Users className="w-5 h-5 mr-2 text-orange-600" />
                    Audience Pitfalls
                  </h3>
                  <p className="text-orange-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.audience_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.creative_pitfalls && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-yellow-900 mb-4 flex items-center">
                    <Lightbulb className="w-5 h-5 mr-2 text-yellow-600" />
                    Creative Pitfalls
                  </h3>
                  <p className="text-yellow-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.creative_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.messaging_pitfalls && (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-purple-900 mb-4 flex items-center">
                    <MessageSquare className="w-5 h-5 mr-2 text-purple-600" />
                    Messaging Pitfalls
                  </h3>
                  <p className="text-purple-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.messaging_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.ethical_cultural_pitfalls && (
                <div className="bg-pink-50 border border-pink-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-pink-900 mb-4 flex items-center">
                    <AlertTriangle className="w-5 h-5 mr-2 text-pink-600" />
                    Ethical & Cultural Pitfalls
                  </h3>
                  <p className="text-pink-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.ethical_cultural_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.media_placement_pitfalls && (
                <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-indigo-900 mb-4 flex items-center">
                    <Monitor className="w-5 h-5 mr-2 text-indigo-600" />
                    Media & Placement Pitfalls
                  </h3>
                  <p className="text-indigo-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.media_placement_pitfalls}</p>
                </div>
              )}
              {analysisData.pitfalls_identified.competitive_market_pitfalls && (
                <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                    <Trophy className="w-5 h-5 mr-2 text-gray-600" />
                    Competitive & Market Pitfalls
                  </h3>
                  <p className="text-gray-800 text-sm leading-relaxed">{analysisData.pitfalls_identified.competitive_market_pitfalls}</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Improvement Signals */}
        {analysisData.system_signals?.improvement_signal_specifics?.issues?.length > 0 && (
          <div className="bg-white rounded-xl p-6 shadow-sm">
            <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
              <Settings className="w-6 h-6 mr-2 text-orange-600" />
              Improvement Signals Detected
            </h2>
            {analysisData.system_signals.improvement_signal_specifics.issues.map((signal: any, index: number) => (
              <div key={index} className="bg-orange-50 p-5 rounded-lg border border-orange-200 mb-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="font-semibold text-orange-900">{signal.element}</h3>
                  <span className="text-sm font-medium text-orange-700 bg-orange-200 px-2 py-1 rounded">Needs Improvement</span>
                </div>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="font-medium text-orange-900">Specific Issue:</span>
                    <p className="text-orange-800 mt-1">{signal.specific_issue}</p>
                  </div>
                  <div>
                    <span className="font-medium text-orange-900">Root Cause:</span>
                    <p className="text-orange-800 mt-1">{signal.root_cause}</p>
                  </div>
                  <div>
                    <span className="font-medium text-orange-900">Improvement Direction:</span>
                    <p className="text-orange-800 mt-1">{signal.improvement_direction}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* The Killer Insight */}
        {analysisData.veteran_verdict?.killer_insight && (
          <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-xl p-6 border-l-4 border-indigo-500">
            <h2 className="text-xl font-bold text-indigo-900 mb-4 flex items-center">
              <Brain className="w-6 h-6 mr-2" />
              The Killer Insight
            </h2>
            <p className="text-indigo-800 font-medium leading-relaxed text-lg">
              {analysisData.veteran_verdict.killer_insight}
            </p>
          </div>
        )}

        {/* If This Was Your Campaign */}
        {analysisData.veteran_verdict?.if_this_was_your_campaign && (
          <div className="bg-white rounded-xl p-6 shadow-sm">
            <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
              <Lightbulb className="w-6 h-6 mr-2 text-yellow-600" />
              If This Was Your Campaign
            </h2>
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-blue-800 text-sm">{analysisData.veteran_verdict.if_this_was_your_campaign}</p>
            </div>
          </div>
        )}

        {/* The Big Learning */}
        {analysisData.veteran_verdict?.big_learning && (
          <div className="bg-gradient-to-r from-gray-900 to-gray-800 text-white rounded-xl p-6">
            <h2 className="text-xl font-bold mb-4 flex items-center">
              <Award className="w-6 h-6 mr-2" />
              The Big Learning
            </h2>
            <p className="font-medium leading-relaxed text-lg">
              {analysisData.veteran_verdict.big_learning}
            </p>
          </div>
        )}

        {/* Citations */}
        {analysisData?.citations && analysisData.citations.length > 0 && (
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ExternalLink className="w-5 h-5" />
                  Sources & Citations
                </CardTitle>
                <CardDescription>
                  Research sources used to enhance this analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analysisData.citations.map((citation: any, index: number) => (
                    <div key={index} className="flex items-start gap-3 p-3 bg-gray-50 rounded-lg">
                      <div className="flex-shrink-0 w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <a 
                          href={citation.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 font-medium hover:underline"
                        >
                          {citation.title}
                        </a>
                        <p className="text-sm text-gray-500 mt-1 break-all">
                          {citation.url}
                        </p>
                      </div>
                      <ExternalLink className="w-4 h-4 text-gray-400 flex-shrink-0" />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Generated Content */}
        <GeneratedContentSections
          marketingCopy={marketingCopy}
          socialMediaPosts={socialMediaPosts}
          marketingScorecard={marketingScorecard}
          seoKeywords={seoKeywords}
          contentSuggestions={contentSuggestions}
        />
      </div>
    </div>
  )
}