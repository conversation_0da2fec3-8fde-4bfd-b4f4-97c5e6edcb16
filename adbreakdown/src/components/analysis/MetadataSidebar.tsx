'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Info } from 'lucide-react'

interface MetadataSidebarProps {
  parsedData: any
  youtubeMetadata?: any
  videoMetadata?: any
}

export default function MetadataSidebar({ parsedData, youtubeMetadata, videoMetadata }: MetadataSidebarProps) {
  const metadata = parsedData?.metadata

  const formatLabel = (key: string) => {
    if (key === 'ad_title') return 'Title'
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  const renderMetadata = (data: any) => {
    return Object.entries(data)
      .filter(([, value]) => value && value !== 'None' && value !== 'N/A' && value !== '')
      .map(([key, value]) => (
        <div key={key} className="grid grid-cols-[auto_1fr] gap-x-2 text-sm">
          <span className="font-medium text-gray-600">{formatLabel(key)}:</span>
          <span className="text-gray-900">{String(value)}</span>
        </div>
      ));
  };

  if (!metadata) {
    // Fallback to existing metadata structure if available
    const fallbackData = {
      brand: videoMetadata?.inferredBrandName,
      duration: videoMetadata?.duration,
      channel: youtubeMetadata?.channelTitle,
    };

    return (
      <Card className="shadow-sm border border-gray-200 bg-white">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm md:text-base font-medium text-gray-900 flex items-center">
            <Info className="w-4 h-4 mr-2 text-gray-500" />
            Ad Details
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-2">
            {renderMetadata(fallbackData)}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm md:text-base font-medium text-gray-900 flex items-center">
          <Info className="w-4 h-4 mr-2 text-gray-500" />
          Ad Details
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2">
          {renderMetadata(metadata)}
        </div>
      </CardContent>
    </Card>
  )
}