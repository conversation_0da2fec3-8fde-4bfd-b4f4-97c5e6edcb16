'use client'

import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { BarChart3, Info } from 'lucide-react'
import RatingLogicModal from '@/components/analysis/RatingLogicModal'

interface ScorecardSidebarProps {
  parsedData: any
}

export default function ScorecardSidebar({ parsedData }: ScorecardSidebarProps) {
  const scorecard = parsedData?.executive_briefing?.scorecard
  const [showRatingLogicModal, setShowRatingLogicModal] = React.useState(false)
  const [showFullJustification, setShowFullJustification] = React.useState(false)

  if (!scorecard) {
    return null
  }

  const formatScoreLabel = (key: string) => {
    return key
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
  }

  const individualScores = Object.entries(scorecard).filter(([key]) => key !== 'overall_impact_score');

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">

      <CardContent className="pt-4">

                
        {/* Disclaimer / Experimental Tag */}
        <div className="flex items-center text-xs mb-2 text-blue-600 cursor-pointer hover:text-blue-800 w-fit"
             onClick={() => setShowRatingLogicModal(true)}>
          <span className="font-base">Experimental</span>
          <Info className="w-3 h-3 ml-1 text-blue-600" />
        </div>

        {/* Overall Score - Prominent Display */}
        {scorecard.overall_impact_score && (
          <div className="text-center mb-3 pb-3 border-b border-gray-200">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1">
              {scorecard.overall_impact_score.score}
              <span className="text-lg text-gray-500">/10</span>
            </div>
            <div className="text-xs font-medium text-gray-600 mb-2">Overall Score</div>
            <div className="text-xs text-gray-500 leading-relaxed text-left">
              {scorecard.overall_impact_score.justification.length > 200 ? (
                <div>
                  {showFullJustification ? (
                    <div>
                      {scorecard.overall_impact_score.justification}
                      <button
                        onClick={() => setShowFullJustification(false)}
                        className="text-blue-600 hover:text-blue-800 font-medium ml-1"
                      >
                        Read Less
                      </button>
                    </div>
                  ) : (
                    <div className="relative">
                      <div className="line-clamp-5">
                        {scorecard.overall_impact_score.justification}
                      </div>
                      <div className="absolute bottom-0 right-0 bg-white pl-2">
                        <button
                          onClick={() => setShowFullJustification(true)}
                          className="text-blue-600 hover:text-blue-800 font-medium"
                        >
                          ...Read More
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div>{scorecard.overall_impact_score.justification}</div>
              )}
            </div>
          </div>
        )}

        
        {/* Individual Scores */}
        <div className="space-y-1">
          {individualScores.map(([key, value]) => (
            <div key={key} className="group relative">
              <div className="flex justify-between items-center py-0 px-3 rounded-lg hover:bg-gray-50 transition-colors cursor-help">
                <span className="text-sm font-medium text-gray-700">{formatScoreLabel(key)}</span>
                <div className="flex items-center">
                  <span className="text-base font-semibold text-gray-900">{(value as any).score}</span>
                  <span className="text-xs text-gray-500">/10</span>
                  <Info className="w-3 h-3 text-gray-400 ml-3" />
                </div>
              </div>
              {/* Custom Tooltip */}
              <div className="absolute left-0 top-full mt-2 w-full bg-white border border-gray-200 text-gray-700 text-xs rounded-lg px-3 py-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-10 pointer-events-none shadow-lg">
                {(value as any).justification}
              </div>
            </div>
          ))}
        </div>
      </CardContent>

      <RatingLogicModal 
        isOpen={showRatingLogicModal}
        onClose={() => setShowRatingLogicModal(false)}
      />
    </Card>
  )
}