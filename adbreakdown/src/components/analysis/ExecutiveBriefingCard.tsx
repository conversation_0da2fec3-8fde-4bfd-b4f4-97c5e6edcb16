'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import Image from 'next/image'

interface ExecutiveBriefingCardProps {
  parsedData: any
}

export default function ExecutiveBriefingCard({ parsedData }: ExecutiveBriefingCardProps) {
  if (!parsedData?.executive_briefing) {
    return null
  }

  const { gut_reaction } = parsedData.executive_briefing

  return (
    <Card className="shadow-sm border border-gray-200 bg-white">
      <CardContent className="p-6">
        {/* Header with AI Expert */}
        <div className="flex items-center space-x-3 mb-6">
          <Image
            src="/Gemini_Generated_Image_bzln87bzln87bzln.png"
            alt="AI Expert Avatar"
            width={48}
            height={48}
            className="w-12 h-12 rounded-full object-cover"
          />
          <div>
            <h2 className="text-base md:text-lg font-medium text-gray-900 mb-1">AI Expert</h2>
            <p className="text-gray-600 text-sm">With instincts of a marketer</p>
          </div>
        </div>
        
        {/* Gut Reaction Quote */}
        {gut_reaction && (
          <div>
              <blockquote className="text-gray-700 italic leading-relaxed">
                &ldquo;{gut_reaction}&rdquo;
              </blockquote>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
