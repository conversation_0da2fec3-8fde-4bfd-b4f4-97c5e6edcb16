'use client'

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Card } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  Play, 
  Clock, 
  Users, 
  Lock, 
  Heart,
  Share2,
  MoreHorizontal,
  Star
} from 'lucide-react'

interface AdTileProps {
  id: string
  slug?: string | null
  title: string
  inferred_brand: string
  thumbnail_url: string
  video_thumbnail_url?: string
  duration_seconds: number
  overall_sentiment: number
  created_at: string
  analysis_completed_at?: string
  youtube_video_id: string
  is_public: boolean
  showcase?: boolean
  view_count?: number
  like_count?: number
  featured?: boolean
  author_name?: string
  className?: string
  showPrivacyBadge?: boolean
}

export default function AdTile({
  id,
  slug,
  title,
  inferred_brand,
  thumbnail_url,
  video_thumbnail_url,
  duration_seconds,
  overall_sentiment,
  created_at,
  analysis_completed_at,
  is_public,
  showcase = false,
  view_count,
  like_count,
  featured = false,
  author_name,
  className = "",
  showPrivacyBadge = true
}: AdTileProps) {
  const formatDuration = (seconds: number) => {
    if (!seconds) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getSentimentColor = (sentiment: number) => {
    if (sentiment >= 0.7) return 'text-emerald-600 bg-emerald-50 border-emerald-200'
    if (sentiment >= 0.4) return 'text-amber-600 bg-amber-50 border-amber-200'
    return 'text-rose-600 bg-rose-50 border-rose-200'
  }


  const imageUrl = video_thumbnail_url || thumbnail_url || 'https://placehold.co/400x225/f1f5f9/64748b?text=Video+Thumbnail'

  return (
    <Card className={`group relative overflow-hidden border-0 shadow-sm hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-white ${className}`}>
      {/* Featured Badge */}
      {featured && (
        <div className="absolute top-3 left-3 z-20">
          <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 shadow-md">
            <Star className="h-3 w-3 mr-1" />
            Featured
          </Badge>
        </div>
      )}

      {/* Privacy/Community Badge */}
      {showPrivacyBadge && (
        <div className="absolute top-3 right-3 z-20">
          <Badge 
            variant="secondary" 
            className={`${is_public 
              ? 'bg-emerald-100 text-emerald-700 border-emerald-200' 
              : 'bg-slate-100 text-slate-700 border-slate-200'
            } shadow-sm`}
          >
            {is_public ? (
              <>
                <Users className="h-3 w-3 mr-1" />
                Public
              </>
            ) : (
              <>
                <Lock className="h-3 w-3 mr-1" />
                Private
              </>
            )}
          </Badge>
        </div>
      )}

      <Link href={`/ad/${slug || id}`} className="block">
        {/* Hero Image Section */}
        <div className="relative aspect-video bg-gradient-to-br from-slate-100 to-slate-200 overflow-hidden">
          <Image
            src={imageUrl}
            alt={title}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-500"
            sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
          />
          
          {/* Overlay Gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          
          {/* Play Button Overlay */}
          <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="bg-white/90 backdrop-blur-sm rounded-full p-4 shadow-lg transform scale-90 group-hover:scale-100 transition-transform duration-300">
              <Play className="h-6 w-6 text-slate-700 ml-0.5" />
            </div>
          </div>

          {/* Duration Badge */}
          <div className="absolute bottom-3 right-3">
            <Badge className="bg-black/70 text-white border-0 backdrop-blur-sm">
              <Clock className="h-3 w-3 mr-1" />
              {formatDuration(duration_seconds)}
            </Badge>
          </div>
        </div>

        {/* Content Section - Only 2 lines */}
        <div className="p-4">
          {/* Line 1: Title with overflow hidden */}
          <div className="mb-2">
            <h3 className="text-md font-semibold truncate group-hover:text-blue-600 transition-colors duration-200 overflow-hidden">
              {title}
            </h3>
          </div>
          
          {/* Line 2: Brand Badge + Actions in one line */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 min-w-0 flex-1">
              {/* Brand Badge */}
              {inferred_brand && inferred_brand !== 'N/A' && (
                <Badge variant="outline" className="text-xs font-medium flex-shrink-0">
                  {inferred_brand}
                </Badge>
              )}
              
              {/* Sentiment Score - compact */}
              {overall_sentiment !== null && overall_sentiment !== undefined && (
                <Badge 
                  variant="secondary" 
                  className={`text-xs font-medium flex-shrink-0 ${getSentimentColor(overall_sentiment)}`}
                >
                  {Math.round(overall_sentiment * 100)}%
                </Badge>
              )}
            </div>
            
            {/* Action Icons: Heart, Share, 3 dots menu */}
            <div className="flex items-center gap-1 flex-shrink-0">
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-7 w-7 p-0 text-slate-600 hover:text-rose-600 hover:bg-rose-50"
                onClick={(e) => {
                  e.preventDefault()
                  // Handle like action
                }}
              >
                <Heart className="h-3.5 w-3.5" />
              </Button>
              
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-7 w-7 p-0 text-slate-600 hover:text-blue-600 hover:bg-blue-50"
                onClick={(e) => {
                  e.preventDefault()
                  // Handle share action
                }}
              >
                <Share2 className="h-3.5 w-3.5" />
              </Button>
              
              <Button 
                variant="ghost" 
                size="sm" 
                className="h-7 w-7 p-0 text-slate-600 hover:bg-slate-100"
                onClick={(e) => {
                  e.preventDefault()
                  // Handle more actions
                }}
              >
                <MoreHorizontal className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>
        </div>
      </Link>
    </Card>
  )
}