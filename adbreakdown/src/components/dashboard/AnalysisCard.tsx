'use client'

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, Clock, AlertCircle, PlayCircle, TrendingUp } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'

interface Analysis {
  id: string
  slug?: string | null
  youtube_url: string
  status: string
  title: string
  inferred_brand: string
  duration: number
  thumbnail_url: string
  overall_sentiment: number
  created_at: string
}

interface AnalysisCardProps {
  analysis: Analysis
}

export default function AnalysisCard({ analysis }: AnalysisCardProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'processing':
        return <Clock className="h-4 w-4 text-blue-600" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatDuration = (seconds: number | null) => {
    if (!seconds) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getSentimentColor = (sentiment: number) => {
    if (sentiment >= 0.7) return 'text-green-600'
    if (sentiment >= 0.4) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <Link href={`/ad/${analysis.id}`}>
      <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group border hover:border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            {/* Thumbnail */}
            <div className="w-24 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0 group-hover:shadow-md transition-shadow">
              {analysis.thumbnail_url ? (
                <Image 
                  src={analysis.thumbnail_url} 
                  alt={analysis.title || 'Video thumbnail'}
                  width={96}
                  height={64}
                  className="w-full h-full object-cover"
                  sizes="96px"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
                  <PlayCircle className="h-8 w-8 text-gray-500" />
                </div>
              )}
            </div>
            
            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between gap-2 mb-2">
                <h3 className="font-semibold text-gray-900 truncate group-hover:text-blue-700 transition-colors">
                  {analysis.title || 'Untitled Video'}
                </h3>
                <Badge 
                  variant="outline" 
                  className={`${getStatusColor(analysis.status)} flex-shrink-0`}
                >
                  <span className="flex items-center gap-1">
                    {getStatusIcon(analysis.status)}
                    {analysis.status}
                  </span>
                </Badge>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                <span className="font-medium">{analysis.inferred_brand || 'Unknown Brand'}</span>
                <span>{formatDuration(analysis.duration)}</span>
                <span>{new Date(analysis.created_at).toLocaleDateString()}</span>
              </div>
              
              {/* Additional info for completed analyses */}
              {analysis.status === 'completed' && analysis.overall_sentiment !== null && (
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-1">
                    <TrendingUp className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-600">Sentiment:</span>
                    <span className={`text-sm font-semibold ${getSentimentColor(analysis.overall_sentiment)}`}>
                      {(analysis.overall_sentiment * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
              )}
            </div>
            
            {/* Sentiment Score (for completed analyses) */}
            {analysis.overall_sentiment !== null && analysis.status === 'completed' && (
              <div className="text-right flex-shrink-0">
                <div className="text-xs text-gray-500 mb-1">Overall Score</div>
                <div className={`text-2xl font-bold ${getSentimentColor(analysis.overall_sentiment)}`}>
                  {(analysis.overall_sentiment * 100).toFixed(0)}%
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  )
}
