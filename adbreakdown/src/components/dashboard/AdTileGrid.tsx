'use client'

import React from 'react'
import AdTile from './AdTile'
import { Skeleton } from '@/components/ui/skeleton'
import { Card, CardContent, CardHeader } from '@/components/ui/card'

interface Analysis {
  id: string
  slug?: string | null
  title: string
  inferred_brand: string
  thumbnail_url: string
  video_thumbnail_url?: string
  duration_seconds: number
  overall_sentiment: number
  created_at: string
  analysis_completed_at?: string
  youtube_video_id: string
  is_public: boolean
  showcase?: boolean
  view_count?: number
  like_count?: number
  featured?: boolean
  author_name?: string
}

interface AdTileGridProps {
  analyses: Analysis[]
  loading?: boolean
  columns?: 'auto' | 1 | 2 | 3 | 4 | 5 | 6
  showFeatured?: boolean
  showPrivacyBadge?: boolean
  className?: string
}

export default function AdTileGrid({ 
  analyses, 
  loading = false, 
  columns = 'auto',
  showFeatured = false,
  showPrivacyBadge = true,
  className = ""
}: AdTileGridProps) {
  const getGridColumns = () => {
    if (columns === 'auto') {
      return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
    }
    if (columns === 3) {
      return 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
    }
    return `grid-cols-${columns}`
  }

  const sortedAnalyses = showFeatured 
    ? [...analyses].sort((a, b) => {
        // Featured items first
        if (a.featured && !b.featured) return -1
        if (!a.featured && b.featured) return 1
        
        // Then by sentiment score (higher first)
        if (b.overall_sentiment !== a.overall_sentiment) {
          return (b.overall_sentiment || 0) - (a.overall_sentiment || 0)
        }
        
        // Finally by creation date (newer first)
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      })
    : analyses

  if (loading) {
    return (
      <div className={`grid ${getGridColumns()} gap-6 ${className}`}>
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className="overflow-hidden">
            {/* Thumbnail Skeleton */}
            <Skeleton className="aspect-video w-full" />
            
            {/* Content Skeleton */}
            <CardHeader className="space-y-3">
              <Skeleton className="h-5 w-full" />
              <Skeleton className="h-4 w-3/4" />
              <div className="flex justify-between">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-6 w-16" />
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-16" />
                </div>
                <Skeleton className="h-3 w-32" />
              </div>
            </CardContent>
            
            {/* Footer Skeleton */}
            <div className="p-4 pt-0">
              <div className="flex justify-between items-center">
                <div className="flex gap-2">
                  <Skeleton className="h-8 w-12" />
                  <Skeleton className="h-8 w-8" />
                </div>
                <Skeleton className="h-8 w-8" />
              </div>
            </div>
          </Card>
        ))}
      </div>
    )
  }

  if (analyses.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-24 h-24 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mx-auto mb-4">
          <div className="w-12 h-12 bg-slate-300 rounded-full flex items-center justify-center">
            <div className="w-6 h-6 bg-slate-400 rounded-full"></div>
          </div>
        </div>
        <h3 className="text-lg font-semibold text-slate-900 mb-2">No analyses found</h3>
        <p className="text-slate-600 max-w-sm mx-auto">
          There are no video analyses to display at the moment. Check back later or create your first analysis.
        </p>
      </div>
    )
  }

  return (
    <div className={`grid ${getGridColumns()} gap-6 ${className}`}>
      {sortedAnalyses.map((analysis, index) => (
        <AdTile
          key={analysis.id}
          id={analysis.id}
          slug={analysis.slug || analysis.id}
          title={analysis.title}
          inferred_brand={analysis.inferred_brand}
          thumbnail_url={analysis.thumbnail_url}
          video_thumbnail_url={analysis.video_thumbnail_url}
          duration_seconds={analysis.duration_seconds}
          overall_sentiment={analysis.overall_sentiment}
          created_at={analysis.created_at}
          analysis_completed_at={analysis.analysis_completed_at}
          youtube_video_id={analysis.youtube_video_id}
          is_public={analysis.is_public}
          showcase={analysis.showcase}
          view_count={analysis.view_count}
          like_count={analysis.like_count}
          featured={analysis.featured || (showFeatured && index < 2)} // Mark first 2 as featured if showFeatured is true
          author_name={analysis.author_name}
          showPrivacyBadge={showPrivacyBadge}
          className={index === 0 && showFeatured ? 'md:col-span-2' : ''} // Make first item larger if featured
        />
      ))}
    </div>
  )
}