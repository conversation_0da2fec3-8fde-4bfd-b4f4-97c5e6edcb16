'use client'

import React from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, User } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import { useCredits } from '@/hooks/useCredits'
import { UserButton } from '@clerk/nextjs'


interface PageHeaderProps {
  title?: string
  subtitle?: string
  showBackButton?: boolean
  backButtonText?: string
  backButtonHref?: string
  children?: React.ReactNode
  className?: string
}

export function PageHeader({
  title = 'AdBreakdown',
  subtitle,
  showBackButton = false,
  backButtonText = 'Back',
  backButtonHref = '/dashboard',
  children,
  className = ''
}: PageHeaderProps) {
  const { isAuthenticated, user } = useAuth()
  const { profile } = useCredits()

  return (
    <header className={`border-b bg-white ${className}`}>
      <div className="container mx-auto px-4 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {showBackButton && (
              <Link href={backButtonHref}>
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  {backButtonText}
                </Button>
              </Link>
            )}
            
            {/* Logo and Brand */}
            <Link href="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity">
              <Image
                src="/logo.png"
                alt="AdBreakdown Logo"
                width={32}
                height={32}
                className="w-8 h-8 object-contain"
                priority
              />
              <div className="flex flex-col">
                <span className="font-bold text-lg text-gray-900">{title}</span>
                {subtitle && (
                  <span className="text-sm text-gray-600">{subtitle}</span>
                )}
              </div>
            </Link>
          </div>

          {/* Right side content */}
          <div className="flex items-center gap-3">
            {/* Custom children content */}
            {children}
            
            {/* User info and credits */}
            {isAuthenticated && profile && (
              <>
               
                {profile.credits_remaining < 40 && (
                  <Link href="/billing">
                    <Button size="sm" variant="outline">
                      Upgrade
                    </Button>
                  </Link>
                )}
              </>
            )}
            
            {/* User profile */}
            <UserButton afterSignOutUrl="/" />
     
          </div>
        </div>
      </div>
    </header>
  )
}

export default PageHeader