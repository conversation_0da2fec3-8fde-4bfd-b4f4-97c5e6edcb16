'use client'

import React, { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ChevronLeft, ChevronRight, Search, Plus } from 'lucide-react'
import { useAuth } from '@/hooks/useAuth'
import AdTileGrid from '@/components/dashboard/AdTileGrid'

interface PublicAnalysis {
  id: string
  slug: string
  title: string
  inferred_brand: string
  thumbnail_url: string
  video_thumbnail_url: string
  duration_seconds: number
  duration_formatted: string
  overall_sentiment: number
  created_at: string
  analysis_completed_at: string
  youtube_video_id: string
  is_public: boolean
  showcase?: boolean
  view_count?: number
}

interface PaginationInfo {
  page: number
  limit: number
  total: number
  totalPages: number
  hasNext: boolean
  hasPrev: boolean
}

interface AdLibraryClientProps {
  initialAnalyses: PublicAnalysis[]
  initialPagination: PaginationInfo
  initialFilters: {
    brands: string[]
    celebrities: string[]
    years: string[]
  }
}

export default function AdLibraryClient({ 
  initialAnalyses, 
  initialPagination, 
  initialFilters 
}: AdLibraryClientProps) {
  const { isAuthenticated } = useAuth()
  
  // Public Analyses State
  const [analyses, setAnalyses] = useState<PublicAnalysis[]>(initialAnalyses)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [pagination, setPagination] = useState<PaginationInfo>(initialPagination)

  // Filters and Search
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState<'recent' | 'popular' | 'sentiment'>('recent')
  const [selectedBrand, setSelectedBrand] = useState<string>('all')
  const [selectedCelebrity, setSelectedCelebrity] = useState<string>('all')
  const [selectedYear, setSelectedYear] = useState<string>('all')
  const [selectedDuration, setSelectedDuration] = useState<string>('all')
  
  // Filter options
  const [availableBrands] = useState<string[]>(initialFilters.brands)
  const [availableCelebrities] = useState<string[]>(initialFilters.celebrities)
  const [availableYears] = useState<string[]>(initialFilters.years)
  const [availableDurations] = useState([
    { value: 'short', label: 'Short (≤30s)' },
    { value: 'medium', label: 'Medium (31-60s)' },
    { value: 'long', label: 'Long (60s+)' }
  ])

  const fetchAnalyses = async (
    page: number = 1, 
    search: string = '', 
    sort: string = 'recent', 
    brand: string = 'all',
    celebrity: string = 'all',
    year: string = 'all',
    duration: string = 'all'
  ) => {
    setLoading(true)
    setError('')
    
    try {
      const searchParams = new URLSearchParams({
        page: page.toString(),
        limit: '12',
        ...(search && { search }),
        sort,
        ...(brand && brand !== 'all' && { brand }),
        ...(celebrity && celebrity !== 'all' && { celebrity }),
        ...(year && year !== 'all' && { year }),
        ...(duration && duration !== 'all' && { duration })
      })
      
      const response = await fetch(`/api/analyses/public?${searchParams}`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch analyses')
      }
      
      const data = await response.json()
      setAnalyses(data.analyses || [])
      setPagination(data.pagination)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analyses')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Only fetch if filters change from initial state
    const hasFilters = searchQuery || sortBy !== 'recent' || selectedBrand !== 'all' || 
                      selectedCelebrity !== 'all' || selectedYear !== 'all' || selectedDuration !== 'all'
    
    if (hasFilters) {
      fetchAnalyses(pagination.page, searchQuery, sortBy, selectedBrand, selectedCelebrity, selectedYear, selectedDuration)
    }
  }, [searchQuery, sortBy, selectedBrand, selectedCelebrity, selectedYear, selectedDuration, pagination.page])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setPagination(prev => ({ ...prev, page: 1 }))
    fetchAnalyses(1, searchQuery, sortBy, selectedBrand, selectedCelebrity, selectedYear, selectedDuration)
  }

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }))
    fetchAnalyses(newPage, searchQuery, sortBy, selectedBrand, selectedCelebrity, selectedYear, selectedDuration)
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  return (
    <>
      {/* Search and Filters */}
      <div className="bg-gray-50 rounded-lg p-6 mb-8">
        <div className="flex flex-wrap items-end gap-4">
          {/* Search Input and Button */}
          <form onSubmit={handleSearch} className="flex gap-2 flex-grow max-w-xs">
            <Input
              type="text"
              placeholder="Search by brand, title, or content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="flex-1"
            />
            <Button type="submit" variant="outline">
              <Search className="h-4 w-4" />
            </Button>
          </form>

          {/* Filters and Sort Dropdowns */}
          <Select
            value={selectedBrand}
            onValueChange={setSelectedBrand}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Brand" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All brands</SelectItem>
              {availableBrands.map((brand) => (
                <SelectItem key={brand} value={brand}>
                  {brand}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={selectedCelebrity}
            onValueChange={setSelectedCelebrity}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Celebrity" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All celebrities</SelectItem>
              {availableCelebrities.map((celebrity) => (
                <SelectItem key={celebrity} value={celebrity}>
                  {celebrity}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={selectedYear}
            onValueChange={setSelectedYear}
          >
            <SelectTrigger className="w-[120px]">
              <SelectValue placeholder="Year" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All years</SelectItem>
              {availableYears.map((year) => (
                <SelectItem key={year} value={year}>
                  {year}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={selectedDuration}
            onValueChange={setSelectedDuration}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Duration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All durations</SelectItem>
              {availableDurations.map((duration) => (
                <SelectItem key={duration.value} value={duration.value}>
                  {duration.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={sortBy}
            onValueChange={(value) => setSortBy(value as 'recent' | 'popular' | 'sentiment')}
          >
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="recent">Most Recent</SelectItem>
              <SelectItem value="popular">Most Popular</SelectItem>
              <SelectItem value="sentiment">Best Rated</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Results Stats */}
        {!loading && (
          <div className="mt-4 pt-4 border-t border-gray-200 flex flex-col gap-2 text-sm text-gray-600">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between">
              <span>
                Showing {analyses.length} of {pagination.total} analyses
              </span>
              {searchQuery && (
                <span>
                  Search results for &quot;{searchQuery}&quot;
                </span>
              )}
            </div>
            
            {/* Active Filters */}
            {(selectedBrand !== 'all' || selectedCelebrity !== 'all' || selectedYear !== 'all' || selectedDuration !== 'all') && (
              <div className="flex flex-wrap gap-2 items-center">
                <span className="text-gray-500">Active filters:</span>
                {selectedBrand !== 'all' && (
                  <Badge variant="secondary" className="text-xs">
                    Brand: {selectedBrand}
                  </Badge>
                )}
                {selectedCelebrity !== 'all' && (
                  <Badge variant="secondary" className="text-xs">
                    Celebrity: {selectedCelebrity}
                  </Badge>
                )}
                {selectedYear !== 'all' && (
                  <Badge variant="secondary" className="text-xs">
                    Year: {selectedYear}
                  </Badge>
                )}
                {selectedDuration !== 'all' && (
                  <Badge variant="secondary" className="text-xs">
                    Duration: {availableDurations.find(d => d.value === selectedDuration)?.label}
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
                  onClick={() => {
                    setSelectedBrand('all')
                    setSelectedCelebrity('all')
                    setSelectedYear('all')
                    setSelectedDuration('all')
                    setSearchQuery('')
                    fetchAnalyses(1, '', sortBy, 'all', 'all', 'all', 'all')
                  }}
                >
                  Clear all filters
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg mb-6">
          {error}
        </div>
      )}

      {/* Analyses Grid */}
      {loading ? (
        <AdTileGrid 
          analyses={[]} 
          loading={true}
          showFeatured={false}
          showPrivacyBadge={false}
          columns={3}
          className="mb-8"
        />
      ) : analyses.length === 0 ? (
        <div className="text-center py-16 bg-gray-50 rounded-lg">
          <div className="max-w-md mx-auto">
            <h3 className="text-lg font-medium mb-2 text-gray-900">
              {searchQuery ? 'No search results found' : 'No analyses found'}
            </h3>
            <p className="text-gray-600 mb-6">
              {searchQuery 
                ? `Try adjusting your search terms or browse all analyses.`
                : 'Be the first to create and share an ad analysis with the community!'
              }
            </p>
            <div className="flex gap-3 justify-center">
              {searchQuery ? (
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setSearchQuery('')
                    setSelectedBrand('all')
                    setSelectedCelebrity('all')
                    setSelectedYear('all')
                    setSelectedDuration('all')
                    fetchAnalyses(1, '', sortBy, 'all', 'all', 'all', 'all')
                  }}
                >
                  Clear Search
                </Button>
              ) : (
                <Link href={isAuthenticated ? "/dashboard" : "/sign-up"}>
                  <Button className="bg-blue-600 hover:bg-blue-700">
                    <Plus className="h-4 w-4 mr-1" />
                    {isAuthenticated ? 'Create Analysis' : 'Get Started'}
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      ) : (
        <>
          <AdTileGrid 
            analyses={analyses} 
            loading={false}
            showFeatured={false}
            showPrivacyBadge={false}
            columns={3}
            className="mb-8"
          />

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="flex items-center justify-center space-x-2 mb-8">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page - 1)}
                disabled={!pagination.hasPrev}
              >
                <ChevronLeft className="h-4 w-4 mr-1" />
                Previous
              </Button>

              <div className="flex items-center space-x-1">
                {[...Array(Math.min(5, pagination.totalPages))].map((_, i) => {
                  const pageNum = pagination.page <= 3 
                    ? i + 1 
                    : pagination.page >= pagination.totalPages - 2
                      ? pagination.totalPages - 4 + i
                      : pagination.page - 2 + i

                  if (pageNum < 1 || pageNum > pagination.totalPages) return null

                  return (
                    <Button
                      key={pageNum}
                      variant={pageNum === pagination.page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(pageNum)}
                      className="w-10"
                    >
                      {pageNum}
                    </Button>
                  )
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handlePageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
              >
                Next
                <ChevronRight className="h-4 w-4 ml-1" />
              </Button>
            </div>
          )}
        </>
      )}

      {/* Call to Action */}
      {!isAuthenticated && analyses.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-3">
            Ready to Analyze Your Own Ads?
          </h3>
          <p className="text-lg text-gray-600 mb-6 max-w-2xl mx-auto">
            Join AdBreakdown to create AI-powered analyses of your video ads, 
            gain actionable insights, and share your findings with the community.
          </p>
          <div className="flex gap-3 justify-center">
            <Link href="/sign-up">
              <Button size="lg" className="bg-blue-600 hover:bg-blue-700">
                Start Free Trial
              </Button>
            </Link>
            <Link href="/featured">
              <Button size="lg" variant="outline">
                View Featured Analysis
              </Button>
            </Link>
          </div>
        </div>
      )}
    </>
  )
}