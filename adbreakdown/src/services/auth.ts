// Authentication service layer (Clerk integration logic)
import { auth, currentUser } from '@clerk/nextjs/server'

export const getAuthenticatedUser = async () => {
  try {
    const user = await currentUser()
    return user
  } catch (error) {
    console.error('Error getting authenticated user:', error)
    return null
  }
}

export const getUserId = async () => {
  const { userId } = await auth()
  return userId
}

export const requireAuth = async () => {
  const { userId } = await auth()
  if (!userId) {
    throw new Error('Unauthorized: User must be signed in')
  }
  return userId
}
