interface VideoMetadata {
  title: string
  thumbnail: string
  duration: string
  inferredBrandName: string
}

interface YouTubeMetadata {
  title: string
  description: string
  channelTitle: string
  publishedAt: string
  viewCount: string
  likeCount: string
  commentCount: string
  tags: string[]
  categoryId: string
  defaultLanguage?: string
  duration: string
  definition: string
}

interface DetailedAnalysisData {
  overallSentiment: number
  emotions: Record<string, number>
  scriptAnalysis: {
    transcript: string
    keyThemes: string[]
    sentimentTimeline: Array<{ time: number; sentiment: number }>
  }
  visualAnalysis: {
    scenes: Array<{ time: number; description: string; objects: string[] }>
    colorPalette: string[]
    visualAppeal: number
  }
  audioAnalysis: {
    musicMood: string
    voiceTone: string
    audioQuality: number
    soundEffects: string[]
  }
  targetingRecommendations: {
    demographics: string[]
    interests: string[]
    behaviors: string[]
  }
  competitorComparison: Array<{ name: string; sentiment: number; effectiveness: number }>
}

interface Report {
  id: string;
  report_types: { name: string };
  content: any;
  status: string;
}

export interface AdAnalysis {
  id: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  video_url: string;
  video_title: string;
  video_thumbnail_url: string;
  video_duration: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'generated';
  overall_sentiment: number;
  emotions: Record<string, number>;
  transcript: string;
  summary: string;
  key_themes: string[];
  inferred_brand: string;
  marketing_analysis: string;
  marketing_copy: string;
  social_media_posts: string;
  marketing_scorecard: string;
  seo_keywords: string;
  content_suggestions: string;
  is_public: boolean;
  slug: string;
  youtube_video_id: string;
  reports?: Report[];
  deciphered_script?: { enhanced_analysis: string; enhanced_analysis_model: string; enhanced_analysis_generated_at: string };
  sentiment_timeline?: Array<{ time: number; sentiment: number }>;
  scenes?: Array<{ time: number; description: string; objects: string[] }>;
  color_palette?: string[];
  visual_appeal?: number;
  music_mood?: string;
  voice_tone?: string;
  audio_quality?: number;
  sound_effects?: string[];
  target_demographics?: string[];
  target_interests?: string[];
  target_behaviors?: string[];
  competitor_comparison?: Array<{ name: string; sentiment: number; effectiveness: number }>;
}
