declare module 'html2pdf.js' {
  interface Html2PdfOptions {
    margin?: number | [number, number, number, number]
    filename?: string
    image?: {
      type?: 'jpeg' | 'png' | 'webp'
      quality?: number
    }
    html2canvas?: {
      scale?: number
      useCORS?: boolean
      width?: number
      windowWidth?: number
    }
    jsPDF?: {
      unit?: 'pt' | 'mm' | 'cm' | 'in'
      format?: 'a0' | 'a1' | 'a2' | 'a3' | 'a4' | 'a5' | 'letter' | 'legal' | [number, number]
      orientation?: 'portrait' | 'landscape'
      compress?: boolean
    }
  }

  interface Html2Pdf {
    set(options: Html2PdfOptions): Html2Pdf
    from(element: Element | string): Html2Pdf
    save(): Promise<void>
    output(type?: 'pdf' | 'datauristring' | 'blob'): Promise<any>
    outputPdf(type?: 'pdf' | 'datauristring' | 'blob'): Promise<any>
  }

  function html2pdf(): Html2Pdf
  export default html2pdf
}