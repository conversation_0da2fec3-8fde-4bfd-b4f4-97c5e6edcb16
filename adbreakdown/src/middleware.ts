import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

const isPublicRoute = createRouteMatcher([
  '/',
  '/ad',
  '/ad/(.*)',
  '/ad-library',
  '/featured',
  '/featured/(.*)',
  '/frameworks',
  '/frameworks/(.*)',
  '/pricing',
  '/ad-agent',
  '/about',
  '/terms',
  '/privacy',
  '/faq',
  '/contact',
  '/api/analyses/(.*)',
  '/api/featured',
  '/api/featured/(.*)',
  '/api/webhooks/clerk',
  '/api/webhooks/lemon-squeezy',
  '/sign-in(.*)',
  '/sign-up(.*)'
])

export default clerkMiddleware(async (auth, request) => {
  if (!isPublicRoute(request)) {
    const { userId } = await auth()
    if (!userId) {
      return NextResponse.redirect(new URL('/sign-in', request.url))
    }
  }
  return NextResponse.next()
})

export const config = {
  // Protects all routes, including api/trpc.
  // See https://clerk.com/docs/references/nextjs/auth-middleware#usage
  // for more information about configuring your Middleware
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
}
