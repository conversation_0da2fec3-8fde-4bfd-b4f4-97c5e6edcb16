/**
 * Utility functions for analysis operations
 * Prevents common UUID/slug resolution issues in API routes
 */

/**
 * Checks if a string is a valid UUID format
 * @param id - The string to check
 * @returns boolean indicating if the string is a UUID
 */
export function isValidUUID(id: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(id)
}

/**
 * Returns the correct database field name for analysis queries
 * @param id - The ID parameter from the route
 * @returns 'id' for UUIDs, 'slug' for slugs
 */
export function getAnalysisFieldName(id: string): 'id' | 'slug' {
  return isValidUUID(id) ? 'id' : 'slug'
}

/**
 * Creates a safe query condition for analysis lookups
 * Automatically handles UUID vs slug resolution
 * @param id - The ID parameter from the route
 * @returns Object with field name and value for Supabase queries
 */
export function createAnalysisQuery(id: string) {
  const fieldName = getAnalysisFieldName(id)
  return {
    field: fieldName,
    value: id,
    // Helper method for Supabase queries
    apply: (query: any) => query.eq(fieldName, id)
  }
}

/**
 * Validates analysis ownership for API routes
 * @param supabase - Supabase client
 * @param analysisId - Analysis ID (UUID or slug)
 * @param userId - User's database ID
 * @returns Promise with analysis data or null if not found/unauthorized
 */
export async function validateAnalysisOwnership(
  supabase: any,
  analysisId: string,
  userId: string
) {
  const query = createAnalysisQuery(analysisId)
  
  const { data: analysis, error } = await supabase
    .from('ad_analyses')
    .select('*')
    .eq(query.field, query.value)
    .eq('user_id', userId)
    .single()

  if (error || !analysis) {
    return null
  }

  return analysis
}

/**
 * Type-safe analysis ID parameter extraction
 * @param params - Next.js route params
 * @returns Promise<string> with the analysis ID
 */
export async function extractAnalysisId(params: Promise<{ id: string }>): Promise<string> {
  const { id } = await params
  return id
}