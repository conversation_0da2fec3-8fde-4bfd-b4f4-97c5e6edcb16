export interface Framework {
  slug: string
  name: string
  summary: string
  icon?: string
  badge?: string
  category: 'persuasion' | 'storytelling' | 'analysis' | 'branding'
  complexity: 'beginner' | 'intermediate' | 'advanced'
  useCase: string[]
  description: string
  psychology?: string
  whenToUse: string[]
  breakdown: {
    title: string
    description: string
  }[]
  exampleAd: {
    title: string
    description: string
  }
  compareWith: {
    framework: string
    comparison: string
  }[]
  relatedFrameworks: string[]
  tags: string[]
}

export const frameworks: Framework[] = [
  {
    slug: 'aida',
    name: 'AIDA',
    summary: 'Classic funnel model for persuasive messaging',
    icon: '🎯',
    badge: 'Popular',
    category: 'persuasion',
    complexity: 'beginner',
    useCase: ['Direct response ads', 'Landing pages', 'Social media ads'],
    description: 'AIDA is a classic marketing framework that outlines the stages a consumer goes through in response to a persuasive message. It\'s widely used in ad creation, copywriting, and performance marketing.',
    whenToUse: [
      'Direct response campaigns',
      'Landing pages and sales videos', 
      'Social media and influencer ads',
      'Email marketing campaigns'
    ],
    breakdown: [
      {
        title: 'Attention',
        description: 'Grab the viewer\'s focus instantly with bold visuals, surprising hooks, or compelling headlines.'
      },
      {
        title: 'Interest', 
        description: 'Build relevance through emotional or functional storytelling that resonates with your audience.'
      },
      {
        title: 'Desire',
        description: 'Stoke aspiration by linking the product to lifestyle, identity, or tangible benefits.'
      },
      {
        title: 'Action',
        description: 'Prompt immediate action with a clear, urgent, and compelling call-to-action.'
      }
    ],
    exampleAd: {
      title: 'Telecom Service Ad',
      description: 'A telecom ad starts with a dropped call scenario (Attention), shows frustrating connectivity issues (Interest), demonstrates seamless service benefits (Desire), and ends with "Switch now and get 50% off" (Action).'
    },
    compareWith: [
      {
        framework: 'PAS',
        comparison: 'AIDA is funnel-focused while PAS is emotion-led and pain-based'
      },
      {
        framework: 'ADPLAN',
        comparison: 'AIDA focuses on performance marketing, ADPLAN on holistic branding'
      }
    ],
    relatedFrameworks: ['pas', 'adplan'],
    tags: ['funnel', 'performance', 'marketing', 'conversion']
  },
  {
    slug: 'pas',
    name: 'PAS',
    summary: 'Copywriting framework built around consumer pain',
    icon: '💊',
    category: 'persuasion',
    complexity: 'beginner',
    useCase: ['Problem-solving products', 'SaaS marketing', 'B2B campaigns'],
    description: 'PAS is a persuasion framework that taps into pain points to motivate action. Popular in sales copy, DTC marketing, and product demo videos.',
    psychology: 'This framework leverages loss aversion and the desire for resolution. By first highlighting a problem, it creates a cognitive dissonance that the solution then satisfyingly resolves.',
    whenToUse: [
      'Problem-solving products',
      'SaaS/Fintech/Healthcare campaigns',
      'B2B conversion-focused ads',
      'Educational content marketing'
    ],
    breakdown: [
      {
        title: 'Problem',
        description: 'Highlight a specific challenge or pain point the viewer currently faces.'
      },
      {
        title: 'Agitate',
        description: 'Deepen the emotional discomfort or emphasize the cost of inaction.'
      },
      {
        title: 'Solution',
        description: 'Present your product as the relief, answer, or pathway to resolution.'
      }
    ],
    exampleAd: {
      title: 'Skincare Treatment Ad',
      description: 'A skincare ad shows acne struggles and social anxiety (Problem), emphasizes missed opportunities and self-esteem loss (Agitate), then presents clear skin and renewed confidence post-treatment (Solution).'
    },
    compareWith: [
      {
        framework: 'AIDA',
        comparison: 'PAS is pain-response focused while AIDA follows a broader funnel approach'
      },
      {
        framework: 'Emotional Arcs',
        comparison: 'PAS targets specific pain points, Emotional Arcs create broader feeling journeys'
      }
    ],
    relatedFrameworks: ['aida', 'emotional-arcs'],
    tags: ['copywriting', 'persuasion', 'sales', 'problem-solving']
  },
  {
    slug: 'adplan',
    name: 'ADPLAN',
    summary: 'Comprehensive brand effectiveness evaluation',
    icon: '📊',
    badge: 'Advanced',
    category: 'analysis',
    complexity: 'advanced',
    useCase: ['Brand campaigns', 'Campaign analysis', 'Strategic planning'],
    description: 'ADPLAN (Attention, Distinction, Positioning, Linkage, Amplification, Net Equity) is a comprehensive framework for evaluating brand effectiveness and strategic advertising impact.',
    whenToUse: [
      'Brand awareness campaigns',
      'Long-term strategic planning',
      'Campaign effectiveness analysis',
      'Brand positioning studies'
    ],
    breakdown: [
      {
        title: 'Attention',
        description: 'Does the ad break through and capture audience attention effectively?'
      },
      {
        title: 'Distinction',
        description: 'Is the brand clearly differentiated from competitors in the messaging?'
      },
      {
        title: 'Positioning',
        description: 'Does the ad reinforce the intended brand positioning and values?'
      },
      {
        title: 'Linkage',
        description: 'Is there a strong connection between the creative and the brand?'
      },
      {
        title: 'Amplification',
        description: 'Does the ad amplify key brand messages and core propositions?'
      },
      {
        title: 'Net Equity',
        description: 'What is the overall impact on brand equity and perception?'
      }
    ],
    exampleAd: {
      title: 'Luxury Car Brand Campaign',
      description: 'A premium automotive ad uses striking visuals (Attention), emphasizes craftsmanship over speed (Distinction), reinforces luxury positioning (Positioning), features prominent brand elements (Linkage), amplifies heritage messaging (Amplification), and builds aspirational brand equity (Net Equity).'
    },
    compareWith: [
      {
        framework: 'AIDA',
        comparison: 'ADPLAN focuses on brand building while AIDA targets direct response'
      },
      {
        framework: 'Brand Positioning Lens',
        comparison: 'ADPLAN evaluates execution, Brand Positioning Lens defines strategy'
      }
    ],
    relatedFrameworks: ['brand-positioning-lens', 'aida'],
    tags: ['branding', 'analysis', 'strategy', 'effectiveness']
  },
  {
    slug: 'emotional-arcs',
    name: 'Emotional Arcs',
    summary: 'Narrative arcs that structure viewer sentiment',
    icon: '🎭',
    category: 'storytelling',
    complexity: 'intermediate',
    useCase: ['Brand storytelling', 'Emotional campaigns', 'Video content'],
    description: 'Emotional Arcs framework maps the emotional journey of viewers through advertising content, ensuring strategic emotional peaks and valleys that drive engagement and memory.',
    whenToUse: [
      'Brand storytelling campaigns',
      'Long-form video content',
      'Emotional connection building',
      'Memory and recall optimization'
    ],
    breakdown: [
      {
        title: 'Setup',
        description: 'Establish the emotional baseline and context for the journey.'
      },
      {
        title: 'Build',
        description: 'Gradually increase emotional tension or investment in the narrative.'
      },
      {
        title: 'Peak',
        description: 'Reach the emotional climax that creates maximum impact and memorability.'
      },
      {
        title: 'Resolution',
        description: 'Provide emotional satisfaction and connect back to the brand message.'
      }
    ],
    exampleAd: {
      title: 'Insurance Family Protection Ad',
      description: 'An insurance ad begins with family happiness (Setup), introduces risk scenarios (Build), shows potential loss impact (Peak), then resolves with protection and peace of mind through insurance coverage (Resolution).'
    },
    compareWith: [
      {
        framework: 'Hero\'s Journey',
        comparison: 'Emotional Arcs focus on feeling progression, Hero\'s Journey follows character transformation'
      },
      {
        framework: 'PAS',
        comparison: 'Emotional Arcs create broader feeling journeys, PAS targets specific pain points'
      }
    ],
    relatedFrameworks: ['heros-journey', 'freytags-pyramid'],
    tags: ['storytelling', 'emotion', 'narrative', 'engagement']
  },
  {
    slug: 'brand-positioning-lens',
    name: 'Brand Positioning Lens',
    summary: 'Strategic framework for category clarity and differentiation',
    icon: '🎯',
    category: 'branding',
    complexity: 'intermediate',
    useCase: ['Brand strategy', 'Positioning studies', 'Competitive analysis'],
    description: 'Brand Positioning Lens provides a strategic framework for analyzing how brands position themselves within categories and against competitors for maximum differentiation and clarity.',
    whenToUse: [
      'Brand strategy development',
      'Competitive positioning analysis',
      'Category entry planning',
      'Brand refresh initiatives'
    ],
    breakdown: [
      {
        title: 'Category Definition',
        description: 'Clearly define the competitive landscape and category boundaries.'
      },
      {
        title: 'Point of Difference',
        description: 'Identify unique value propositions that set the brand apart.'
      },
      {
        title: 'Point of Parity',
        description: 'Establish baseline expectations and category hygiene factors.'
      },
      {
        title: 'Brand Personality',
        description: 'Define the human characteristics and emotional attributes of the brand.'
      }
    ],
    exampleAd: {
      title: 'Electric Vehicle Brand Launch',
      description: 'An EV brand defines itself within the automotive category (Category), emphasizes sustainable technology (Difference), ensures safety and reliability expectations (Parity), and projects innovation and environmental consciousness (Personality).'
    },
    compareWith: [
      {
        framework: 'ADPLAN',
        comparison: 'Brand Positioning Lens defines strategy, ADPLAN evaluates execution'
      },
      {
        framework: 'AIDA',
        comparison: 'Positioning Lens focuses on strategic differentiation, AIDA on persuasion tactics'
      }
    ],
    relatedFrameworks: ['adplan'],
    tags: ['branding', 'positioning', 'strategy', 'differentiation']
  },
  {
    slug: 'freytags-pyramid',
    name: 'Freytag\'s Pyramid',
    summary: 'Classic 5-part storytelling arc',
    icon: '⛰️',
    category: 'storytelling',
    complexity: 'intermediate',
    useCase: ['Narrative ads', 'Video storytelling', 'Brand films'],
    description: 'Freytag\'s Pyramid is a classic storytelling structure that creates compelling narratives through exposition, rising action, climax, falling action, and resolution.',
    whenToUse: [
      'Long-form video content',
      'Brand documentary style ads',
      'Product launch narratives',
      'Corporate storytelling'
    ],
    breakdown: [
      {
        title: 'Exposition',
        description: 'Set the scene, introduce characters, and establish the context.'
      },
      {
        title: 'Rising Action',
        description: 'Build tension and develop the central conflict or challenge.'
      },
      {
        title: 'Climax',
        description: 'Reach the turning point or moment of highest tension.'
      },
      {
        title: 'Falling Action',
        description: 'Show the consequences and begin resolution of the conflict.'
      },
      {
        title: 'Resolution',
        description: 'Conclude the story and connect to the brand message.'
      }
    ],
    exampleAd: {
      title: 'Athletic Brand Comeback Story',
      description: 'An athletic brand ad introduces an injured athlete (Exposition), shows rehabilitation struggles (Rising Action), captures the moment of return to competition (Climax), demonstrates improved performance (Falling Action), and concludes with brand victory (Resolution).'
    },
    compareWith: [
      {
        framework: 'Hero\'s Journey',
        comparison: 'Freytag\'s Pyramid is more concise, Hero\'s Journey is more comprehensive'
      },
      {
        framework: 'Emotional Arcs',
        comparison: 'Freytag focuses on plot structure, Emotional Arcs on feeling progression'
      }
    ],
    relatedFrameworks: ['heros-journey', 'emotional-arcs'],
    tags: ['storytelling', 'narrative', 'structure', 'drama']
  },
  {
    slug: 'heros-journey',
    name: 'Hero\'s Journey',
    summary: '12-step mythological structure often used in films',
    icon: '🗡️',
    category: 'storytelling',
    complexity: 'advanced',
    useCase: ['Brand films', 'Customer journeys', 'Transformation stories'],
    description: 'The Hero\'s Journey is a comprehensive narrative structure that follows a character through transformation, commonly used in brand storytelling and customer success narratives.',
    whenToUse: [
      'Brand transformation stories',
      'Customer success narratives',
      'Product launch epics',
      'Corporate vision storytelling'
    ],
    breakdown: [
      {
        title: 'Call to Adventure',
        description: 'The protagonist faces a challenge or opportunity that disrupts their normal world.'
      },
      {
        title: 'Refusal of the Call',
        description: 'Initial hesitation or resistance to change, showing relatable human nature.'
      },
      {
        title: 'Meeting the Mentor',
        description: 'Introduction of guidance, wisdom, or the brand as the helping force.'
      },
      {
        title: 'Crossing the Threshold',
        description: 'Commitment to the journey and entry into the transformation process.'
      },
      {
        title: 'Return with Elixir',
        description: 'The protagonist returns transformed, with new knowledge or capability.'
      }
    ],
    exampleAd: {
      title: 'Educational Platform Success Story',
      description: 'An online learning platform shows a career-stuck professional (Call), initial doubt about online education (Refusal), discovering the platform (Mentor), enrolling in courses (Threshold), and achieving career advancement (Return with new skills).'
    },
    compareWith: [
      {
        framework: 'Freytag\'s Pyramid',
        comparison: 'Hero\'s Journey is more comprehensive, Freytag\'s Pyramid is more concise'
      },
      {
        framework: 'PAS',
        comparison: 'Hero\'s Journey focuses on transformation, PAS on problem-solving'
      }
    ],
    relatedFrameworks: ['freytags-pyramid', 'emotional-arcs'],
    tags: ['storytelling', 'transformation', 'journey', 'mythology']
  },
  {
    slug: 'lasswells-communication-model',
    name: 'Lasswell\'s Communication Model',
    summary: 'Communication theory: Who says what to whom with what effect?',
    icon: '📡',
    category: 'analysis',
    complexity: 'intermediate',
    useCase: ['Message analysis', 'Communication strategy', 'Media planning'],
    description: 'Lasswell\'s Communication Model breaks down communication into five key questions, providing a comprehensive framework for analyzing advertising effectiveness and message delivery.',
    whenToUse: [
      'Communication strategy development',
      'Message effectiveness analysis',
      'Media planning and selection',
      'Audience targeting optimization'
    ],
    breakdown: [
      {
        title: 'Who (Communicator)',
        description: 'Who is delivering the message? Brand, spokesperson, influencer, or character.'
      },
      {
        title: 'Says What (Message)',
        description: 'What is the core message, claim, or proposition being communicated?'
      },
      {
        title: 'To Whom (Audience)',
        description: 'Who is the intended audience and what are their characteristics?'
      },
      {
        title: 'In What Channel (Medium)',
        description: 'What medium or platform is being used to deliver the message?'
      },
      {
        title: 'With What Effect (Impact)',
        description: 'What is the intended outcome or behavioral change sought?'
      }
    ],
    exampleAd: {
      title: 'Celebrity Endorsement Campaign',
      description: 'A famous athlete (Who) promotes sustainable practices (Says What) to environmentally conscious millennials (To Whom) through social media platforms (Channel) to drive brand purchase and advocacy (Effect).'
    },
    compareWith: [
      {
        framework: 'ADPLAN',
        comparison: 'Lasswell focuses on communication mechanics, ADPLAN on brand effectiveness'
      },
      {
        framework: 'AIDA',
        comparison: 'Lasswell analyzes message delivery, AIDA structures persuasion flow'
      }
    ],
    relatedFrameworks: ['adplan'],
    tags: ['communication', 'analysis', 'strategy', 'effectiveness']
  }
]

export function getFrameworkBySlug(slug: string): Framework | undefined {
  return frameworks.find(framework => framework.slug === slug)
}

export function getRelatedFrameworks(currentSlug: string, relatedSlugs: string[]): Framework[] {
  return frameworks.filter(framework => 
    relatedSlugs.includes(framework.slug) && framework.slug !== currentSlug
  )
}

export function getFrameworksByCategory(category: Framework['category']): Framework[] {
  return frameworks.filter(framework => framework.category === category)
}