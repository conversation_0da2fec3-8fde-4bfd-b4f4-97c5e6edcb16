// Redis disabled - using no-op cache service
// import Redis from 'ioredis'

// Redis completely disabled for stability
let redis: any | null = null

console.log('📦 Cache: Redis disabled - running without caching')

export interface CacheOptions {
  ttl?: number // Time to live in seconds
  prefix?: string
}

const DEFAULT_TTL = 60 * 60 // 1 hour
const PUBLIC_ANALYSIS_TTL = 60 * 60 * 24 // 24 hours for public analyses

export class CacheService {
  private static buildKey(key: string, prefix?: string): string {
    return prefix ? `${prefix}:${key}` : key
  }

  static async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    try {
      if (!redis) return null // Skip cache if Redis unavailable
      
      const cacheKey = this.buildKey(key, options.prefix)
      const cached = await redis.get(cacheKey)
      return cached ? JSON.parse(cached) : null
    } catch (error) {
      console.warn('Cache get error:', error)
      return null
    }
  }

  static async set(key: string, value: any, options: CacheOptions = {}): Promise<boolean> {
    try {
      if (!redis) return false // Skip cache if Redis unavailable
      
      const cacheKey = this.buildKey(key, options.prefix)
      const ttl = options.ttl || DEFAULT_TTL
      await redis.setex(cacheKey, ttl, JSON.stringify(value))
      return true
    } catch (error) {
      console.warn('Cache set error:', error)
      return false
    }
  }

  static async delete(key: string, options: CacheOptions = {}): Promise<boolean> {
    try {
      if (!redis) return false // Skip cache if Redis unavailable
      
      const cacheKey = this.buildKey(key, options.prefix)
      await redis.del(cacheKey)
      return true
    } catch (error) {
      console.warn('Cache delete error:', error)
      return false
    }
  }

  static async invalidatePattern(pattern: string): Promise<boolean> {
    try {
      if (!redis) return false // Skip cache if Redis unavailable
      
      const keys = await redis.keys(pattern)
      if (keys.length > 0) {
        await redis.del(...keys)
      }
      return true
    } catch (error) {
      console.warn('Cache invalidate error:', error)
      return false
    }
  }

  // Specific methods for analysis caching
  static async getPublicAnalysis(id: string) {
    return this.get(`analysis:${id}`, { prefix: 'public', ttl: PUBLIC_ANALYSIS_TTL })
  }

  static async setPublicAnalysis(id: string, data: any) {
    return this.set(`analysis:${id}`, data, { prefix: 'public', ttl: PUBLIC_ANALYSIS_TTL })
  }

  static async getPublicAnalysisList(page: number = 1, limit: number = 10) {
    return this.get(`list:${page}:${limit}`, { prefix: 'public-analyses', ttl: 60 * 30 }) // 30 minutes
  }

  static async setPublicAnalysisList(page: number, limit: number, data: any) {
    return this.set(`list:${page}:${limit}`, data, { prefix: 'public-analyses', ttl: 60 * 30 })
  }

  // Private analysis methods
  static async getPrivateAnalysis(id: string, userId: string) {
    return this.get(`analysis:${id}:${userId}`, { prefix: 'private', ttl: 60 * 30 }) // 30 minutes
  }

  static async setPrivateAnalysis(id: string, userId: string, data: any) {
    return this.set(`analysis:${id}:${userId}`, data, { prefix: 'private', ttl: 60 * 30 })
  }

  // Dashboard methods
  static async getDashboardAnalyses(userId: string, page: number = 1, limit: number = 10) {
    return this.get(`user:${userId}:analyses:${page}:${limit}`, { prefix: 'dashboard', ttl: 60 * 10 }) // 10 minutes
  }

  static async setDashboardAnalyses(userId: string, page: number, limit: number, data: any) {
    return this.set(`user:${userId}:analyses:${page}:${limit}`, data, { prefix: 'dashboard', ttl: 60 * 10 })
  }

  static async invalidateAnalysis(id: string) {
    // Invalidate both public and private caches
    await this.delete(`analysis:${id}`, { prefix: 'public' })
    await this.invalidatePattern(`private:analysis:${id}:*`)
    // Invalidate list caches
    await this.invalidatePattern('public-analyses:*')
  }

  static async invalidateUserDashboard(userId: string) {
    await this.invalidatePattern(`dashboard:user:${userId}:analyses:*`)
  }
}

export default CacheService