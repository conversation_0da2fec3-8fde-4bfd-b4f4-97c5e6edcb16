// Utility functions for navigation and URL handling

/**
 * Generate the URL for an analysis page using UUID for stable routing
 */
export function getAnalysisUrl(analysis: { slug?: string | null; id: string }): string {
  // Always use UUID for stable URLs that don't change during analysis
  return `/ad/${analysis.id}`
}

/**
 * @deprecated No longer used - URLs now use UUIDs consistently for stability
 * Check if the current URL uses a UUID and should redirect to slug-based URL
 */
export function shouldRedirectToSlug(
  _currentId: string, 
  _analysis: { slug?: string | null; id: string }
): { shouldRedirect: boolean; redirectUrl?: string } {
  // Always return false - we now keep UUID URLs stable
  return { shouldRedirect: false }
}

/**
 * Extract ID from analysis URL parameter (could be UUID or slug)
 */
export function parseAnalysisId(id: string): { 
  id: string; 
  isUUID: boolean; 
  isSlug: boolean 
} {
  const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
  
  return {
    id,
    isUUID,
    isSlug: !isUUID
  }
}