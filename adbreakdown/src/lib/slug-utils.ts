// Utility functions for slug generation and YouTube URL handling

/**
 * Extract YouTube video ID from various YouTube URL formats
 */
export function extractYouTubeVideoId(url: string): string | null {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([\w-]+)/,
    /youtube\.com\/watch\?.*v=([\w-]+)/,
  ]
  
  for (const pattern of patterns) {
    const match = url.match(pattern)
    if (match) {
      return match[1]
    }
  }
  
  return null
}

/**
 * Normalize YouTube URL to a canonical format
 */
export function normalizeYouTubeUrl(url: string): string | null {
  const videoId = extractYouTubeVideoId(url)
  if (!videoId) return null
  
  return `https://www.youtube.com/watch?v=${videoId}`
}

/**
 * Convert string to URL-friendly slug
 */
export function slugify(text: string): string {
  return text
    .toLowerCase()
    .trim()
    // Replace special characters and spaces with hyphens
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+|-+$/g, '')
    // Limit length
    .substring(0, 60)
}

/**
 * Generate a unique slug for an ad analysis
 */
export function generateAdSlug(
  brand: string | null | undefined,
  title: string | null | undefined,
  videoId: string,
  fallbackName?: string
): string {
  const parts: string[] = []
  
  // Add brand if available and meaningful
  if (brand && brand !== 'N/A' && brand.length > 0) {
    parts.push(slugify(brand))
  }
  
  // Add title if available
  if (title && title.length > 0) {
    const titleSlug = slugify(title)
    if (titleSlug.length > 0) {
      parts.push(titleSlug)
    }
  }
  
  // Add fallback name if provided and no other parts
  if (parts.length === 0 && fallbackName) {
    parts.push(slugify(fallbackName))
  }
  
  // If we still have no parts, use generic name
  if (parts.length === 0) {
    parts.push('video-analysis')
  }
  
  // Always end with video ID for uniqueness
  parts.push(videoId)
  
  return parts.join('-')
}

/**
 * Check if a slug already exists in the database
 */
export async function isSlugUnique(slug: string, supabase: any): Promise<boolean> {
  const { data, error } = await supabase
    .from('ad_analyses')
    .select('id')
    .eq('slug', slug)
    .single()
  
  // If error (no record found), slug is unique
  // If data exists, slug is not unique
  return !data
}

/**
 * Generate a unique slug with incremental suffix if needed
 */
export async function generateUniqueSlug(
  brand: string | null | undefined,
  title: string | null | undefined,
  videoId: string,
  supabase: any,
  fallbackName?: string
): Promise<string> {
  const baseSlug = generateAdSlug(brand, title, videoId, fallbackName)
  
  // Check if base slug is unique
  if (await isSlugUnique(baseSlug, supabase)) {
    return baseSlug
  }
  
  // If not unique, try with incremental suffixes
  let counter = 2
  while (counter <= 100) { // Prevent infinite loop
    const numberedSlug = `${baseSlug}-${counter}`
    if (await isSlugUnique(numberedSlug, supabase)) {
      return numberedSlug
    }
    counter++
  }
  
  // Fallback: use timestamp
  const timestamp = Date.now().toString().slice(-6)
  return `${baseSlug}-${timestamp}`
}

/**
 * Validate YouTube URL format
 */
export function isValidYouTubeUrl(url: string): boolean {
  const youtubeRegex = /^(https?:\/\/)?(www\.)?(youtube\.com\/watch\?v=|youtu\.be\/)([\w-]+)/
  return youtubeRegex.test(url)
}

/**
 * Extract metadata from YouTube URL for slug generation
 */
export interface YouTubeUrlInfo {
  videoId: string
  normalizedUrl: string
  isValid: boolean
}

export function parseYouTubeUrl(url: string): YouTubeUrlInfo | null {
  if (!isValidYouTubeUrl(url)) {
    return null
  }
  
  const videoId = extractYouTubeVideoId(url)
  if (!videoId) {
    return null
  }
  
  const normalizedUrl = normalizeYouTubeUrl(url)
  if (!normalizedUrl) {
    return null
  }
  
  return {
    videoId,
    normalizedUrl,
    isValid: true
  }
}