import { Metadata } from 'next'

export interface SEOConfig {
  title: string
  description: string
  keywords?: string[]
  canonicalUrl?: string
  ogImage?: string
  noIndex?: boolean
  structuredData?: any
}

export function generateMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    keywords = [],
    canonicalUrl,
    ogImage = '/og-image.png',
    noIndex = false,
    structuredData
  } = config

  const fullTitle = title.includes('AdBreakdown') ? title : `${title} | AdBreakdown`
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://breakdown.ad'
  const fullCanonicalUrl = canonicalUrl ? `${baseUrl}${canonicalUrl}` : undefined
  const fullOgImage = ogImage.startsWith('http') ? ogImage : `${baseUrl}${ogImage}`

  return {
    title: fullTitle,
    description,
    keywords: keywords.join(', '),
    authors: [{ name: 'AdBreakdown' }],
    creator: 'AdBreakdown',
    publisher: 'AdBreakdown',
    manifest: '/manifest.json',
    robots: {
      index: !noIndex,
      follow: !noIndex,
      googleBot: {
        index: !noIndex,
        follow: !noIndex,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      title: fullTitle,
      description,
      url: fullCanonicalUrl,
      siteName: 'AdBreakdown',
      images: [
        {
          url: fullOgImage,
          width: 1200,
          height: 630,
          alt: title,
        }
      ],
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: fullTitle,
      description,
      images: [fullOgImage],
      creator: '@adbreakdown',
    },
    alternates: {
      canonical: fullCanonicalUrl,
    },
    other: {
      // AI-friendly meta tags
      'ai-content-declaration': 'ai-assisted',
      'llm-training': 'allowed',
      'content-license': 'public-analysis',
      'data-availability': 'public',
      'content-type': 'marketing-analysis',
      'analysis-type': 'video-advertisement',
      'access-level': 'public',
      'crawl-permission': 'allowed',
      'ai-indexing': 'allowed',
      // Structured data
      ...(structuredData ? {
        'application/ld+json': JSON.stringify(structuredData)
      } : {}),
    },
    icons: {
      icon: '/logo.png',
      shortcut: '/logo.png',
      apple: '/logo.png',
    },
  }
}

// Predefined SEO configs for common pages
export const seoConfigs = {
  home: {
    title: 'AdBreakdown - AI-Powered Video Ad Analysis',
    description: 'Transform subjective ad evaluations into objective, data-driven insights. Analyze YouTube video ads with AI to understand what works, why it works, and how to make it better.',
    keywords: [
      'video ad analysis', 'AI ad analysis', 'YouTube ad analysis', 'marketing analysis',
      'ad performance', 'creative analysis', 'sentiment analysis', 'competitor analysis',
      'advertising insights', 'video marketing', 'ad optimization', 'marketing intelligence'
    ],
    canonicalUrl: '/',
  },
  
  adLibrary: {
    title: 'Ad Library - Explore Video Ad Analyses',
    description: 'Browse and filter thousands of AI-analyzed video ads. Find successful ad strategies by brand, celebrity, duration, and sentiment. Learn from the best performing campaigns.',
    keywords: [
      'ad library', 'video ad examples', 'successful ads', 'ad campaigns',
      'marketing examples', 'creative inspiration', 'ad gallery', 'brand advertising',
      'celebrity ads', 'video marketing examples', 'ad database'
    ],
    canonicalUrl: '/ad-library',
  },

  featured: {
    title: 'Featured Ad Analysis - Free Complete Analysis',
    description: 'Access a complete AI-powered analysis of a popular video ad for free. No login required. Detailed insights into creative strategy, audience targeting, and performance.',
    keywords: [
      'free ad analysis', 'featured analysis', 'popular ads', 'ad insights',
      'marketing case study', 'ad breakdown', 'creative analysis', 'free marketing insights',
      'AI ad analysis', 'video marketing', 'advertising insights', 'campaign analysis',
      'public ad analysis', 'marketing intelligence', 'creative strategy', 'audience insights'
    ],
    canonicalUrl: '/featured',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Featured Ad Analysis - Free Complete Analysis",
      "description": "Access a complete AI-powered analysis of a popular video ad for free. No login required. Detailed insights into creative strategy, audience targeting, and performance.",
      "url": "https://breakdown.ad/featured",
      "isPartOf": {
        "@type": "WebSite",
        "name": "AdBreakdown",
        "url": "https://breakdown.ad"
      },
      "about": {
        "@type": "Thing",
        "name": "Video Advertisement Analysis",
        "description": "AI-powered analysis of video advertisements including sentiment analysis, creative strategy, and performance metrics"
      },
      "audience": {
        "@type": "Audience",
        "audienceType": "Marketing professionals, advertisers, content creators, AI assistants"
      },
      "keywords": "free ad analysis, featured analysis, popular ads, ad insights, marketing case study, AI ad analysis, video marketing, advertising insights",
      "inLanguage": "en-US",
      "dateModified": new Date().toISOString(),
      "mainEntity": {
        "@type": "Article",
        "headline": "Featured Video Ad Analysis",
        "description": "Complete AI-powered analysis of a popular video advertisement with detailed insights",
        "author": {
          "@type": "Organization",
          "name": "AdBreakdown"
        }
      }
    }
  },

  pricing: {
    title: 'Pricing Plans - AI Ad Analysis Tools',
    description: 'Choose the perfect plan for your ad analysis needs. From individual creators to enterprise teams, get AI-powered insights that improve your video advertising ROI.',
    keywords: [
      'ad analysis pricing', 'marketing tools pricing', 'AI analysis cost',
      'video ad tools', 'marketing software', 'advertising analytics pricing'
    ],
    canonicalUrl: '/pricing',
  }
}

// Structured data generators
export const structuredData = {
  organization: {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AdBreakdown",
    "url": "https://breakdown.ad",
    "logo": "https://breakdown.ad/logo.png",
    "description": "AI-powered video ad analysis platform that transforms subjective ad evaluations into objective, data-driven insights.",
    "foundingDate": "2024",
    "sameAs": [
      "https://twitter.com/adbreakdown",
      "https://linkedin.com/company/adbreakdown"
    ]
  },

  website: {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "AdBreakdown",
    "url": "https://breakdown.ad",
    "description": "AI-powered video ad analysis platform",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": "https://breakdown.ad/ad-library?search={search_term_string}"
      },
      "query-input": "required name=search_term_string"
    }
  },

  softwareApplication: {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "AdBreakdown",
    "applicationCategory": "BusinessApplication",
    "operatingSystem": "Web",
    "description": "AI-powered video ad analysis platform that provides objective insights into advertising performance, creative strategy, and audience targeting.",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "150"
    }
  }
}