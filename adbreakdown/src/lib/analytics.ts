// Google Analytics 4 setup
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID

// Initialize Google Analytics
export const initGA = () => {
  if (!GA_TRACKING_ID) return

  // Load gtag script
  const script = document.createElement('script')
  script.src = `https://www.googletagmanager.com/gtag/js?id=${GA_TRACKING_ID}`
  script.async = true
  document.head.appendChild(script)

  // Initialize gtag
  const gtag: any = function(...args: any[]) {
    (gtag.q = gtag.q || []).push(args)
  }
  window.gtag = window.gtag || gtag
  window.gtag('js', new Date())
  window.gtag('config', GA_TRACKING_ID, {
    page_title: document.title,
    page_location: window.location.href,
  })
}

// Track page views
export const trackPageView = (url: string) => {
  if (!GA_TRACKING_ID || !window.gtag) return

  window.gtag('config', GA_TRACKING_ID, {
    page_location: url,
  })
}

// Track custom events
export const trackEvent = (
  action: string,
  category: string,
  label?: string,
  value?: number
) => {
  if (!GA_TRACKING_ID || !window.gtag) return

  window.gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  })
}

// Track business-specific events
export const trackAnalysisEvent = (
  action: 'view' | 'generate' | 'share' | 'export',
  analysisId: string,
  brand?: string
) => {
  trackEvent(action, 'analysis', `${analysisId}${brand ? `-${brand}` : ''}`)
}

export const trackUserEvent = (
  action: 'signup' | 'login' | 'subscribe' | 'cancel',
  userId?: string
) => {
  trackEvent(action, 'user', userId)
}

export const trackFeatureEvent = (
  feature: string,
  action: 'use' | 'click' | 'complete'
) => {
  trackEvent(action, 'feature', feature)
}

// Declare gtag for TypeScript
declare global {
  interface Window {
    gtag: any
  }
}