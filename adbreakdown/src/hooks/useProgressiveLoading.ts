// Custom hook for progressive data loading in analysis page

import { useState, useEffect, useCallback, useRef } from 'react'

interface LoadingState {
  isLoading: boolean
  isLoaded: boolean
  error: string | null
}

interface ProgressiveLoadingConfig {
  immediate?: boolean // Load immediately
  onScroll?: boolean // Load when scrolled into view
  onTabChange?: boolean // Load when tab is activated
  dependencies?: any[] // Dependencies to trigger reload
}

export function useProgressiveLoading<T>(
  loadFunction: () => Promise<T>,
  config: ProgressiveLoadingConfig = {}
) {
  const [data, setData] = useState<T | null>(null)
  const [state, setState] = useState<LoadingState>({
    isLoading: false,
    isLoaded: false,
    error: null
  })

  const hasLoadedRef = useRef(false)

  const load = useCallback(async () => {
    if (hasLoadedRef.current) return // Prevent duplicate loads
    
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    hasLoadedRef.current = true

    try {
      const result = await loadFunction()
      setData(result)
      setState(prev => ({ ...prev, isLoading: false, isLoaded: true }))
    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        isLoading: false, 
        error: error instanceof Error ? error.message : 'An error occurred'
      }))
      hasLoadedRef.current = false // Allow retry on error
    }
  }, [loadFunction])

  // Load immediately if configured
  useEffect(() => {
    if (config.immediate) {
      load()
    }
  }, [config.immediate, load])

  // Reload when dependencies change
  useEffect(() => {
    if (config.dependencies && hasLoadedRef.current) {
      hasLoadedRef.current = false
      if (config.immediate) {
        load()
      }
    }
  }, [config.immediate, load, config.dependencies])

  return {
    data,
    ...state,
    load
  }
}

// Hook for intersection observer (scroll-based loading)
export function useIntersectionObserver(
  ref: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isVisible, setIsVisible] = useState(false)
  const [hasBeenVisible, setHasBeenVisible] = useState(false)

  useEffect(() => {
    const element = ref.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting)
        if (entry.isIntersecting && !hasBeenVisible) {
          setHasBeenVisible(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [ref, hasBeenVisible, options])

  return { isVisible, hasBeenVisible }
}

// Hook for tab-based lazy loading
export function useTabLazyLoading(activeTab: string, tabName: string) {
  const [shouldLoad, setShouldLoad] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)

  useEffect(() => {
    if (activeTab === tabName && !hasLoaded) {
      setShouldLoad(true)
      setHasLoaded(true)
    }
  }, [activeTab, tabName, hasLoaded])

  return { shouldLoad, hasLoaded }
}