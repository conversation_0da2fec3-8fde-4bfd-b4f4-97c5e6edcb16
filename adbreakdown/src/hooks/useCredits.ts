import { useState, useEffect, useCallback } from 'react'
import { useAuth } from './useAuth'

interface UserProfile {
  subscription_status: string
  subscription_plan: string | null
  subscription_period_end: string | null
  credits_remaining: number
  created_at: string
  updated_at: string
}

export const useCredits = () => {
  const { isAuthenticated, loading: authLoading } = useAuth()
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const fetchProfile = useCallback(async () => {
    if (!isAuthenticated) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const response = await fetch('/api/users/profile')
      if (!response.ok) {
        throw new Error('Failed to fetch profile')
      }
      
      const data = await response.json()
      if (data.success) {
        setProfile(data.profile)
      } else {
        throw new Error(data.error || 'Failed to load profile')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }, [isAuthenticated])

  const checkCredits = (required: number = 1): boolean => {
    if (!profile) return false
    return profile.credits_remaining >= required
  }

  const deductCredits = async (amount: number = 1): Promise<boolean> => {
    if (!isAuthenticated || !profile) return false
    
    try {
      const response = await fetch('/api/users/deduct-credits', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ credits: amount })
      })
      
      if (response.ok) {
        const data = await response.json()
        if (data.success) {
          setProfile(prev => prev ? { ...prev, credits_remaining: data.credits_remaining } : null)
          return true
        }
      }
      return false
    } catch (err) {
      console.error('Failed to deduct credits:', err)
      return false
    }
  }

  useEffect(() => {
    if (!authLoading) {
      fetchProfile()
    }
  }, [isAuthenticated, authLoading, fetchProfile])

  return {
    profile,
    loading: loading || authLoading,
    error,
    checkCredits,
    deductCredits,
    refreshProfile: fetchProfile
  }
}