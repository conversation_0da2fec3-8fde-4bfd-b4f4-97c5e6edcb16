// Supabase Edge Function: run-emotion-timeline-analysis
// Generates a detailed, timeline-based emotion analysis for a video ad.

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { GoogleGenerativeAI } from 'https://esm.sh/@google/generative-ai@0.1.3'

// --- Request/Response Interfaces ---

interface EmotionTimelineRequest {
  ad_analysis_id: string
  report_id: string
  youtube_url: string
}

interface EmotionTimelineEvent {
  startTimeSeconds: number
  endTimeSeconds: number
  audienceEmotion: 'Humor' | 'Joy' | 'Surprise' | 'Anger' | 'Sadness' | 'Fear' | 'Curiosity' | 'Neutral'
  intensity: 'Low' | 'Medium' | 'High' | 'Peak'
  eventDescription: string
  actorEmotion: 'Anger' | 'Joy' | 'Sadness' | 'Neutral' | null
}

interface AdvancedGeminiResponse {
  overallSentiment: 'Positive' | 'Negative' | 'Neutral' | 'Mixed'
  primaryAudienceEmotion: 'Humor' | 'Joy' | 'Surprise' | 'Anger' | 'Sadness' | 'Fear'
  analysisSummary: string
  timeline: EmotionTimelineEvent[]
}

// --- Main Server Logic ---

serve(async (req) => {
  try {
    // --- Initialization ---
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    const geminiApiKey = Deno.env.get('GEMINI_API_KEY')!
    const genAI = new GoogleGenerativeAI(geminiApiKey)
    const model = genAI.getGenerativeModel({ 
      model: 'gemini-1.5-pro', // Upgraded to pro for grounding
      generationConfig: {
        temperature: 0.8,
        topP: 0.95,
      },
      tools: [
        {
          googleSearchRetrieval: {
            dynamicRetrievalConfig: {
              mode: 'MODE_DYNAMIC',
              dynamicThreshold: 0.7
            }
          }
        }
      ]
    })

    const { ad_analysis_id, report_id, youtube_url }: EmotionTimelineRequest = await req.json()
    console.log('Processing emotion timeline:', { ad_analysis_id, report_id })

    // --- AI Analysis Prompt ---
    const analysisPrompt = `
      Analyze the YouTube video ad at this URL: ${youtube_url}

      Perform a deep, timeline-based emotion analysis and return the output as a single, valid JSON object.

      JSON Structure:
      {
        "overallSentiment": "Positive" | "Negative" | "Neutral" | "Mixed",
        "primaryAudienceEmotion": "Humor" | "Joy" | "Surprise" | "Anger" | "Sadness" | "Fear",
        "analysisSummary": "A concise, human-readable paragraph summarizing the overall emotional arc and sentiment of the ad.",
        "timeline": [
          {
            "startTimeSeconds": integer,
            "endTimeSeconds": integer,
            "audienceEmotion": "Humor" | "Joy" | "Surprise" | "Anger" | "Sadness" | "Fear" | "Curiosity" | "Neutral",
            "intensity": "Low" | "Medium" | "High" | "Peak",
            "eventDescription": "Detailed description of the visual and audio cues in this segment.",
            "actorEmotion": "Anger" | "Joy" | "Sadness" | "Neutral" | null
          }
        ]
      }

      Instructions:
      1.  **Temporal Segmentation:** Break the video into meaningful scenes or time-based chunks.
      2.  **Multi-Modal Analysis:** For each segment, analyze visuals (facial expressions, actions), audio (tone of voice), and transcript.
      3.  **Emotion Classification:** Distinguish between the emotion PORTRAYED by an actor and the emotion EVOKED in the audience.
      4.  **Intensity Scoring:** Quantify the strength of the audience's emotion for each segment.
      5.  **Summary Generation:** Synthesize all findings into a coherent summary.
      6.  **Strict JSON:** Ensure the output is a single, perfectly formed JSON object without any extra text or formatting.
    `

    // --- Call Gemini API ---
    const result = await model.generateContent(analysisPrompt)
    const responseText = result.response.text()
    
    let analysisData: AdvancedGeminiResponse
    try {
      analysisData = JSON.parse(responseText)
    } catch (e) {
      console.error("Failed to parse Gemini JSON response:", responseText)
      throw new Error(`Invalid JSON from AI: ${e.message}`)
    }

    // --- Database Operations ---

    // 1. Create the main sentiment_analyses record
    const { data: sentimentAnalysis, error: sentimentError } = await supabase
      .from('sentiment_analyses')
      .insert({
        ad_analysis_id: ad_analysis_id,
        overall_sentiment: analysisData.overallSentiment,
        primary_audience_emotion: analysisData.primaryAudienceEmotion,
        analysis_summary: analysisData.analysisSummary,
      })
      .select('analysis_id')
      .single()

    if (sentimentError) throw new Error(`Failed to create sentiment analysis record: ${sentimentError.message}`)
    const newAnalysisId = sentimentAnalysis.analysis_id

    // 2. Insert all timeline events
    const timelineEventsToInsert = analysisData.timeline.map(event => ({
      analysis_id: newAnalysisId,
      start_time_seconds: event.startTimeSeconds,
      end_time_seconds: event.endTimeSeconds,
      audience_emotion: event.audienceEmotion,
      intensity: event.intensity,
      event_description: event.eventDescription,
      actor_emotion: event.actorEmotion,
    }))

    const { error: eventsError } = await supabase
      .from('emotion_timeline_events')
      .insert(timelineEventsToInsert)

    if (eventsError) throw new Error(`Failed to insert emotion timeline events: ${eventsError.message}`)

    // 3. Update the analysis_reports table with a summary
    await supabase
      .from('analysis_reports')
      .update({
        content: {
          summary: analysisData.analysisSummary,
          overallSentiment: analysisData.overallSentiment,
          primaryAudienceEmotion: analysisData.primaryAudienceEmotion,
          timelineEventCount: analysisData.timeline.length,
          // Storing the full timeline here might be too large.
          // A reference to the sentiment_analyses table is better.
          sentiment_analysis_id: newAnalysisId 
        },
        status: 'generated',
        generated_at: new Date().toISOString(),
      })
      .eq('id', report_id)

    console.log('Emotion timeline analysis completed successfully:', report_id)

    // --- Return Success Response ---
    return new Response(
      JSON.stringify({ success: true, message: 'Emotion timeline analysis completed successfully' }),
      { headers: { 'Content-Type': 'application/json' }, status: 200 }
    )

  } catch (error) {
    console.error('Error in run-emotion-timeline-analysis function:', error)
    
    // --- Error Handling & Status Update ---
    try {
      const { report_id } = await req.json()
      if (report_id) {
        const supabaseUrl = Deno.env.get('SUPABASE_URL')!
        const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
        const supabase = createClient(supabaseUrl, supabaseServiceKey)
        await supabase.from('analysis_reports').update({ status: 'error' }).eq('id', report_id)
      }
    } catch (updateError) {
      // This might fail if the request body is already consumed, which is fine.
    }

    return new Response(
      JSON.stringify({ error: 'Analysis failed', message: error.message }),
      { headers: { 'Content-Type': 'application/json' }, status: 500 }
    )
  }
})
