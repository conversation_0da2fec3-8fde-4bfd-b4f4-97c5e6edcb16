# AdBreakdown Development Rules

## Project Context
This is AdBreakdown - an AI-powered video ad analysis SaaS platform.

## Documentation Reference
All project specifications are in the `/documentation` folder:
- `documentation/prd.md` - Complete PRD with features, user stories, and acceptance criteria
- `documentation/user-flow.md` - Detailed user flows and navigation paths  
- `documentation/folder-structure.md` - Project folder structure
- `documentation/task-plan.md` - Phase-by-phase implementation roadmap
- `documentation/ad-id-prototype.ts` - Fully functional prototype of the ad/[id]/page.tsx 

## Development Guidelines
1. ALWAYS reference relevant docs when implementing features
2. Follow the exact folder structure from `documentation/folder-structure.md`
3. Implement user flows as specified in `documentation/user-flow.md`
4. Follow phase progression from `documentation/task-plan.md`
5. Meet acceptance criteria from `documentation/prd.md`
6. Implement ad/[id]/page.tsx the analysi page as it is in `documentation/ad-id-prototype.ts`



## Tech Stack (from PRD)
- Frontend: React, TypeScript, Tailwind CSS, Video.js
- Backend: Node.js (Express), Python (AI/ML)
- Database: PostgreSQL
- Auth: Clerk
- Backend Services: Supabase



## Key Features to Implement
1. AI sentiment/emotion analysis (PRD section 3.1)
2. Scene-by-scene breakdowns (PRD section 3.2)
3. Competitor comparison (PRD section 3.3)
4. Targeting recommendations (PRD section 3.4)
5. Actionable suggestions (PRD section 3.5)

## Code Standards
- Use TypeScript strict mode
- Follow component-based architecture from architecture.md
- Implement proper error handling
- Use Tailwind for styling (as specified in tech stack)
- Ensure mobile responsiveness
- Follow UI patterns from mockup specifications

## Current Development Phase
Track progress against task-plan.md phases:
- [ ] Phase 1: Auth & Foundation (Steps 1-15)
- [ ] Phase 2: Core AI Pipeline (Steps 16-26)  
- [ ] Phase 3: Analysis UI (Steps 27-34)
- [ ] Phase 4: Report Content Display (Steps 35-39)
- [ ] Phase 5+: Advanced Features

## Prompt Expectations
When implementing features, reference specific documents:
- "@docs/product-requirements.md" for feature specs
- "@docs/user-flow.md" for UX behavior
- "@docs/architecture.md" for structure
- "@docs/task-plan.md" for implementation steps