#!/bin/bash

# AdBreakdown Schema Diff Monitoring Script
# This script monitors schema differences between local and remote

set -e

echo "🔍 AdBreakdown Schema Diff Monitoring"
echo "======================================"

# Configuration
SCHEMA_DIR="database"
REMOTE_SCHEMA_FILE="$SCHEMA_DIR/remote_schema.sql"
LOCAL_SCHEMA_FILE="$SCHEMA_DIR/current_schema.sql"
DIFF_OUTPUT_FILE="$SCHEMA_DIR/schema_diff.txt"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if Supabase CLI is available
if ! command -v supabase &> /dev/null; then
    echo -e "${RED}❌ Supabase CLI not found${NC}"
    exit 1
fi

# Check if project is linked
if [ ! -f "supabase/config.toml" ]; then
    echo -e "${RED}❌ Supabase project not linked${NC}"
    exit 1
fi

echo "1️⃣ Fetching remote schema..."

# Try to pull remote schema with timeout
if timeout 30 supabase db pull --schema=public --file="$REMOTE_SCHEMA_FILE" 2>/dev/null; then
    echo -e "${GREEN}✅ Remote schema fetched successfully${NC}"
else
    echo -e "${YELLOW}⚠️  Could not fetch remote schema directly${NC}"
    echo "   Using alternative method..."
    
    # Alternative: Use pg_dump via service role (if available)
    if [ -n "$SUPABASE_DB_URL" ]; then
        pg_dump "$SUPABASE_DB_URL" --schema=public --schema-only > "$REMOTE_SCHEMA_FILE" 2>/dev/null || {
            echo -e "${YELLOW}⚠️  Could not fetch remote schema via pg_dump${NC}"
            echo "   Creating placeholder file..."
            echo "-- Remote schema could not be fetched at $TIMESTAMP" > "$REMOTE_SCHEMA_FILE"
            echo "-- Please ensure database connection is available" >> "$REMOTE_SCHEMA_FILE"
        }
    else
        echo -e "${YELLOW}⚠️  No database URL available${NC}"
        echo "   Creating placeholder file..."
        echo "-- Remote schema could not be fetched at $TIMESTAMP" > "$REMOTE_SCHEMA_FILE"
        echo "-- Please set SUPABASE_DB_URL environment variable" >> "$REMOTE_SCHEMA_FILE"
    fi
fi

echo "2️⃣ Comparing schemas..."

# Create diff output
{
    echo "Schema Diff Report"
    echo "Generated: $TIMESTAMP"
    echo "=================="
    echo ""
    
    if [ -f "$REMOTE_SCHEMA_FILE" ] && [ -f "$LOCAL_SCHEMA_FILE" ]; then
        # Check if files have actual schema content
        if grep -q "CREATE TABLE" "$REMOTE_SCHEMA_FILE" && grep -q "CREATE TABLE" "$LOCAL_SCHEMA_FILE"; then
            echo "Differences between local and remote schemas:"
            echo ""
            diff -u "$LOCAL_SCHEMA_FILE" "$REMOTE_SCHEMA_FILE" || echo "No differences found"
        else
            echo "Warning: One or both schema files appear to be empty or invalid"
            echo ""
            echo "Local schema file size: $(wc -l < "$LOCAL_SCHEMA_FILE") lines"
            echo "Remote schema file size: $(wc -l < "$REMOTE_SCHEMA_FILE") lines"
        fi
    else
        echo "Error: Schema files not found"
        echo "Local: $LOCAL_SCHEMA_FILE - $([ -f "$LOCAL_SCHEMA_FILE" ] && echo "exists" || echo "missing")"
        echo "Remote: $REMOTE_SCHEMA_FILE - $([ -f "$REMOTE_SCHEMA_FILE" ] && echo "exists" || echo "missing")"
    fi
    
    echo ""
    echo "Migration Status:"
    echo "=================="
    
    # Check for pending migrations
    if [ -d "$SCHEMA_DIR/migrations" ]; then
        echo "Available migrations:"
        ls -la "$SCHEMA_DIR/migrations"/*.sql 2>/dev/null || echo "No migration files found"
    else
        echo "No migrations directory found"
    fi
    
} > "$DIFF_OUTPUT_FILE"

echo "3️⃣ Analyzing differences..."

# Analyze the diff
if [ -f "$DIFF_OUTPUT_FILE" ]; then
    # Check if there are actual differences
    if grep -q "^[+-]" "$DIFF_OUTPUT_FILE"; then
        echo -e "${YELLOW}⚠️  Schema differences detected${NC}"
        
        # Count lines with differences
        ADDED=$(grep -c "^+" "$DIFF_OUTPUT_FILE" || echo "0")
        REMOVED=$(grep -c "^-" "$DIFF_OUTPUT_FILE" || echo "0")
        
        echo "   Added lines: $ADDED"
        echo "   Removed lines: $REMOVED"
        
        # Check for specific types of changes
        if grep -q "CREATE TABLE" "$DIFF_OUTPUT_FILE"; then
            echo -e "${YELLOW}   📊 Table structure changes detected${NC}"
        fi
        
        if grep -q "ALTER TABLE" "$DIFF_OUTPUT_FILE"; then
            echo -e "${YELLOW}   🔧 Table alterations detected${NC}"
        fi
        
        if grep -q "DROP TABLE" "$DIFF_OUTPUT_FILE"; then
            echo -e "${RED}   🗑️  Table deletions detected${NC}"
        fi
        
    else
        echo -e "${GREEN}✅ No schema differences found${NC}"
    fi
    
    # Show summary
    echo ""
    echo "📋 Report saved to: $DIFF_OUTPUT_FILE"
    echo "📅 Last check: $TIMESTAMP"
    
else
    echo -e "${RED}❌ Could not generate diff report${NC}"
fi

echo ""
echo "🔄 To run this check regularly, add to cron:"
echo "   0 9 * * * cd /path/to/adbreakdown && ./scripts/schema-diff.sh"