#!/bin/bash

# AdBreakdown Supabase Functions Deployment Script
# This script deploys all Edge Functions to Supabase

set -e

echo "🚀 Deploying AdBreakdown Edge Functions to Supabase..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Please install it first:"
    echo "   brew install supabase/tap/supabase"
    exit 1
fi

# Check if project is linked
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ Supabase project not linked. Please run:"
    echo "   supabase link --project-ref <your-project-ref>"
    exit 1
fi

# List of functions to deploy
FUNCTIONS=(
    "run-ad-analysis"
    "run-llm-feature-generation" 
    "run-emotion-timeline-analysis"
)

echo "📦 Functions to deploy: ${FUNCTIONS[*]}"

# Deploy each function
for func in "${FUNCTIONS[@]}"; do
    echo "🔧 Deploying function: $func"
    
    if [ -f "supabase/functions/$func/index.ts" ]; then
        supabase functions deploy "$func" --project-ref "${SUPABASE_PROJECT_REF:-$(grep 'project_id' supabase/config.toml | cut -d'"' -f2)}"
        echo "✅ Successfully deployed: $func"
    else
        echo "⚠️  Warning: Function file not found: supabase/functions/$func/index.ts"
    fi
done

echo "🎉 All functions deployed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Test functions in Supabase Dashboard"
echo "2. Update API routes to use deployed functions"
echo "3. Monitor function logs for any issues"