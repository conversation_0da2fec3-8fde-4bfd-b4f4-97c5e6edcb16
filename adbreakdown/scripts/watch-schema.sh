#!/bin/bash

# AdBreakdown Schema Watcher Script
# This script continuously monitors schema changes

set -e

echo "👁️  AdBreakdown Schema Watcher Started"
echo "Press Ctrl+C to stop"

# Configuration
WATCH_INTERVAL=300  # 5 minutes
SCHEMA_DIR="database"
LOG_FILE="$SCHEMA_DIR/schema_watch.log"

# Ensure log directory exists
mkdir -p "$SCHEMA_DIR"

# Function to log with timestamp
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Function to check schema
check_schema() {
    log_message "🔍 Checking schema differences..."
    
    if ./scripts/schema-diff.sh >> "$LOG_FILE" 2>&1; then
        log_message "✅ Schema check completed successfully"
        
        # Check if differences were found
        if [ -f "$SCHEMA_DIR/schema_diff.txt" ]; then
            if grep -q "^[+-]" "$SCHEMA_DIR/schema_diff.txt"; then
                log_message "⚠️  Schema differences detected! Check schema_diff.txt"
                
                # Optional: Send notification (uncomment if needed)
                # echo "Schema differences detected in AdBreakdown" | mail -s "Schema Alert" <EMAIL>
                
                # Optional: Create backup
                cp "$SCHEMA_DIR/current_schema.sql" "$SCHEMA_DIR/backups/schema_backup_$(date +%Y%m%d_%H%M%S).sql" 2>/dev/null || true
            fi
        fi
    else
        log_message "❌ Schema check failed"
    fi
}

# Function to handle cleanup on exit
cleanup() {
    log_message "👋 Schema watcher stopped"
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Initial check
log_message "🚀 Starting schema monitoring..."
check_schema

# Main monitoring loop
while true; do
    sleep $WATCH_INTERVAL
    check_schema
done