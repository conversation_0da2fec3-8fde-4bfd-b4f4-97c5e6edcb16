#!/bin/bash

# AdBreakdown Supabase Functions Testing Script
# This script tests all deployed Edge Functions

set -e

echo "🧪 Testing AdBreakdown Edge Functions..."

# Function URLs (adjust based on your project)
PROJECT_REF="${SUPABASE_PROJECT_REF:-$(grep 'project_id' supabase/config.toml | cut -d'"' -f2)}"
BASE_URL="https://${PROJECT_REF}.supabase.co/functions/v1"

# Test data
TEST_YOUTUBE_URL="https://www.youtube.com/watch?v=dQw4w9WgXcQ"
TEST_ANALYSIS_ID="550e8400-e29b-41d4-a716-446655440000"
TEST_USER_ID="550e8400-e29b-41d4-a716-446655440001"

echo "🔗 Base URL: $BASE_URL"
echo ""

# Test run-ad-analysis function
echo "1️⃣ Testing run-ad-analysis function..."
curl -X POST \
  "$BASE_URL/run-ad-analysis" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
  -d "{
    \"analysis_id\": \"$TEST_ANALYSIS_ID\",
    \"user_id\": \"$TEST_USER_ID\",
    \"youtubeUrl\": \"$TEST_YOUTUBE_URL\"
  }" \
  --max-time 30 \
  --silent \
  --show-error \
  || echo "❌ run-ad-analysis test failed"

echo ""
echo "2️⃣ Testing run-llm-feature-generation function..."
curl -X POST \
  "$BASE_URL/run-llm-feature-generation" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
  -d "{
    \"analysis_id\": \"$TEST_ANALYSIS_ID\",
    \"report_id\": \"$TEST_ANALYSIS_ID\",
    \"report_type_name\": \"marketing_copy\",
    \"user_id\": \"$TEST_USER_ID\",
    \"credit_cost\": 1
  }" \
  --max-time 30 \
  --silent \
  --show-error \
  || echo "❌ run-llm-feature-generation test failed"

echo ""
echo "3️⃣ Testing run-emotion-timeline-analysis function..."
curl -X POST \
  "$BASE_URL/run-emotion-timeline-analysis" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $SUPABASE_ANON_KEY" \
  -d "{
    \"ad_analysis_id\": \"$TEST_ANALYSIS_ID\",
    \"report_id\": \"$TEST_ANALYSIS_ID\",
    \"youtube_url\": \"$TEST_YOUTUBE_URL\"
  }" \
  --max-time 30 \
  --silent \
  --show-error \
  || echo "❌ run-emotion-timeline-analysis test failed"

echo ""
echo "✅ Function testing completed!"
echo "📝 Check Supabase Dashboard for detailed logs and responses"