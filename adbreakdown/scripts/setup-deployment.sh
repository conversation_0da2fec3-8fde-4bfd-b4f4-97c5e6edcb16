#!/bin/bash

# AdBreakdown Deployment Setup Script
# This script sets up the complete deployment environment

set -e

echo "🔧 Setting up AdBreakdown deployment environment..."

# Check prerequisites
echo "1️⃣ Checking prerequisites..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Installing..."
    if command -v brew &> /dev/null; then
        brew install supabase/tap/supabase
    else
        echo "❌ Homebrew not found. Please install Supabase CLI manually:"
        echo "   https://github.com/supabase/cli#install-the-cli"
        exit 1
    fi
fi

# Check if project is linked
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ Supabase project not linked. Please run:"
    echo "   supabase link --project-ref <your-project-ref>"
    exit 1
fi

# Create necessary directories
echo "2️⃣ Creating deployment directories..."
mkdir -p supabase/functions/run-ad-analysis
mkdir -p supabase/functions/run-llm-feature-generation
mkdir -p supabase/functions/run-emotion-timeline-analysis
mkdir -p scripts
mkdir -p logs

# Set up environment variables
echo "3️⃣ Setting up environment variables..."
if [ ! -f ".env.local" ]; then
    echo "❌ .env.local file not found. Please create it with required variables."
    exit 1
fi

# Copy functions from database to supabase directory
echo "4️⃣ Copying functions to deployment directories..."
if [ -f "database/functions/run-ad-analysis.ts" ]; then
    cp database/functions/run-ad-analysis.ts supabase/functions/run-ad-analysis/index.ts
    echo "✅ Copied run-ad-analysis function"
fi

if [ -f "database/functions/run-llm-feature-generation.ts" ]; then
    cp database/functions/run-llm-feature-generation.ts supabase/functions/run-llm-feature-generation/index.ts
    echo "✅ Copied run-llm-feature-generation function"
fi

if [ -f "database/functions/run-emotion-timeline-analysis.ts" ]; then
    cp database/functions/run-emotion-timeline-analysis.ts supabase/functions/run-emotion-timeline-analysis/index.ts
    echo "✅ Copied run-emotion-timeline-analysis function"
fi

# Create package.json for deployment scripts
echo "5️⃣ Creating package.json scripts..."
PACKAGE_JSON='package.json'
if [ -f "$PACKAGE_JSON" ]; then
    # Add deployment scripts to existing package.json
    node -e "
    const fs = require('fs');
    const pkg = JSON.parse(fs.readFileSync('$PACKAGE_JSON', 'utf8'));
    pkg.scripts = pkg.scripts || {};
    pkg.scripts['deploy:functions'] = './scripts/deploy-functions.sh';
    pkg.scripts['test:functions'] = './scripts/test-functions.sh';
    pkg.scripts['setup:deployment'] = './scripts/setup-deployment.sh';
    fs.writeFileSync('$PACKAGE_JSON', JSON.stringify(pkg, null, 2));
    console.log('✅ Added deployment scripts to package.json');
    "
fi

# Create deployment configuration
echo "6️⃣ Creating deployment configuration..."
cat > supabase/functions/_shared/config.ts << 'EOF'
// Shared configuration for all Edge Functions
export const config = {
  corsHeaders: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, GET, OPTIONS, PUT, DELETE',
  },
  gemini: {
    models: {
      flash: 'gemini-1.5-flash',
      pro: 'gemini-1.5-pro'
    }
  },
  supabase: {
    url: Deno.env.get('SUPABASE_URL')!,
    serviceKey: Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!,
  }
}
EOF

echo "✅ Created shared configuration"

# Create deployment README
echo "7️⃣ Creating deployment documentation..."
cat > DEPLOYMENT.md << 'EOF'
# AdBreakdown Deployment Guide

## Prerequisites

1. **Supabase CLI**: Install via Homebrew
   ```bash
   brew install supabase/tap/supabase
   ```

2. **Project Link**: Link your local project to Supabase
   ```bash
   supabase link --project-ref <your-project-ref>
   ```

3. **Environment Variables**: Ensure `.env.local` has all required variables

## Deployment Commands

### Deploy All Functions
```bash
npm run deploy:functions
# or
./scripts/deploy-functions.sh
```

### Test Functions
```bash
npm run test:functions
# or
./scripts/test-functions.sh
```

### Setup Deployment Environment
```bash
npm run setup:deployment
# or
./scripts/setup-deployment.sh
```

## Function Structure

```
supabase/functions/
├── run-ad-analysis/
│   └── index.ts
├── run-llm-feature-generation/
│   └── index.ts
├── run-emotion-timeline-analysis/
│   └── index.ts
└── _shared/
    └── config.ts
```

## Environment Variables

Each function requires these environment variables:
- `SUPABASE_URL`
- `SUPABASE_SERVICE_ROLE_KEY`
- `GEMINI_API_KEY`

## Monitoring

1. **Function Logs**: Check in Supabase Dashboard > Functions
2. **Error Tracking**: Monitor function execution in real-time
3. **Performance**: Check function execution times and memory usage

## Troubleshooting

### Common Issues

1. **Function Not Found**: Ensure function is properly deployed
2. **Permission Errors**: Check service role key permissions
3. **Timeout Issues**: Increase function timeout in config

### Debug Commands

```bash
# Check function status
supabase functions list

# View function logs
supabase functions logs <function-name>

# Test locally
supabase functions serve <function-name>
```
EOF

echo "✅ Created deployment documentation"

echo ""
echo "🎉 Deployment setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Review DEPLOYMENT.md for detailed instructions"
echo "2. Run 'npm run deploy:functions' to deploy all functions"
echo "3. Run 'npm run test:functions' to test deployed functions"
echo "4. Monitor function logs in Supabase Dashboard"