-- Migration: Add public flag to ad_analyses table
-- This allows analyses to be marked as publicly viewable

ALTER TABLE public.ad_analyses 
ADD COLUMN is_public boolean DEFAULT false;

-- Add index for public analyses queries
CREATE INDEX idx_ad_analyses_public ON public.ad_analyses(is_public) WHERE is_public = true;

-- Add index for public slug lookups
CREATE INDEX idx_ad_analyses_public_slug ON public.ad_analyses(slug, is_public) WHERE is_public = true;

-- Optional: Create a view for public analyses for easier querying
CREATE VIEW public.public_analyses AS
SELECT 
  id,
  slug,
  youtube_url,
  youtube_video_id,
  title,
  inferred_brand,
  duration_seconds,
  duration_formatted,
  thumbnail_url,
  overall_sentiment,
  emotions,
  transcript,
  summary,
  key_themes,
  music_mood,
  voice_tone,
  audio_quality,
  visual_appeal,
  color_palette,
  scenes,
  target_demographics,
  target_interests,
  target_behaviors,
  created_at,
  analysis_completed_at
FROM public.ad_analyses 
WHERE is_public = true AND status = 'completed';