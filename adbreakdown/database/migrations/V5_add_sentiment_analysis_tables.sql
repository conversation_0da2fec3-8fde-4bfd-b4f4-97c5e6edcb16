-- V5_add_sentiment_analysis_tables.sql
-- Migration to add tables for advanced timeline-based emotion analysis

-- NEW: Table to store the overall analysis result for a video
CREATE TABLE sentiment_analyses (
    analysis_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    ad_analysis_id UUID NOT NULL, -- Foreign Key to the ad_analyses table
    overall_sentiment VARCHAR(50) NOT NULL, -- e.g., 'Positive', 'Negative', 'Neutral', 'Mixed'
    primary_audience_emotion VARCHAR(50), -- e.g., 'Humor', 'Joy', 'Surprise', 'Anger', 'Sadness', 'Fear'
    analysis_summary TEXT,
    analysis_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ad_analysis_id) REFERENCES ad_analyses(id) ON DELETE CASCADE
);

-- NEW: Table to store the detailed timeline events for each analysis
CREATE TABLE emotion_timeline_events (
    event_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id UUID NOT NULL, -- Foreign Key to the sentiment_analyses table
    start_time_seconds INT NOT NULL,
    end_time_seconds INT NOT NULL,
    audience_emotion VARCHAR(50) NOT NULL, -- e.g., 'Humor', 'Joy', 'Surprise', 'Anger', 'Sadness', 'Fear', 'Curiosity', 'Neutral'
    intensity VARCHAR(20) NOT NULL, -- e.g., 'Low', 'Medium', 'High', 'Peak'
    event_description TEXT,
    actor_emotion VARCHAR(50), -- The emotion displayed by the actor (can be NULL)
    FOREIGN KEY (analysis_id) REFERENCES sentiment_analyses(analysis_id) ON DELETE CASCADE
);

-- Add uuid-ossp extension if not already present (needed for uuid_generate_v4())
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
