-- Migration V12: Add comprehensive analysis fields
-- Date: 2025-01-08
-- Description: Add new fields for comprehensive analysis feature

-- Add new fields to ad_analyses table
ALTER TABLE public.ad_analyses 
ADD COLUMN IF NOT EXISTS brand text,
ADD COLUMN IF NOT EXISTS product_category text,
ADD COLUMN IF NOT EXISTS parent_entity text,
ADD COLUMN IF NOT EXISTS campaign_category text,
ADD COLUMN IF NOT EXISTS central_insight text,
ADD COLUMN IF NOT EXISTS rich_analysis_data jsonb,
ADD COLUMN IF NOT EXISTS primary_framework text,
ADD COLUMN IF NOT EXISTS framework_confidence integer,
ADD COLUMN IF NOT EXISTS framework_execution_quality integer,
ADD COLUMN IF NOT EXISTS alternative_frameworks text[],
ADD COLUMN IF NOT EXISTS analysis_model_version text,
ADD COLUMN IF NOT EXISTS analysis_prompt_version text;

-- Add indexes for new fields
CREATE INDEX IF NOT EXISTS idx_ad_analyses_brand ON public.ad_analyses(brand);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_product_category ON public.ad_analyses(product_category);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_campaign_category ON public.ad_analyses(campaign_category);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_primary_framework ON public.ad_analyses(primary_framework);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_model_version ON public.ad_analyses(analysis_model_version);

-- Add comments for documentation
COMMENT ON COLUMN public.ad_analyses.brand IS 'Specific brand being advertised (e.g., iPhone, Swiggy)';
COMMENT ON COLUMN public.ad_analyses.product_category IS 'Product/service category (e.g., Smartphone, Food Delivery)';
COMMENT ON COLUMN public.ad_analyses.parent_entity IS 'Parent company if different from brand (e.g., Apple Inc)';
COMMENT ON COLUMN public.ad_analyses.campaign_category IS 'Campaign type (e.g., Brand building, Performance)';
COMMENT ON COLUMN public.ad_analyses.central_insight IS 'Core human truth or insight the ad is built on';
COMMENT ON COLUMN public.ad_analyses.rich_analysis_data IS 'Complete comprehensive analysis data as JSON';
COMMENT ON COLUMN public.ad_analyses.primary_framework IS 'Primary marketing framework identified';
COMMENT ON COLUMN public.ad_analyses.framework_confidence IS 'Confidence score for framework identification (1-10)';
COMMENT ON COLUMN public.ad_analyses.framework_execution_quality IS 'Quality score for framework execution (1-10)';
COMMENT ON COLUMN public.ad_analyses.alternative_frameworks IS 'Alternative frameworks that could apply';
COMMENT ON COLUMN public.ad_analyses.analysis_model_version IS 'AI model version used for analysis';
COMMENT ON COLUMN public.ad_analyses.analysis_prompt_version IS 'Prompt version used for analysis';

-- Update existing records to have default values
UPDATE public.ad_analyses 
SET 
    brand = inferred_brand,
    analysis_model_version = 'gemini-1.5-flash',
    analysis_prompt_version = 'v1.0-legacy'
WHERE brand IS NULL;