-- Migration V1: Legacy Database Migration (moved from root)
-- Date: 2025-01-08
-- Description: Original migration for adding missing columns to ad_analyses table
-- Note: This was originally database_migration.sql in root directory

-- Ensure all required fields exist and have proper constraints
-- Most fields already exist, this migration adds any missing ones and ensures data types

-- Add any missing columns (these may already exist, so we use IF NOT EXISTS equivalent)
DO $$ 
BEGIN
    -- Check and add video_info if missing
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'ad_analyses' AND column_name = 'video_info') THEN
        ALTER TABLE public.ad_analyses ADD COLUMN video_info TEXT;
    END IF;
    
    -- Ensure marketing_analysis column exists and is TEXT
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'ad_analyses' AND column_name = 'marketing_analysis') THEN
        ALTER TABLE public.ad_analyses ADD COLUMN marketing_analysis TEXT;
    END IF;
    
    -- Ensure emotions column is JSONB
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'ad_analyses' AND column_name = 'emotions' AND data_type = 'jsonb') THEN
        -- If column exists but wrong type, this would need manual intervention
        -- For new installs, this ensures correct type
        ALTER TABLE public.ad_analyses ADD COLUMN emotions JSONB;
    END IF;
    
    -- Ensure all content generation fields exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'ad_analyses' AND column_name = 'marketing_copy') THEN
        ALTER TABLE public.ad_analyses ADD COLUMN marketing_copy TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'ad_analyses' AND column_name = 'social_media_posts') THEN
        ALTER TABLE public.ad_analyses ADD COLUMN social_media_posts TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'ad_analyses' AND column_name = 'marketing_scorecard') THEN
        ALTER TABLE public.ad_analyses ADD COLUMN marketing_scorecard TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'ad_analyses' AND column_name = 'seo_keywords') THEN
        ALTER TABLE public.ad_analyses ADD COLUMN seo_keywords TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'ad_analyses' AND column_name = 'content_suggestions') THEN
        ALTER TABLE public.ad_analyses ADD COLUMN content_suggestions TEXT;
    END IF;
    
    -- Check if deciphered_script column exists and update its type to JSONB
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'ad_analyses' AND column_name = 'deciphered_script') THEN
        ALTER TABLE public.ad_analyses ADD COLUMN deciphered_script JSONB;
    ELSE
        -- If column exists but is not JSONB, alter it
        IF EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'ad_analyses' AND column_name = 'deciphered_script' AND data_type != 'jsonb') THEN
            ALTER TABLE public.ad_analyses ALTER COLUMN deciphered_script TYPE JSONB USING deciphered_script::JSONB;
        END IF;
    END IF;
END $$;

-- Create indexes for better performance on commonly queried fields
CREATE INDEX IF NOT EXISTS idx_ad_analyses_status ON public.ad_analyses(status);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_user_id ON public.ad_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_created_at ON public.ad_analyses(created_at);

-- Ensure updated_at trigger exists
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger if it doesn't exist
DROP TRIGGER IF EXISTS update_ad_analyses_updated_at ON public.ad_analyses;
CREATE TRIGGER update_ad_analyses_updated_at 
    BEFORE UPDATE ON public.ad_analyses
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Verify schema
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'ad_analyses' 
ORDER BY ordinal_position;