-- V9_add_slug_and_youtube_video_id.sql
-- Add slug column and youtube_video_id for improved URL structure and deduplication

-- Add slug column for SEO-friendly URLs
ALTER TABLE ad_analyses ADD COLUMN IF NOT EXISTS slug VARCHAR(255);

-- Add youtube_video_id for deduplication across different URL formats
ALTER TABLE ad_analyses ADD COLUMN IF NOT EXISTS youtube_video_id VARCHAR(20);

-- Create unique index on slug (for URL routing)
CREATE UNIQUE INDEX IF NOT EXISTS idx_ad_analyses_slug_unique ON ad_analyses(slug);

-- Create index on youtube_video_id (for deduplication queries)
CREATE INDEX IF NOT EXISTS idx_ad_analyses_youtube_video_id ON ad_analyses(youtube_video_id);

-- Create compound index for user + youtube_video_id (for checking user's existing analyses)
CREATE INDEX IF NOT EXISTS idx_ad_analyses_user_youtube_video_id ON ad_analyses(user_id, youtube_video_id);

-- Add comments for documentation
COMMENT ON COLUMN ad_analyses.slug IS 'SEO-friendly URL slug generated from brand, title, and video ID';
COMMENT ON COLUMN ad_analyses.youtube_video_id IS 'YouTube video ID extracted from URL for deduplication';

-- Note: We don't populate existing records immediately to avoid breaking existing functionality
-- A separate data migration script can be run later to populate slugs for existing records