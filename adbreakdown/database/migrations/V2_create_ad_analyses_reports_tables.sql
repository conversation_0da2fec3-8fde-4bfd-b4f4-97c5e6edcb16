-- V2_create_ad_analyses_reports_tables.sql
-- Migration for ad analyses and reports functionality

-- This migration extends the base schema with analysis-specific tables
-- The base tables (users, profiles, report_types) should already exist from V1

-- Ad analyses table (if not exists from main schema)
CREATE TABLE IF NOT EXISTS ad_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    youtube_url TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, error
    title TEXT,
    inferred_brand VARCHAR(255),
    duration INTEGER, -- in seconds
    thumbnail_url TEXT,
    overall_sentiment DECIMAL(3,2), -- -1.00 to 1.00
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analysis reports table (if not exists from main schema)
CREATE TABLE IF NOT EXISTS analysis_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id UUID NOT NULL REFERENCES ad_analyses(id) ON DELETE CASCADE,
    report_type_id INTEGER NOT NULL REFERENCES report_types(id),
    status VARCHAR(50) DEFAULT 'pending', -- pending, generating, generated, error
    content JSONB,
    generated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Ensure indexes exist
CREATE INDEX IF NOT EXISTS idx_ad_analyses_user_id ON ad_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_status ON ad_analyses(status);
CREATE INDEX IF NOT EXISTS idx_analysis_reports_analysis_id ON analysis_reports(analysis_id);
CREATE INDEX IF NOT EXISTS idx_analysis_reports_type ON analysis_reports(report_type_id);

-- Ensure RLS is enabled
ALTER TABLE ad_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_reports ENABLE ROW LEVEL SECURITY;

-- Ensure updated_at trigger exists for ad_analyses
CREATE TRIGGER update_ad_analyses_updated_at BEFORE UPDATE ON ad_analyses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default report types if they don't exist
INSERT INTO report_types (name, description, credit_cost) VALUES
    ('transcript_summary', 'Video transcript and summary analysis', 0),
    ('marketing_analysis', 'Detailed marketing effectiveness analysis', 0),
    ('marketing_copy', 'AI-generated marketing copy and headlines', 1),
    ('social_media_posts', 'Social media post suggestions', 1),
    ('marketing_scorecard', 'Marketing effectiveness scorecard', 1),
    ('seo_keywords', 'SEO keyword extraction', 1),
    ('content_suggestions', 'Content improvement suggestions', 1)
ON CONFLICT (name) DO NOTHING;
