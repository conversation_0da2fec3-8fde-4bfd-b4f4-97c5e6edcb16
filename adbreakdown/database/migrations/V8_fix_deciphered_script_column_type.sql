-- V8_fix_deciphered_script_column_type.sql
-- Change deciphered_script from JSONB[] (array) to JSONB (object)

-- First, let's see what data exists and handle it properly
-- This will convert any existing array data to preserve it as legacy_script

-- Step 1: Add a temporary column
ALTER TABLE ad_analyses ADD COLUMN IF NOT EXISTS deciphered_script_new JSONB;

-- Step 2: Migrate existing data
UPDATE ad_analyses 
SET deciphered_script_new = CASE 
    WHEN deciphered_script IS NOT NULL THEN 
        jsonb_build_object('legacy_script', deciphered_script)
    ELSE NULL 
END
WHERE deciphered_script IS NOT NULL;

-- Step 3: Drop the old column
ALTER TABLE ad_analyses DROP COLUMN IF EXISTS deciphered_script;

-- Step 4: Rename the new column
ALTER TABLE ad_analyses RENAME COLUMN deciphered_script_new TO deciphered_script;

-- Add comment for documentation
COMMENT ON COLUMN ad_analyses.deciphered_script IS 'JSONB object containing script analysis data (legacy_script, enhanced_analysis, etc.)';