-- V10_populate_existing_slugs.sql
-- Populate slug and youtube_video_id for existing ad_analyses records

-- Function to extract YouTube video ID from URL
CREATE OR REPLACE FUNCTION extract_youtube_video_id(url TEXT) 
RETURNS TEXT AS $$
BEGIN
  -- Handle youtube.com/watch?v= format
  IF url ~ 'youtube\.com/watch\?.*v=([a-zA-Z0-9_-]+)' THEN
    RETURN substring(url from 'youtube\.com/watch\?.*v=([a-zA-Z0-9_-]+)');
  END IF;
  
  -- Handle youtu.be/ format
  IF url ~ 'youtu\.be/([a-zA-Z0-9_-]+)' THEN
    RETURN substring(url from 'youtu\.be/([a-zA-Z0-9_-]+)');
  END IF;
  
  -- Handle youtube.com/embed/ format
  IF url ~ 'youtube\.com/embed/([a-zA-Z0-9_-]+)' THEN
    RETURN substring(url from 'youtube\.com/embed/([a-zA-Z0-9_-]+)');
  END IF;
  
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to create slug from text
CREATE OR REPLACE FUNCTION slugify(text_input TEXT)
RETURNS TEXT AS $$
BEGIN
  IF text_input IS NULL OR text_input = '' THEN
    RETURN '';
  END IF;
  
  RETURN lower(
    trim(
      regexp_replace(
        regexp_replace(
          regexp_replace(text_input, '[^\w\s-]', '', 'g'),
          '[\s_-]+', '-', 'g'
        ),
        '^-+|-+$', '', 'g'
      )
    )
  );
END;
$$ LANGUAGE plpgsql;

-- Function to generate unique slug
CREATE OR REPLACE FUNCTION generate_unique_slug(
  brand_name TEXT,
  title_text TEXT, 
  video_id TEXT,
  fallback_name TEXT DEFAULT 'video-analysis'
)
RETURNS TEXT AS $$
DECLARE
  base_slug TEXT := '';
  final_slug TEXT;
  counter INTEGER := 2;
  slug_exists BOOLEAN;
BEGIN
  -- Build base slug from components
  IF brand_name IS NOT NULL AND brand_name != 'N/A' AND length(brand_name) > 0 THEN
    base_slug := slugify(brand_name);
  END IF;
  
  IF title_text IS NOT NULL AND length(title_text) > 0 THEN
    IF length(base_slug) > 0 THEN
      base_slug := base_slug || '-' || slugify(title_text);
    ELSE
      base_slug := slugify(title_text);
    END IF;
  END IF;
  
  -- If no base slug, use fallback
  IF length(base_slug) = 0 THEN
    base_slug := fallback_name;
  END IF;
  
  -- Add video ID
  base_slug := base_slug || '-' || video_id;
  
  -- Limit length
  base_slug := left(base_slug, 60);
  
  -- Check uniqueness
  final_slug := base_slug;
  
  SELECT EXISTS(SELECT 1 FROM ad_analyses WHERE slug = final_slug) INTO slug_exists;
  
  WHILE slug_exists LOOP
    final_slug := base_slug || '-' || counter::text;
    SELECT EXISTS(SELECT 1 FROM ad_analyses WHERE slug = final_slug) INTO slug_exists;
    counter := counter + 1;
    
    -- Prevent infinite loop
    IF counter > 100 THEN
      final_slug := base_slug || '-' || extract(epoch from now())::bigint;
      EXIT;
    END IF;
  END LOOP;
  
  RETURN final_slug;
END;
$$ LANGUAGE plpgsql;

-- Update existing records with youtube_video_id
UPDATE ad_analyses 
SET youtube_video_id = extract_youtube_video_id(youtube_url)
WHERE youtube_video_id IS NULL AND youtube_url IS NOT NULL;

-- Update existing records with generated slugs
UPDATE ad_analyses 
SET slug = generate_unique_slug(
  inferred_brand,
  title,
  COALESCE(youtube_video_id, 'unknown'),
  'video-analysis'
)
WHERE slug IS NULL;

-- Clean up temporary functions (optional - keep them if you want to use them in the future)
-- DROP FUNCTION IF EXISTS extract_youtube_video_id(TEXT);
-- DROP FUNCTION IF EXISTS slugify(TEXT);
-- DROP FUNCTION IF EXISTS generate_unique_slug(TEXT, TEXT, TEXT, TEXT);