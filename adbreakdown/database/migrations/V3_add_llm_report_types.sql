-- V3_add_llm_report_types.sql
-- Migration to ensure all LLM feature report types exist

-- Insert LLM-powered report types with credit costs
INSERT INTO report_types (name, description, credit_cost) VALUES
    ('marketing_copy', 'AI-generated marketing copy and headlines', 1),
    ('social_media_posts', 'Social media post suggestions for multiple platforms', 1),
    ('marketing_scorecard', 'Marketing effectiveness scorecard with scores and justifications', 1),
    ('seo_keywords', 'SEO keyword extraction and analysis', 1),
    ('content_suggestions', 'Content improvement suggestions and recommendations', 1)
ON CONFLICT (name) DO UPDATE SET
    description = EXCLUDED.description,
    credit_cost = EXCLUDED.credit_cost;

-- Verify all report types exist
SELECT id, name, description, credit_cost 
FROM report_types 
ORDER BY credit_cost, name;
