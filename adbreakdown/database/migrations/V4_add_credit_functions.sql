-- V4_add_credit_functions.sql
-- Migration to add credit management functions

-- Function to safely decrement user credits
CREATE OR REPLACE FUNCTION decrement_credits(user_id_param UUID, cost INTEGER)
RETURNS INTEGER AS $$
DECLARE
    current_credits INTEGER;
    new_credits INTEGER;
BEGIN
    -- Get current credits with row lock
    SELECT credits_remaining INTO current_credits 
    FROM profiles 
    WHERE user_id = user_id_param 
    FOR UPDATE;
    
    -- Check if user exists
    IF current_credits IS NULL THEN
        RAISE EXCEPTION 'User profile not found';
    END IF;
    
    -- Check if sufficient credits
    IF current_credits < cost THEN
        RAISE EXCEPTION 'Insufficient credits: % available, % required', current_credits, cost;
    END IF;
    
    -- Calculate new credit amount
    new_credits := current_credits - cost;
    
    -- Update credits
    UPDATE profiles 
    SET credits_remaining = new_credits,
        updated_at = NOW()
    WHERE user_id = user_id_param;
    
    -- Return new credit amount
    RETURN new_credits;
END;
$$ LANGUAGE plpgsql;

-- Function to add credits (for subscriptions, purchases, etc.)
CREATE OR REPLACE FUNCTION add_credits(user_id_param UUID, amount INTEGER)
RETURNS INTEGER AS $$
DECLARE
    new_credits INTEGER;
BEGIN
    -- Update credits
    UPDATE profiles 
    SET credits_remaining = credits_remaining + amount,
        updated_at = NOW()
    WHERE user_id = user_id_param
    RETURNING credits_remaining INTO new_credits;
    
    -- Check if user exists
    IF new_credits IS NULL THEN
        RAISE EXCEPTION 'User profile not found';
    END IF;
    
    -- Return new credit amount
    RETURN new_credits;
END;
$$ LANGUAGE plpgsql;

-- Function to get user credits
CREATE OR REPLACE FUNCTION get_user_credits(user_id_param UUID)
RETURNS INTEGER AS $$
DECLARE
    credits INTEGER;
BEGIN
    SELECT credits_remaining INTO credits 
    FROM profiles 
    WHERE user_id = user_id_param;
    
    RETURN COALESCE(credits, 0);
END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION decrement_credits(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION add_credits(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_credits(UUID) TO authenticated;
