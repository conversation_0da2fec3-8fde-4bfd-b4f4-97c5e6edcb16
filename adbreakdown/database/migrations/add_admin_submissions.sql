-- Migration: Add admin submissions table for video analysis requests
-- This allows users to submit videos that exceed length limits for admin review

CREATE TABLE public.admin_submissions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  youtube_url text NOT NULL,
  youtube_video_id character varying,
  user_email text NOT NULL,
  video_title text,
  channel_title text,
  duration text,
  reason text NOT NULL,
  status text DEFAULT 'pending'::text, -- pending, approved, rejected, processed
  admin_notes text,
  processed_by text, -- admin user ID
  processed_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT admin_submissions_pkey PRIMARY KEY (id)
);

-- Add indexes for efficient querying
CREATE INDEX idx_admin_submissions_status ON public.admin_submissions(status);
CREATE INDEX idx_admin_submissions_created_at ON public.admin_submissions(created_at);
CREATE INDEX idx_admin_submissions_user_email ON public.admin_submissions(user_email);
CREATE INDEX idx_admin_submissions_video_id ON public.admin_submissions(youtube_video_id);

-- Add function to automatically extract video ID from URL
CREATE OR REPLACE FUNCTION extract_youtube_video_id(url text)
RETURNS text AS $$
BEGIN
  -- Extract video ID from various YouTube URL formats
  IF url ~ 'youtube\.com/watch\?v=' THEN
    RETURN substring(url from 'v=([a-zA-Z0-9_-]{11})');
  ELSIF url ~ 'youtu\.be/' THEN
    RETURN substring(url from 'youtu\.be/([a-zA-Z0-9_-]{11})');
  ELSIF url ~ 'youtube\.com/embed/' THEN
    RETURN substring(url from 'embed/([a-zA-Z0-9_-]{11})');
  ELSE
    RETURN NULL;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Add trigger to automatically populate video ID
CREATE OR REPLACE FUNCTION auto_populate_video_id()
RETURNS TRIGGER AS $$
BEGIN
  NEW.youtube_video_id := extract_youtube_video_id(NEW.youtube_url);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_auto_populate_video_id
  BEFORE INSERT OR UPDATE ON public.admin_submissions
  FOR EACH ROW
  EXECUTE FUNCTION auto_populate_video_id();

-- Update the updated_at timestamp automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_admin_submissions_updated_at
  BEFORE UPDATE ON public.admin_submissions
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create view for pending submissions
CREATE VIEW public.pending_admin_submissions AS
SELECT 
  id,
  youtube_url,
  youtube_video_id,
  user_email,
  video_title,
  channel_title,
  duration,
  reason,
  created_at
FROM public.admin_submissions 
WHERE status = 'pending'
ORDER BY created_at DESC;