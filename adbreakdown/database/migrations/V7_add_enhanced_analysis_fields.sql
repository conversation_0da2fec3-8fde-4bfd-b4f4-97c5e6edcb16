-- V7_add_enhanced_analysis_fields.sql
-- Add enhanced fields for authentic AI-generated data display

-- Add columns for enhanced sentiment and visual analysis data
ALTER TABLE ad_analyses 
ADD COLUMN IF NOT EXISTS visual_appeal INTEGER,
ADD COLUMN IF NOT EXISTS audio_quality INTEGER,
ADD COLUMN IF NOT EXISTS color_palette JSONB,
ADD COLUMN IF NOT EXISTS sentiment_timeline JSONB;

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_ad_analyses_visual_appeal ON ad_analyses(visual_appeal);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_audio_quality ON ad_analyses(audio_quality);

-- Add comments for documentation
COMMENT ON COLUMN ad_analyses.visual_appeal IS 'AI-generated visual appeal score (1-10)';
COMMENT ON COLUMN ad_analyses.audio_quality IS 'AI-generated audio quality score (1-10)';
COMMENT ON COLUMN ad_analyses.color_palette IS 'Array of dominant colors extracted from video';
COMMENT ON COLUMN ad_analyses.sentiment_timeline IS 'Timeline data showing sentiment changes throughout video';