-- Migration: Add showcase field to ad_analyses table for daily showcase feature
-- Only public analyses can be marked as showcase

-- Add showcase boolean field to ad_analyses table
ALTER TABLE ad_analyses 
ADD COLUMN showcase BOOLEAN DEFAULT FALSE;

-- Create index for performance when querying showcase analyses
CREATE INDEX idx_ad_analyses_showcase ON ad_analyses(showcase) WHERE showcase = true AND is_public = true;

-- Add comment for documentation
COMMENT ON COLUMN ad_analyses.showcase IS 'When true, this public analysis is eligible for daily showcase feature';

-- Create trigger function to auto-add to daily_showcases when showcase is set to true
CREATE OR REPLACE FUNCTION auto_add_to_daily_showcase()
RETURNS TRIGGER AS $$
BEGIN
  -- Only process if this is a public analysis
  IF NEW.is_public = TRUE THEN
    -- If showcase is being set to true and wasn't true before
    IF NEW.showcase = TRUE AND (OLD.showcase IS NULL OR OLD.showcase = FALSE) THEN
      -- Find the next available date (today or next day without a showcase)
      INSERT INTO daily_showcases (analysis_id, featured_date, excerpt, key_insights)
      SELECT 
        NEW.id,
        COALESCE(
          (SELECT MIN(date_candidate) 
           FROM (
             SELECT CURRENT_DATE + generate_series(0, 30) AS date_candidate
           ) AS dates
           WHERE date_candidate NOT IN (SELECT featured_date FROM daily_showcases)
           LIMIT 1
          ),
          CURRENT_DATE
        ) AS featured_date,
        COALESCE(
          NEW.summary, 
          'Featured analysis of ' || COALESCE(NEW.inferred_brand, 'this brand') || '''s video ad with comprehensive AI-powered insights.'
        ) AS excerpt,
        ARRAY[
          'AI-powered sentiment analysis', 
          'Detailed audience insights', 
          'Creative strategy breakdown',
          'Performance recommendations'
        ] AS key_insights
      ON CONFLICT (featured_date) DO NOTHING; -- Don't overwrite existing showcases
    END IF;
    
    -- If showcase is being set to false, remove from daily_showcases
    IF NEW.showcase = FALSE AND OLD.showcase = TRUE THEN
      DELETE FROM daily_showcases WHERE analysis_id = NEW.id;
    END IF;
  ELSE
    -- If analysis is made private, also set showcase to false and remove from daily_showcases
    IF OLD.is_public = TRUE AND NEW.is_public = FALSE THEN
      NEW.showcase = FALSE;
      DELETE FROM daily_showcases WHERE analysis_id = NEW.id;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger on ad_analyses for showcase changes
CREATE TRIGGER trigger_auto_daily_showcase
  AFTER UPDATE OF showcase, is_public ON ad_analyses
  FOR EACH ROW
  EXECUTE FUNCTION auto_add_to_daily_showcase();

-- Add constraint to ensure only public analyses can be showcase
ALTER TABLE ad_analyses 
ADD CONSTRAINT check_showcase_only_public 
CHECK (showcase = FALSE OR (showcase = TRUE AND is_public = TRUE));

-- Create a view for easy querying of showcase analyses
CREATE OR REPLACE VIEW showcase_analyses AS
SELECT 
  a.*,
  ds.featured_date,
  ds.excerpt,
  ds.key_insights
FROM ad_analyses a
LEFT JOIN daily_showcases ds ON a.id = ds.analysis_id
WHERE a.is_public = true AND a.showcase = true;

-- Add RLS policy for the view
ALTER VIEW showcase_analyses SET (security_barrier = true);

-- Grant access to the view
GRANT SELECT ON showcase_analyses TO authenticated, anon;