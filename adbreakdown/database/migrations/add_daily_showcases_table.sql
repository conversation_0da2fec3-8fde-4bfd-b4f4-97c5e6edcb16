-- Migration: Add daily_showcases table for featured analysis content
-- This table manages the "Ad of the Day" feature for the daily showcase page

CREATE TABLE IF NOT EXISTS daily_showcases (
  id SERIAL PRIMARY KEY,
  analysis_id UUID NOT NULL REFERENCES ad_analyses(id) ON DELETE CASCADE,
  featured_date DATE NOT NULL UNIQUE,
  excerpt TEXT NOT NULL,
  key_insights TEXT[] DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add trigger function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER update_daily_showcases_updated_at
  BEFORE UPDATE ON daily_showcases
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Create indexes for performance
CREATE INDEX idx_daily_showcases_featured_date ON daily_showcases(featured_date DESC);
CREATE INDEX idx_daily_showcases_analysis_id ON daily_showcases(analysis_id);

-- Add RLS policies for public access
ALTER TABLE daily_showcases ENABLE ROW LEVEL SECURITY;

-- Allow public read access for daily showcases
CREATE POLICY "Daily showcases are publicly readable" ON daily_showcases
  FOR SELECT USING (true);

-- Only authenticated users can insert/update (admin functionality)
CREATE POLICY "Only authenticated users can modify daily showcases" ON daily_showcases
  FOR ALL USING (auth.uid() IS NOT NULL);

-- Add comments for documentation
COMMENT ON TABLE daily_showcases IS 'Featured analysis content for the daily showcase page';
COMMENT ON COLUMN daily_showcases.analysis_id IS 'Reference to the featured ad analysis';
COMMENT ON COLUMN daily_showcases.featured_date IS 'Date when this analysis was/will be featured';
COMMENT ON COLUMN daily_showcases.excerpt IS 'Brief description/excerpt for the showcase';
COMMENT ON COLUMN daily_showcases.key_insights IS 'Array of key insights to highlight';