-- Monthly Credit Reset Function
-- This function should be called monthly to reset credits for active subscribers
-- It can be triggered via Supabase Edge Function or external cron job

CREATE OR REPLACE FUNCTION reset_monthly_credits()
RETURNS JSON AS $$
DECLARE
    subscription_plans JSONB := '{
        "starter": 50,
        "pro": 150,
        "enterprise": 500
    }';
    reset_count INTEGER := 0;
    profile_record RECORD;
    new_credits INTEGER;
BEGIN
    -- Loop through all active subscribers
    FOR profile_record IN 
        SELECT user_id, subscription_plan, subscription_status, credits_remaining
        FROM profiles 
        WHERE subscription_status = 'active' 
        AND subscription_plan IS NOT NULL
    LOOP
        -- Get credit allowance for the plan
        new_credits := (subscription_plans ->> profile_record.subscription_plan)::INTEGER;
        
        IF new_credits IS NOT NULL THEN
            -- Reset credits to plan allowance
            UPDATE profiles 
            SET credits_remaining = new_credits,
                updated_at = NOW()
            WHERE user_id = profile_record.user_id;
            
            -- Log the credit reset
            INSERT INTO credit_usage (
                user_id,
                credits_used,
                description,
                created_at
            ) VALUES (
                profile_record.user_id,
                new_credits - profile_record.credits_remaining, -- Can be negative if adding credits
                'Monthly credit reset for ' || profile_record.subscription_plan || ' plan',
                NOW()
            );
            
            reset_count := reset_count + 1;
        END IF;
    END LOOP;
    
    -- Return summary
    RETURN json_build_object(
        'success', true,
        'profiles_updated', reset_count,
        'reset_date', NOW()
    );
    
EXCEPTION WHEN OTHERS THEN
    -- Return error details
    RETURN json_build_object(
        'success', false,
        'error', SQLERRM,
        'profiles_updated', reset_count
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION reset_monthly_credits() TO authenticated;

-- Create a function to check when the last reset was performed
CREATE OR REPLACE FUNCTION get_last_credit_reset()
RETURNS JSON AS $$
DECLARE
    last_reset_date TIMESTAMP;
    days_since_reset INTEGER;
BEGIN
    -- Find the most recent credit reset log entry
    SELECT created_at INTO last_reset_date
    FROM credit_usage
    WHERE description LIKE 'Monthly credit reset%'
    ORDER BY created_at DESC
    LIMIT 1;
    
    IF last_reset_date IS NULL THEN
        days_since_reset := NULL;
    ELSE
        days_since_reset := EXTRACT(DAY FROM NOW() - last_reset_date);
    END IF;
    
    RETURN json_build_object(
        'last_reset_date', last_reset_date,
        'days_since_last_reset', days_since_reset,
        'next_reset_due', days_since_reset IS NULL OR days_since_reset >= 30
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_last_credit_reset() TO authenticated;