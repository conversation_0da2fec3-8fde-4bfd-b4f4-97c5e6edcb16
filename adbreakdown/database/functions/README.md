# Database Functions

This directory contains database-related functions for AdBreakdown.

## Function Types

### SQL Functions
- **`monthly-credit-reset.sql`**: Monthly credit reset function for subscription management

### Edge Functions
Edge Functions are located in `/supabase/functions/` and deployed via Supabase CLI:

- **`run-ad-analysis`**: Main AI analysis pipeline
- **`run-llm-feature-generation`**: Content generation features  
- **`run-emotion-timeline-analysis`**: Emotion timeline analysis

## Usage

### SQL Functions
Execute directly in Supabase SQL Editor or via API:
```sql
SELECT reset_monthly_credits();
```

### Edge Functions
Deploy and manage via scripts:
```bash
# Deploy all functions
./scripts/deploy-functions.sh

# Test functions
./scripts/test-functions.sh
```

## Development

Edge Functions are developed in `/supabase/functions/` directory following Supabase CLI conventions. This directory contains only SQL functions and references to Edge Functions.