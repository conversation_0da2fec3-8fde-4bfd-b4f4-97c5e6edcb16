-- WARNING: This schema is for context only and is not meant to be run.
-- Table order and constraints may not be valid for execution.

CREATE TABLE public.ad_analyses (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  youtube_url text NOT NULL,
  status text DEFAULT 'pending'::text,
  title text,
  inferred_brand text,
  duration_seconds integer,
  duration_formatted text,
  thumbnail_url text,
  video_info text,
  marketing_analysis text,
  overall_sentiment numeric,
  emotions jsonb,
  transcript text,
  summary text,
  key_themes jsonb,
  music_mood text,
  voice_tone text,
  audio_quality integer,
  visual_appeal integer,
  color_palette jsonb,
  scenes jsonb,
  target_demographics jsonb,
  target_interests jsonb,
  target_behaviors jsonb,
  marketing_copy text,
  social_media_posts text,
  marketing_scorecard text,
  seo_keywords text,
  content_suggestions text,
  analysis_completed_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  sentiment_timeline jsonb,
  deciphered_script jsonb,
  slug character varying,
  youtube_video_id character varying,
  is_public boolean DEFAULT false,
  CONSTRAINT ad_analyses_pkey PRIMARY KEY (id),
  CONSTRAINT ad_analyses_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.analysis_reports (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  analysis_id uuid NOT NULL,
  report_type_id uuid NOT NULL,
  status text DEFAULT 'pending'::text,
  content text,
  generated_at timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT analysis_reports_pkey PRIMARY KEY (id),
  CONSTRAINT analysis_reports_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.ad_analyses(id),
  CONSTRAINT analysis_reports_report_type_id_fkey FOREIGN KEY (report_type_id) REFERENCES public.report_types(id)
);
-- TABLE REMOVED: analysis_sections was not used in the application
-- Removed in schema cleanup - originally defined for UI section organization
CREATE TABLE public.credit_usage (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  analysis_id uuid,
  report_type_id uuid,
  credits_used integer NOT NULL,
  description text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT credit_usage_pkey PRIMARY KEY (id),
  CONSTRAINT credit_usage_report_type_id_fkey FOREIGN KEY (report_type_id) REFERENCES public.report_types(id),
  CONSTRAINT credit_usage_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT credit_usage_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.ad_analyses(id)
);
CREATE TABLE public.emotion_timeline_events (
  event_id uuid NOT NULL DEFAULT uuid_generate_v4(),
  analysis_id uuid NOT NULL,
  start_time_seconds integer NOT NULL,
  end_time_seconds integer NOT NULL,
  audience_emotion character varying NOT NULL,
  intensity character varying NOT NULL,
  event_description text,
  actor_emotion character varying,
  CONSTRAINT emotion_timeline_events_pkey PRIMARY KEY (event_id),
  CONSTRAINT emotion_timeline_events_analysis_id_fkey FOREIGN KEY (analysis_id) REFERENCES public.sentiment_analyses(analysis_id)
);
CREATE TABLE public.profiles (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  user_id uuid NOT NULL,
  credits_remaining integer DEFAULT 100,
  subscription_status text DEFAULT 'free'::text,
  subscription_plan text,
  subscription_period_end timestamp with time zone,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT profiles_pkey PRIMARY KEY (id),
  CONSTRAINT profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);
CREATE TABLE public.report_types (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  name text NOT NULL UNIQUE,
  description text,
  credit_cost integer DEFAULT 1,
  CONSTRAINT report_types_pkey PRIMARY KEY (id)
);
CREATE TABLE public.sentiment_analyses (
  analysis_id uuid NOT NULL DEFAULT uuid_generate_v4(),
  ad_analysis_id uuid NOT NULL,
  overall_sentiment character varying NOT NULL,
  primary_audience_emotion character varying,
  analysis_summary text,
  analysis_timestamp timestamp with time zone DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT sentiment_analyses_pkey PRIMARY KEY (analysis_id),
  CONSTRAINT sentiment_analyses_ad_analysis_id_fkey FOREIGN KEY (ad_analysis_id) REFERENCES public.ad_analyses(id)
);
CREATE TABLE public.users (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  clerk_id text NOT NULL UNIQUE,
  email text NOT NULL,
  first_name text,
  last_name text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT users_pkey PRIMARY KEY (id)
);