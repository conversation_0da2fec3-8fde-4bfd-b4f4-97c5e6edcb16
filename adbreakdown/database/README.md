# AdBreakdown Database Directory

This directory contains all database-related files for the AdBreakdown project.

## Directory Structure

```
/database/
├── README.md                   # This file
├── current_schema.sql          # Current database schema (reference)
├── schema.sql                  # Full database dump
├── update_schema.sh           # Schema update script
├── performance-indexes.sql     # Performance optimization indexes
├── rls-policies.sql           # Row Level Security policies
├── test-data.sql              # Test data for development
├── migrations/                 # Database migrations (version controlled)
│   ├── V1_legacy_database_migration.sql
│   ├── V2_create_ad_analyses_reports_tables.sql
│   ├── V3_add_llm_report_types.sql
│   ├── V4_add_credit_functions.sql
│   ├── V5_add_sentiment_analysis_tables.sql
│   ├── V6_add_emotion_timeline_report_type.sql
│   ├── V7_add_enhanced_analysis_fields.sql
│   ├── V8_fix_deciphered_script_column_type.sql
│   ├── V9_add_slug_and_youtube_video_id.sql
│   ├── V10_populate_existing_slugs.sql
│   ├── V11_remove_analysis_sections.sql
│   ├── add_admin_submissions.sql
│   ├── add_daily_showcases_table.sql
│   ├── add_public_flag.sql
│   └── add_showcase_to_ad_analyses.sql
├── functions/                  # Database functions
│   ├── README.md              # Functions documentation
│   └── monthly-credit-reset.sql        # SQL function for credit reset
└── backups/                    # Schema backups
    └── current_schema_backup_*.sql
```

## File Descriptions

### Core Schema Files
- **`current_schema.sql`**: Reference schema file with current table structure
- **`schema.sql`**: Full database dump including all Supabase internals
- **`update_schema.sh`**: Script to update schema files based on migrations

### Migrations
- **`migrations/`**: Version-controlled database migrations
  - `V1_legacy_database_migration.sql`: Original migration (moved from root)
  - `V2-V11_*.sql`: Sequential migrations for schema evolution
  - `add_*.sql`: Feature-specific migrations

### Functions
- **`functions/`**: Database functions
  - `monthly-credit-reset.sql`: SQL function for monthly credit reset
  - `README.md`: Functions documentation

### Edge Functions
Edge Functions are located in `/supabase/functions/` (project root) and managed via Supabase CLI:
- `run-ad-analysis/`: Main AI analysis pipeline
- `run-llm-feature-generation/`: Content generation features
- `run-emotion-timeline-analysis/`: Emotion timeline analysis

### Support Files
- **`performance-indexes.sql`**: Database performance optimizations
- **`rls-policies.sql`**: Row Level Security policies
- **`test-data.sql`**: Development test data
- **`backups/`**: Automatic schema backups

## Usage

### Schema Management
```bash
# Update schema files
./database/update_schema.sh

# Check for schema differences
./scripts/schema-diff.sh

# Monitor schema changes
./scripts/watch-schema.sh
```

### Function Deployment
```bash
# Deploy all Edge Functions
./scripts/deploy-functions.sh

# Test deployed functions
./scripts/test-functions.sh
```

### Migration Management
1. Create new migration: `database/migrations/V{number}_{description}.sql`
2. Apply to database via Supabase Dashboard or CLI
3. Update schema: `./database/update_schema.sh`

## Database Tables

### Active Tables (10)
- **users**: Core user management (Clerk integration)
- **profiles**: Credit/subscription management
- **ad_analyses**: Primary analysis storage
- **analysis_reports**: Generated AI reports
- **report_types**: Report definitions & credit costs
- **credit_usage**: Transaction logging
- **sentiment_analyses**: Emotion timeline data
- **emotion_timeline_events**: Detailed emotion events
- **daily_showcases**: Featured content system
- **admin_submissions**: Admin video requests

### Removed Tables
- **analysis_sections**: Removed in V11 (unused)

## Best Practices

1. **Migrations**: Always create migrations for schema changes
2. **Backups**: Schema backups are automatically created
3. **Testing**: Test migrations on staging before production
4. **Documentation**: Document schema changes in migration files
5. **Monitoring**: Use schema monitoring scripts to track changes

## Environment Variables

Required for database operations:
- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Service role key for admin operations
- `GEMINI_API_KEY`: For AI Edge Functions

## Support

For database-related issues:
1. Check migration files for recent changes
2. Review schema backups in `/backups/`
3. Use monitoring scripts to identify differences
4. Consult CLAUDE.md and GEMINI.md for integration details