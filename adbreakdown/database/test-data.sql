-- Test Data for AdBreakdown
-- Run this in your Supabase SQL Editor after creating a user account

-- Insert test analysis data (replace 'your-clerk-user-id' with your actual Clerk user ID)
-- You can find your Clerk user ID after signing up by checking the browser console or Clerk dashboard

-- Example analysis record
INSERT INTO ad_analyses (
    id,
    user_id,
    youtube_url,
    status,
    title,
    inferred_brand,
    duration,
    thumbnail_url,
    overall_sentiment,
    created_at
) VALUES (
    'test-analysis-1',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), -- Replace with your email
    'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
    'completed',
    'Amazing Product Demo - Revolutionary Smartphone',
    'TechBrand',
    30,
    'https://i.ytimg.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    0.85,
    NOW() - INTERVAL '2 hours'
),
(
    'test-analysis-2',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), -- Replace with your email
    'https://www.youtube.com/watch?v=example123',
    'completed',
    'Fitness App Commercial - Get Fit Fast',
    'FitLife',
    45,
    'https://placehold.co/480x360/4F46E5/FFFFFF?text=Fitness+Ad',
    0.72,
    NOW() - INTERVAL '1 day'
),
(
    'test-analysis-3',
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), -- Replace with your email
    'https://www.youtube.com/watch?v=example456',
    'processing',
    'Eco-Friendly Product Launch',
    'GreenTech',
    60,
    'https://placehold.co/480x360/10B981/FFFFFF?text=Eco+Ad',
    NULL,
    NOW() - INTERVAL '10 minutes'
);

-- Insert test reports for the completed analyses
INSERT INTO analysis_reports (
    id,
    analysis_id,
    report_type_id,
    status,
    content,
    generated_at
) VALUES (
    'test-report-1',
    'test-analysis-1',
    (SELECT id FROM report_types WHERE name = 'transcript_summary'),
    'generated',
    '{
        "transcript": "Welcome to the future of smartphones! Our revolutionary device features an incredible camera system, all-day battery life, and a sleek design that fits perfectly in your hand.",
        "summary": "High-energy product demonstration showcasing key smartphone features with emphasis on camera quality, battery performance, and design aesthetics.",
        "themes": ["innovation", "technology", "lifestyle", "premium quality"]
    }',
    NOW() - INTERVAL '2 hours'
),
(
    'test-report-2',
    'test-analysis-1',
    (SELECT id FROM report_types WHERE name = 'marketing_analysis'),
    'generated',
    '{
        "effectiveness_score": 8.5,
        "target_audience": "Tech-savvy millennials and professionals aged 25-40",
        "emotional_appeal": "High - creates desire and excitement about innovation",
        "call_to_action_strength": 7,
        "brand_consistency": 9,
        "visual_quality": 9,
        "message_clarity": 8
    }',
    NOW() - INTERVAL '2 hours'
);

-- Insert some credit usage records
INSERT INTO credit_usage (
    user_id,
    analysis_id,
    report_type_id,
    credits_used,
    description
) VALUES (
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), -- Replace with your email
    'test-analysis-1',
    (SELECT id FROM report_types WHERE name = 'marketing_copy'),
    1,
    'Generated marketing copy for smartphone ad analysis'
),
(
    (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1), -- Replace with your email
    'test-analysis-2',
    (SELECT id FROM report_types WHERE name = 'social_media_posts'),
    1,
    'Generated social media posts for fitness app ad'
);

-- Note: After you sign up with Clerk, update the email in the queries above
-- to match your actual email address, then run this script.