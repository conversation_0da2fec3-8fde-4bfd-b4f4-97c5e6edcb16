-- AdBreakdown Database Schema
-- Phase 1: Users and Profiles tables

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (synced with <PERSON>)
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    clerk_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    first_name VA<PERSON>HA<PERSON>(100),
    last_name VA<PERSON><PERSON><PERSON>(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User profiles table
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    credits_remaining INTEGER DEFAULT 10, -- Free tier starts with 10 credits
    subscription_status VARCHAR(50) DEFAULT 'free', -- free, active, cancelled, expired
    subscription_plan VARCHAR(50), -- starter, pro, enterprise
    subscription_period_end TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Report types lookup table
CREATE TABLE IF NOT EXISTS report_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    credit_cost INTEGER DEFAULT 1
);

-- Insert default report types
INSERT INTO report_types (name, description, credit_cost) VALUES
    ('transcript_summary', 'Video transcript and summary analysis', 0),
    ('marketing_analysis', 'Detailed marketing effectiveness analysis', 0),
    ('marketing_copy', 'AI-generated marketing copy and headlines', 1),
    ('social_media_posts', 'Social media post suggestions', 1),
    ('marketing_scorecard', 'Marketing effectiveness scorecard', 1),
    ('seo_keywords', 'SEO keyword extraction', 1),
    ('content_suggestions', 'Content improvement suggestions', 1)
ON CONFLICT (name) DO NOTHING;

-- Ad analyses table
CREATE TABLE IF NOT EXISTS ad_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    youtube_url TEXT NOT NULL,
    status VARCHAR(50) DEFAULT 'pending', -- pending, processing, completed, error
    title TEXT,
    inferred_brand VARCHAR(255),
    duration INTEGER, -- in seconds
    thumbnail_url TEXT,
    overall_sentiment DECIMAL(3,2), -- -1.00 to 1.00
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Analysis reports table
CREATE TABLE IF NOT EXISTS analysis_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id UUID NOT NULL REFERENCES ad_analyses(id) ON DELETE CASCADE,
    report_type_id INTEGER NOT NULL REFERENCES report_types(id),
    status VARCHAR(50) DEFAULT 'pending', -- pending, generating, generated, error
    content JSONB,
    generated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Credit usage tracking
CREATE TABLE IF NOT EXISTS credit_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    analysis_id UUID REFERENCES ad_analyses(id) ON DELETE SET NULL,
    report_type_id INTEGER REFERENCES report_types(id),
    credits_used INTEGER NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_users_clerk_id ON users(clerk_id);
CREATE INDEX IF NOT EXISTS idx_profiles_user_id ON profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_user_id ON ad_analyses(user_id);
CREATE INDEX IF NOT EXISTS idx_ad_analyses_status ON ad_analyses(status);
CREATE INDEX IF NOT EXISTS idx_analysis_reports_analysis_id ON analysis_reports(analysis_id);
CREATE INDEX IF NOT EXISTS idx_analysis_reports_type ON analysis_reports(report_type_id);
CREATE INDEX IF NOT EXISTS idx_credit_usage_user_id ON credit_usage(user_id);

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE ad_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE analysis_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE credit_usage ENABLE ROW LEVEL SECURITY;

-- RLS Policies (will be implemented when Clerk integration is complete)
-- Users can only access their own data

-- Updated at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ad_analyses_updated_at BEFORE UPDATE ON ad_analyses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
