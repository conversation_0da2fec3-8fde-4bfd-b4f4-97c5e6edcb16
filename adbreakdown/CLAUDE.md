# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AdBreakdown is an AI-powered video ad analysis SaaS platform that transforms subjective ad evaluations into objective, data-driven insights. Built with Next.js 14, it uses advanced AI to analyze YouTube video ads and generate comprehensive reports.

## Architecture

### Tech Stack
- **Frontend**: Next.js 14 (App Router), React 18, TypeScript (strict mode)
- **Styling**: Tailwind CSS, ShadCN/ui components, Radix UI primitives
- **Authentication**: Clerk
- **Database**: Supabase (PostgreSQL)
- **AI/ML**: Google Gemini API for video analysis
- **Payments**: Lemon Squeezy integration
- **Video Processing**: Video.js

### Project Structure
```
/src/app/                 # Next.js App Router pages
├── /(auth)/             # Authentication pages (sign-in/sign-up) 
├── /ad/[id]/            # Dynamic analysis detail pages
├── /api/                # API routes (analyses, billing, webhooks, users)
├── /dashboard/          # Main user dashboard
└── /billing/            # Subscription management

/src/components/         # React components
├── /ui/                 # Reusable UI components (ShadCN/ui)
└── /dashboard/          # Dashboard-specific components

/src/lib/                # Utility functions and configurations
/src/hooks/              # Custom React hooks (useAuth, useCredits)
/src/services/           # API clients and external service integrations
/documentation/          # Project specifications and requirements
/database/               # Database migrations and schema
```

## Development Commands

### Common Development Tasks
```bash
# Development
npm run dev              # Start development server (http://localhost:3000)
npm run build            # Build for production
npm run start            # Start production server
npm run lint             # Run ESLint
npm run type-check       # Run TypeScript compiler without emitting files

# Database & Schema Management
./database/update_schema.sh    # Update local schema files
./scripts/schema-diff.sh       # Compare local vs remote schema
./scripts/watch-schema.sh      # Monitor schema changes continuously

# Supabase Functions Deployment
npm run deploy:functions       # Deploy all Edge Functions
npm run test:functions         # Test deployed Edge Functions
npm run setup:deployment       # Setup deployment environment

# Setup
npm install              # Install dependencies
cp .env.example .env.local  # Copy environment template
```

### Build Requirements
- Node.js (latest LTS)
- TypeScript strict mode enabled
- All type errors must be resolved before production build

## Key Development Guidelines

### Documentation Reference
All project specifications are in `/documentation/`:
- `documentation/prd.md` - Complete PRD with features, user stories, and acceptance criteria
- `documentation/user-flow.md` - Detailed user flows and navigation paths  
- `documentation/folder-structure.md` - Project folder structure
- `documentation/task-plan.md` - Phase-by-phase implementation roadmap
- `documentation/ad-id-prototype.ts` - Fully functional prototype of ad/[id]/page.tsx

### Implementation Priorities
1. **ALWAYS reference relevant docs** when implementing features
2. **Follow exact folder structure** from documentation/folder-structure.md
3. **Implement user flows** as specified in documentation/user-flow.md
4. **Follow phase progression** from documentation/task-plan.md
5. **Meet acceptance criteria** from documentation/prd.md
6. **Implement ad/[id]/page.tsx** as specified in documentation/ad-id-prototype.ts

### Current Development Phase
Track progress against task-plan.md phases:
- ✅ Phase 1: Auth & Foundation (Steps 1-15)
- ✅ Phase 2: Core AI Pipeline (Steps 16-26)  
- 🔄 Phase 3: Analysis UI (Steps 27-34)
- ⏳ Phase 4: Report Content Display (Steps 35-39)
- ⏳ Phase 5+: Advanced Features

## Core Features to Implement

1. **AI Sentiment Analysis** - Frame-by-frame emotional analysis (PRD section 3.1)
2. **Scene-by-Scene Breakdowns** - Detailed video segment analysis (PRD section 3.2)
3. **Competitor Comparison** - Automated competitor discovery and analysis (PRD section 3.3)
4. **Targeting Recommendations** - Demographic and psychographic profiling (PRD section 3.4)
5. **Actionable Suggestions** - AI-powered optimization recommendations (PRD section 3.5)

## Code Standards

### TypeScript
- Strict mode enabled (`"strict": true` in tsconfig.json)
- Use proper type annotations for all parameters
- Path aliases configured: `@/*` maps to `./src/*`
- Target: ES2017

### React/Next.js
- Use App Router (not Pages Router)
- Components in `/src/components/` with proper organization
- Custom hooks in `/src/hooks/`
- API routes in `/src/app/api/`

### Styling
- Use Tailwind CSS for all styling
- Follow ShadCN/ui component patterns
- Ensure mobile responsiveness
- Support dark/light mode where applicable

### Database
- Supabase PostgreSQL with comprehensive schema
- Use typed queries with proper error handling
- Implement proper credit system tracking
- Follow database schema from /database/ folder

### API Integration
- **Gemini API**: For video analysis and AI-powered content generation
- **Clerk**: For authentication and user management
- **Lemon Squeezy**: For subscription billing and payments
- **YouTube**: For video URL processing and metadata extraction

## Environment Setup

Required environment variables (see .env.example):

### Authentication
- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` - Clerk public key
- `CLERK_SECRET_KEY` - Clerk secret key
- `CLERK_WEBHOOK_SECRET` - Clerk webhook secret

### Database
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Supabase anon key
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key

### AI Services
- `GEMINI_API_KEY` - Google Gemini API key

### Payments
- `LEMON_SQUEEZY_API_KEY` - Lemon Squeezy API key
- `LEMON_SQUEEZY_STORE_ID` - Store ID
- `LEMON_SQUEEZY_WEBHOOK_SECRET` - Webhook secret
- `NEXT_PUBLIC_LEMON_SQUEEZY_*_VARIANT_ID` - Product variant IDs

### Application
- `NEXT_PUBLIC_APP_URL` - Application base URL
- `NEXT_PUBLIC_API_URL` - API base URL

## Testing and Quality

- Run `npm run type-check` before committing
- Run `npm run lint` to ensure code quality
- Test API endpoints thoroughly
- Verify mobile responsiveness
- Test authentication flows with Clerk
- Validate AI analysis workflows

## Key Implementation Notes

### Authentication Flow
- Use Clerk for all authentication
- Implement proper user profile management
- Handle sign-up flow with optional YouTube URL analysis
- Manage user credits and subscription status

### AI Analysis Pipeline
- Process YouTube URLs through Gemini API
- Store analysis results in Supabase
- Generate comprehensive reports with multiple sections
- Implement credit-based usage tracking

### Database Operations
- Use Supabase client for all database operations
- Implement proper error handling and loading states
- Follow established patterns for CRUD operations
- Maintain referential integrity in schema

### UI/UX Patterns
- Follow existing component patterns from /src/components/ui/
- Use consistent loading states and error handling
- Implement proper responsive design
- Support theme switching (dark/light mode)

## Critical Issue: UUID/Slug Resolution Pattern

### ⚠️ RECURRING PROBLEM
The most common and critical issue in this codebase is **UUID/Slug resolution in API routes**. This issue has occurred multiple times and MUST be prevented in all future development.

### 🔍 Problem Description
- **Frontend URLs**: Use SEO-friendly slugs like `/ad/video-analysis-j8KpV-4_mRg`
- **Database Storage**: Analysis records have both `id` (UUID) and `slug` fields
- **API Route Issue**: New API routes often assume the `[id]` parameter is always a UUID, causing failures when slugs are passed

### 🚨 Error Pattern
```
Error: invalid input syntax for type uuid: "video-analysis-j8KpV-4_mRg"
```

This happens when API routes use:
```typescript
// ❌ WRONG - Assumes id is always UUID
.eq('id', id)
```

Instead of:
```typescript
// ✅ CORRECT - Handles both UUIDs and slugs
const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
.eq(isUUID ? 'id' : 'slug', id)
```

### 📋 MANDATORY Pattern for ALL API Routes in `/api/analyses/[id]/`

**EVERY API route that handles analysis IDs MUST use this pattern:**

```typescript
export async function GET/POST/PUT/DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params
    
    // ✅ ALWAYS ADD THIS UUID/SLUG DETECTION
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id)
    
    // ✅ USE CONDITIONAL FIELD SELECTION
    const { data: analysis, error } = await supabase
      .from('ad_analyses')
      .select('*')
      .eq(isUUID ? 'id' : 'slug', id)  // ← Critical: Use conditional field
      .eq('user_id', user.id)
      .single()
    
    // ✅ FOR UPDATES/DELETES: Use the actual UUID from retrieved data
    await supabase
      .from('ad_analyses')
      .update({ ... })
      .eq('id', analysis.id)  // ← Use analysis.id, not the parameter id
      
  } catch (error) {
    // Handle errors
  }
}
```

### 🛡️ Prevention Checklist

Before creating or modifying ANY API route in `/api/analyses/[id]/`:

- [ ] **Step 1**: Add UUID detection regex pattern
- [ ] **Step 2**: Use conditional field selection in ALL queries
- [ ] **Step 3**: For updates/deletes, use `analysis.id` from retrieved data
- [ ] **Step 4**: Test with both UUID and slug URLs
- [ ] **Step 5**: Verify no direct `.eq('id', id)` usage remains

### 🔧 Routes Already Fixed
These routes have been updated with proper UUID/slug resolution:
- ✅ `/api/analyses/[id]/route.ts` (GET, DELETE)
- ✅ `/api/analyses/[id]/generate-enhanced-script/route.ts`
- ✅ `/api/analyses/[id]/generate-llm-feature/route.ts`
- ✅ `/api/analyses/[id]/trigger-initial-analysis/route.ts`
- ✅ `/api/analyses/[id]/update-ai-analysis/route.ts`
- ✅ `/api/analyses/[id]/save-generated-content/route.ts`
- ✅ `/api/analyses/[id]/basic/route.ts`
- ✅ `/api/analyses/[id]/tab-data/route.ts`
- ✅ `/api/analyses/[id]/metadata/route.ts`

### 🚨 High-Risk Routes to Monitor
When creating new routes or modifying existing ones:
- Any route in `/api/analyses/[id]/`
- Any database query using the `id` parameter
- Any route that performs user ownership verification

### 🎯 Quick Fix Command
When this error occurs, search for the problematic query:
```bash
grep -r "\.eq('id', id)" src/app/api/analyses/
```

And replace with the UUID/slug resolution pattern above.

## Critical Architectural Patterns

### 1. Progressive Loading Architecture
The analysis page uses sophisticated progressive loading:

```typescript
// Fast initial load
const basicRes = await fetch(`/api/analyses/${id}/basic`)
// Background full data load
setTimeout(() => fetchFullData(), 100)
// Tab-based lazy loading
fetch(`/api/analyses/${id}/tab-data?tab=${activeTab}`)
```

**Implementation Pattern:**
- Initial render with skeleton states
- Fast `/basic` endpoint for above-the-fold content
- Asynchronous full data loading
- Tab content loads only when accessed

### 2. Credit-First Architecture
ALL AI operations must follow this pattern:

```typescript
// 1. Pre-validation
if (!checkCredits(amount)) {
  setError('Insufficient credits')
  return
}

// 2. Deduct credits BEFORE operation
const success = await deductCredits(amount)
if (!success) {
  setError('Credit deduction failed')
  return
}

// 3. Perform operation
// 4. Handle success/failure
```

**Credit Costs:**
- Basic analysis: 1 credit
- Enhanced features: 1 credit each
- Emotion timeline: 2 credits
- Enhanced script: 3 credits

### 3. Modular AI Prompt System
Located in `/src/lib/prompts/`:

```typescript
// Each feature has dedicated prompt file
getVideoAnalysisPrompt(videoUrl)
getMarketingAnalysisPrompt(transcript, summary)
getMarketingCopyPrompt(videoInfo, marketingAnalysis)
```

**Key Patterns:**
- Centralized prompt management
- Context passing between operations
- JSON response format enforcement
- Multi-model support (flash vs pro)

### 4. Two-Phase AI Processing Pipeline

```typescript
// Phase 1: Initial Analysis (1 credit)
const initialData = await callGeminiApi(
  getVideoAnalysisPrompt(videoUrl),
  'gemini-1.5-flash',
  [videoData],
  true // JSON response
)

// Phase 2: Marketing Analysis (uses Phase 1 output)
const marketingAnalysis = await callGeminiApi(
  getMarketingAnalysisPrompt(initialData.transcript, initialData.summary),
  'gemini-1.5-flash'
)

// Phase 3: On-demand features (use accumulated context)
```

### 5. Composite Data Architecture
The analysis page synthesizes data from multiple sources:

```typescript
// Database: Core analysis + generated reports
const analysis = await supabase.from('ad_analyses')
const reports = await supabase.from('analysis_reports')

// External APIs: YouTube metadata
const youtubeData = await fetch(`youtube/v3/videos?id=${videoId}`)

// Dynamic synthesis into unified interface
parseAndSetData(combinedData)
```

### 6. Advanced Authentication Integration

```typescript
// Dual authentication system
const { isAuthenticated } = useAuth() // Clerk frontend
const { user } = auth() // Server-side authentication

// Auto-fallback user creation
if (!user) {
  await createUserIfNotExists(clerkUserId)
}
```

### 7. Component State Management Patterns

```typescript
// Complex interaction states
const [loadedTabs, setLoadedTabs] = useState<Set<string>>(new Set(['overview']))
const [activeTab, setActiveTab] = useState('overview')

// Tab change handler with lazy loading
const handleTabChange = (newTab: string) => {
  if (!loadedTabs.has(newTab)) {
    preloadTabData(newTab)
    setLoadedTabs(prev => new Set([...prev, newTab]))
  }
}
```

## Advanced Debugging Patterns

### UUID/Slug Resolution Debugging
```bash
# Find problematic queries
grep -r "\.eq('id', id)" src/app/api/analyses/

# Test both formats
curl /api/analyses/550e8400-e29b-41d4-a716-************
curl /api/analyses/video-analysis-abc123
```

### Credit System Debugging
```typescript
// Check credit balance
const { credits, checkCredits } = useCredits()
console.log('💳 Credits:', credits, 'Can afford 3?', checkCredits(3))

// Trace credit deduction
const success = await deductCredits(amount)
console.log('💰 Credit deduction:', success ? 'SUCCESS' : 'FAILED')
```

### Progressive Loading Debugging
```typescript
// Track loading phases
console.log('⚡ Initial loading:', isInitialLoading)
console.log('🔄 Full loading:', loading)
console.log('📑 Loaded tabs:', Array.from(loadedTabs))
```

## Database Schema Management

### Current Schema State
- **Tables**: 10 active tables (analysis_sections removed in V11)
- **Indexes**: 13 performance indexes
- **Functions**: 1 trigger function + 3 Edge Functions
- **Triggers**: 3 updated_at triggers

### Schema Files Structure
```
/database/
├── README.md                   # Database documentation
├── current_schema.sql          # Updated reference schema
├── schema.sql                  # Full database dump
├── migrations/                 # Version-controlled migrations
├── functions/                  # SQL functions only
├── backups/                    # Schema backups
└── update_schema.sh           # Schema update script

/supabase/                      # Supabase CLI files
├── config.toml                 # Supabase configuration
└── functions/                  # Edge Functions for deployment
```

### Database Tables Overview
- **users**: Core user management (Clerk integration)
- **profiles**: Credit/subscription management
- **ad_analyses**: Primary analysis storage (heavily used)
- **analysis_reports**: Generated AI reports
- **report_types**: Report definitions & credit costs
- **credit_usage**: Transaction logging
- **sentiment_analyses**: Emotion timeline data
- **emotion_timeline_events**: Detailed emotion events
- **daily_showcases**: Featured content system
- **admin_submissions**: Admin video requests

### Schema Monitoring
```bash
# Check for schema differences
./scripts/schema-diff.sh

# Monitor changes continuously
./scripts/watch-schema.sh

# Update local schema files
./database/update_schema.sh
```

## Supabase Functions Deployment

### Function Structure
```
/supabase/functions/
├── run-ad-analysis/            # Main AI analysis pipeline
├── run-llm-feature-generation/ # Content generation
├── run-emotion-timeline-analysis/ # Emotion analysis
└── _shared/                    # Shared configuration
```

### Deployment Commands
```bash
# Setup deployment environment
./scripts/setup-deployment.sh

# Deploy all functions
./scripts/deploy-functions.sh

# Test deployed functions
./scripts/test-functions.sh
```

### Function Environment Variables
Each function requires:
- `SUPABASE_URL`
- `SUPABASE_SERVICE_ROLE_KEY`
- `GEMINI_API_KEY`

## Architecture Alignment

The current Next.js App Router implementation should continue to follow the documented specifications while maintaining these established patterns. When implementing new features, reference the prototype files in /documentation/ for exact implementation guidance.

**Key architectural decisions that make AdBreakdown unique:**
- Progressive loading for performance
- Credit-first AI operations
- UUID/slug dual resolution
- Composite data synthesis
- Modular prompt management
- Two-phase AI processing
- Automated schema monitoring
- Edge function deployment pipeline