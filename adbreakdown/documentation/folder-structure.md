adbreakdown/
├── public/
│   └── index.html
├── src/
│   ├── assets/
│   │   ├── images/
│   │   └── styles/
│   ├── components/
│   │   ├── common/
│   │   ├── ui/             # General UI components (e.g., custom buttons, cards)
│   │   └── clerk-ui/       # Components specifically integrating with Clerk's UI (e.g., custom sign-in/sign-up forms if not using Clerk's defaults)
│   ├── contexts/
│   ├── hooks/
│   │   └── useAuth.ts      # Custom hook for authentication state (leveraging Clerk)
│   ├── layouts/
│   ├── pages/
│   │   ├── auth/           # Authentication pages (e.g., login, signup, handled by Clerk's routing)
│   │   ├── dashboard/      # Homepage after login for new analysis & history
│   │   ├── analysis/       # The /ad/:adId page for detailed analysis
│   │   ├── settings/       # User settings page
│   │   └── billing/        # Subscription and payment management (integrates with Lemon Squeezy)
│   ├── services/
│   │   ├── api.ts          # API client for backend communication
│   │   ├── auth.ts         # Authentication service layer (Clerk integration logic)
│   │   └── payment.ts      # Payment service layer (Lemon Squeezy integration logic)
│   ├── utils/
│   ├── App.tsx
│   ├── index.tsx
│   └── react-app-env.d.ts
├── server/
│   ├── src/
│   │   ├── controllers/
│   │   ├── routes/
│   │   │   ├── auth.ts      # Routes for Clerk webhooks/callbacks
│   │   │   └── payment.ts   # Routes for Lemon Squeezy webhooks
│   │   ├── services/
│   │   │   ├── auth.ts      # Backend services related to authentication (if needed beyond Clerk webhooks)
│   │   │   └── payment.ts   # Backend services related to Lemon Squeezy (e.g., subscription management)
│   │   ├── models/
│   │   ├── middleware/
│   │   ├── config/
│   │   ├── app.ts
│   │   └── server.ts
│   ├── tests/
│   └── package.json
├── ai-service/
│   ├── src/
│   │   ├── models/
│   │   ├── scripts/
│   │   ├── utils/
│   │   └── main.py         # Entry point for AI analysis via Gemini API
│   ├── notebooks/
│   ├── requirements.txt
│   └── Dockerfile
├── database/
│   ├── migrations/
│   ├── seeds/
│   └── schema.sql
├── .env
├── .gitignore
├── package.json            # Main package.json for monorepo (if used) or frontend
├── tsconfig.json
├── tailwind.config.js
├── postcss.config.js
└── README.md