```markdown
# AdBreakdown - Task Plan for AI coding agents (Updated)

This plan prioritizes early testing of the core ad analysis generation pipeline, integrating authentication and payment from the start.

## Phase 1: Foundational Setup & Authentication (Steps 1-15)

 1. **Initialize Project:** Set up project directory (AdBreakdown), initialize Git.

 2. **Setup Next.js App:** Create Next.js app (TypeScript, App Router).

 3. **UI Framework Setup:** Integrate Tailwind CSS. Initialize ShadCN/ui. Add basic components like `<Button>`, `<Card>`, `<Badge>`, `<Progress>`, `<Tabs>`.

 4. **Supabase Project Setup:** Create Supabase Cloud project. Note keys/URL.

 5. **Supabase Local Dev Setup:** Install/Setup Supabase CLI, init, login, link.

 6. **Clerk Setup:** Create Clerk application. Note keys. Configure providers. Configure JWT template if needed.

 7. **Environment Variable Config:** Setup `.env.local` & Vercel env vars (Clerk, Supabase, Gemini API, Lemon Squeezy placeholder). **Verify.**

 8. **Clerk Frontend Integration:** Install `@clerk/nextjs`. Wrap root layout with `<ClerkProvider>`. Implement basic Clerk sign-in/sign-up components in `/app/(auth)/` routes.

 9. **Clerk Middleware Setup:** Create `middleware.ts` using `authMiddleware`. Define public/protected routes (e.g., `/` public, `/dashboard` and `/ad/:id` protected).

10. **Basic Protected Page:** Create a simple `/dashboard/page.tsx` protected by middleware, showing user info from Clerk.

11. **Test Auth Flow:** Deploy. **Verify:** Signup, Login, Logout, Redirection, Route Protection.

12. **DB Schema Migration (Users & Profiles):** Create `V1_create_users_and_profiles_tables.sql` migration (`users` table linked to Clerk ID, `profiles` table for `credits_remaining`, `subscription_status`). Apply migration.

13. **Clerk Webhook Handler (User Creation & Update):** Implement `/api/webhooks/clerk` endpoint. Verify signature. Handle `user.created` event (insert into Supabase `users` and `profiles` tables). Handle `user.updated` (update profile if needed).

14. **Test Webhook & DB Sync:** Configure webhook. Sign up. **Verify:** Webhook received, user and profile records created/updated in Supabase DB. **(Milestone: Auth Layer Tested & Integrated)**

15. **Lemon Squeezy Webhook Placeholder:** Create `/api/webhooks/lemon-squeezy` endpoint placeholder.

## Phase 2: Core Ad Analysis & Initial Generation Test (Steps 16-29)

16. **DB Schema Migration (Ad Analyses & Reports):** Create `V2_create_ad_analyses_reports_tables.sql` migration (`ad_analyses` table with `user_id`, `youtube_url`, `status`, `title`, `inferred_brand`, `duration`). Also, `analysis_reports` table (`analysis_id`, `report_type_id`, `status`, `content` (JSONB), `generated_at`). Add `report_types` lookup table (e.g., 'transcript_summary', 'marketing_analysis'). Apply migration.

17. **API: Implement POST /api/analyses:** Create endpoint. Use Clerk `auth()` helper to get `userId`. Inserts an `ad_analyses` record (status='pending'). Returns `analysis_id`.

18. **API: Implement POST /api/analyses/{id}/trigger-initial-analysis:** Create endpoint. Use Clerk `auth()` and ownership check. Takes `{ youtubeUrl: string }`. Creates `analysis_reports` placeholders for `transcript_summary` and `marketing_analysis` (status='generating'). Triggers Supabase Function `run-ad-analysis`. Returns 202 Accepted.

19. **Supabase Fn: Setup `run-ad-analysis` (v1 - Initial Analysis):** Create basic TS function structure. Add `google-generativeai` dependency.

20. **Supabase Fn: Implement Logic (v1):** Receive context (`analysis_id`, `user_id`, `youtubeUrl`).

    * Call Gemini API (`gemini-1.5-flash`) for combined transcript, summary, and inferred metadata (title, brand, duration, sentiment, emotions, themes, music mood, voice tone, targeting) in **JSON format**.

    * Update the `ad_analyses` record with inferred `title`, `inferred_brand`, `duration`, and `status='processing'`.

    * Update `analysis_reports` record for `transcript_summary` with `content` and `status='generated'`.

    * Call Gemini API again for the detailed 10-point marketing analysis using the transcript/summary as context.

    * Update `analysis_reports` record for `marketing_analysis` with `content` and `status='generated'`.

    * Handle errors gracefully, updating relevant `status='error'`. Basic error logging.

21. **Supabase Fn: Deploy Function.**

22. **Frontend (`/dashboard/page.tsx` - Temporary Trigger UI):** On the `/dashboard` page, add:

    * `<Input>` for YouTube URL.

    * `<Button>` "Analyze Ad".

23. **Frontend Logic:** Wire the button:

    * Call POST `/api/analyses`.

    * On success, get `analysis_id`.

    * Call POST `/api/analyses/{analysis_id}/trigger-initial-analysis` with the YouTube URL.

    * Display basic feedback (e.g., Toast "Analysis started..."). Redirect to `/ad/[analysis_id]`.

24. **UI: Build Analysis Report Page Layout (`/ad/[id]/page.tsx`):** Implement the core layout with Video Player, Metadata, and the Tabbed Interface (Overview, Sentiment, Script, Visual, Audio, Targeting). Initially, populate with mock/placeholder data.

25. **API: Implement GET /api/analyses/{id} (View Initial Data):** Endpoint to fetch `ad_analyses` details + initial `analysis_reports` list (`id`, `report_type_id`, `status`). Add Clerk `auth()` and ownership check.

26. **FE: Connect Analysis Report Page:** Fetch `ad_analyses` data via `/api/analyses/{id}`. Use `useEffect` to populate `videoMetadata` and `detailedAnalysisData` with the data from the `transcript_summary` and `marketing_analysis` reports once they are available. Display loading states for individual sections.

27. **Testing & Verification:**

    * Manually trigger the flow using the temporary UI.

    * Check Supabase DB `ad_analyses` table for new analysis record, updated `title`, `brand`, `duration`.

    * Check Supabase DB `analysis_reports` for generated `transcript_summary` and `marketing_analysis` content.

    * Check Supabase Function logs.

    * **Verify** the `/ad/[id]` page loads and displays the inferred metadata, transcript/summary in the Script tab, and marketing analysis in the Overview tab.

28. **Refine & Debug:** Debug any issues in the entire pipeline until it works reliably for the initial analysis.

29. **(Milestone: Core AI-driven Ad Analysis Pipeline & Basic Report Viewing Tested)**

## Phase 3: Dashboard & Analysis Listing (Steps 30-33)

30. **API: Implement GET /api/analyses (List/Search):** Implement endpoint to fetch a list of `ad_analyses` for the authenticated `userId`. Add Clerk `auth()`. Allow basic filtering/sorting.

31. **UI: Enhance Dashboard (`/dashboard/page.tsx`):**

    * Replace temporary form with `AnalysisInputCard` component.

    * Add a "Recent Analyses Grid" section.

    * Fetch analyses via `/api/analyses` and render them using `AnalysisCard` components (displaying title, thumbnail, status).

    * Implement basic pagination/load more if applicable.

32. **UI: Implement AnalysisCard Component:** Display title, thumbnail (from `ad_analyses.thumbnail`), inferred brand, overall sentiment (from `ad_analyses.overall_sentiment` if added, or fetched from `analysis_reports`), link to `/ad/:id`.

33. **Test Dashboard:** **Verify:** Dashboard displays recent analyses, clicking a card navigates to the analysis page. **(Milestone: Basic Ad Analysis Management UI Operational)**

## Phase 4: LLM-Powered Feature Generation (Steps 34-40)

34. **DB: Extend `report_types` table:** Add entries for 'marketing_copy', 'social_media_posts', 'marketing_scorecard', 'seo_keywords', 'content_suggestions'.

35. **API: Implement POST /api/analyses/{id}/generate-llm-feature:** A generic endpoint to trigger secondary LLM features.

    * Takes `analysis_id`, `report_type_id` (e.g., 'marketing_copy').

    * Add Clerk `auth()` and ownership check.

    * Add **Credit Check** logic (from `profiles.credits_remaining`). If insufficient, return 402 Payment Required.

    * Creates new `analysis_reports` record (status='generating').

    * Triggers Supabase Function `run-llm-feature-generation` with relevant context. Returns 202 Accepted.

36. **Supabase Fn: Setup `run-llm-feature-generation`:** Create new TS function. Receive `analysis_id`, `report_type_id`, `user_id`.

    * Fetch necessary existing reports (transcript, marketing analysis) to use as context.

    * Use `report_type_id` to determine which Gemini API prompt to use (for copy, social, scorecard, keywords, suggestions).

    * Call Gemini API accordingly (`gemini-1.5-flash`).

    * Update relevant `analysis_reports` record with `content` and `status='generated'`.

    * **Deduct credits** from `profiles.credits_remaining` for the `user_id`. Log credit usage.

    * Handle errors, update status.

37. **Supabase Fn: Deploy Function.**

38. **FE: Wire Action Buttons (`/ad/[id]/page.tsx`):**

    * Connect "Generate Marketing Copy", "Propose Social Media Posts", "Generate Marketing Scorecard", "Extract SEO Keywords", "Suggest Content Improvements" buttons to call `/api/analyses/{id}/generate-llm-feature` with respective `report_type_id`.

    * Manage loading states for each button.

    * Handle 402 responses (redirect to billing/show upgrade message).

39. **FE: Display Generated Content:** Add conditional rendering for `marketingCopy`, `socialMediaPosts`, `marketingScorecard`, `seoKeywords`, `contentSuggestions` sections. Fetch these reports via `GET /api/analyses/{id}` or a new `GET /api/reports/{id}` endpoint and display.

40. **Testing LLM Features:**

    * **Verify:** Each button triggers the correct Gemini API call.

    * **Verify:** Generated content displays correctly.

    * **Verify:** Credit deduction works.

    * **Verify:** Error handling (e.g., insufficient credits).

    * **Verify:** Markdown rendering in UI for scorecard, keywords, etc.
      \*\* (Milestone: All LLM-Powered Features Operational & Integrated)\*\*

## Phase 5: Tiers, Payments & Final Polish (Steps 41-48)

41. **Lemon Squeezy Integration (Backend):** Finalize `/api/webhooks/lemon-squeezy` handler.

    * Listen for `subscription_created`, `subscription_updated`, `subscription_cancelled` events.

    * Update `profiles.subscription_status` and `profiles.credits_remaining` (e.g., grant monthly credits) in Supabase DB based on subscription changes.

42. **UI: Build Billing/Subscription Page (`/billing/page.tsx`):**

    * Display current subscription status and remaining credits.

    * Add "Manage Subscription" button (linking to Lemon Squeezy's customer portal URL).

    * Show pricing tiers and "Upgrade" / "Downgrade" buttons (linking to Lemon Squeezy checkout URLs).

    * Implement invoice history (via Lemon Squeezy API or customer portal link).

43. **UI: Implement Credit Limit Handling:** Display remaining credits on dashboard/analysis page. Show clear messages when credits are low or exhausted, with calls to action to upgrade.

44. **Supabase Fn: Implement & Schedule `monthly-credit-reset`:** A Supabase job/function to reset monthly credits for active subscribers.

45. **Testing & Verification (Full E2E):**

    * Comprehensive E2E tests (Playwright) for auth, analysis, LLM features, subscription changes (via LS test events).

    * Unit/integration tests (Jest/Vitest) for core logic.

46. **UI Polish & Accessibility:** Review responsiveness, accessibility (WCAG 2.1 AA), loading/error states, empty states. Finalize all UI copy.

47. **Production Config:** Finalize Vercel/Supabase production settings. Set up API keys securely.

48. **Deploy & Monitor:** Deploy to production. Set up monitoring/alerting for API usage, errors, and system health.
    **(Milestone: Production Ready Application)**
```