I understand you would like the content of the "User Flow – AdBreakdown (Updated)" Canvas artifact to be converted to pure Markdown. Here is the requested output:

````markdown
# User Flow – AdBreakdown (Updated)

## 1. Persona & Context

* **Persona**: Advertising professionals, digital marketing agencies, and small business owners seeking to optimize video ad campaigns.
* **Objective**: To obtain a comprehensive, data-driven analysis of a YouTube video ad, generate creative assets, and identify areas for improvement and benchmarking.

## 2. Preconditions & Postconditions

* **Preconditions**:
    * User has identified a YouTube video ad they wish to analyze.
    * User has an internet connection and a web browser.
    * **User is authenticated via Clerk (for most features).**
    * **User has an active subscription via Lemon Squeezy (for premium features).**
* **Postconditions**: User has received a detailed analysis report for the specified ad, including insights, recommendations, and AI-generated creative assets.

## 3. Entry Point

* **Entry path**: User navigates to the AdBreakdown web application URL.
* **Preconditions**: User has a web browser and internet access.
* **Postconditions**: AdBreakdown landing page is displayed, and the system is ready for authentication or input.

## 4. Onboarding & Authentication Flow

* **Preconditions**: User is visiting AdBreakdown.
* **Steps**:
    1.  User lands on the AdBreakdown homepage (`/`).
    2.  **System checks authentication status (Clerk).**
    3.  **Decision**: Is the user logged in?
        * **No**:
            * User is prompted to **Log In or Sign Up (via Clerk's UI components).**
            * User completes Clerk's authentication flow (email/password, social login, etc.).
            * **Error Path**: Authentication fails (e.g., incorrect credentials). Clerk handles error display; user can retry.
        * **Yes**:
            * **System checks user's subscription status (via Lemon Squeezy integration, typically after login).**
            * **Decision**: Does the user have an active subscription?
                * **No**:
                    * User is informed of premium features and prompted to **Subscribe (via Lemon Squeezy checkout flow).**
                    * User completes payment process.
                    * **Error Path**: Payment fails. Lemon Squeezy handles error display; user can retry or contact support.
                * **Yes**: User is granted full access.
    4.  User is redirected to the main analysis input area or dashboard.
* **Swimlanes/Roles**:
    * **User**: Navigates, authenticates, interacts with payment.
    * **System**: Displays UI, manages authentication (Clerk), manages subscriptions (Lemon Squeezy), registers input.
* **Flowchart**
    ```mermaid
    flowchart TD
      A[Start - User Opens Browser] --> B[AdBreakdown Homepage Loads]
      B --> C{User Logged In? (Clerk)}
      C -->|No| D[Prompt Log In / Sign Up (Clerk UI)]
      D --> E[User Completes Clerk Auth Flow]
      E --> F{Auth Success?}
      F -->|No| D
      F -->|Yes| G[Check Subscription Status (Lemon Squeezy)]
      C -->|Yes| G
      G --> H{Active Subscription?}
      H -->|No| I[Prompt Subscribe (Lemon Squeezy Checkout)]
      I --> J[User Completes Lemon Squeezy Payment]
      J --> K{Payment Success?}
      K -->|No| I
      K -->|Yes| L[Full App Access Granted]
      H -->|Yes| L
      L --> M[End - Ready for Analysis Input]
    ```
* **Postconditions**: User is authenticated, has appropriate access rights, and is ready to input a YouTube video link for analysis.

## 5. Primary Navigation Flow

### 1. YouTube Video Input & Initial Analysis Trigger

* **Entry path**: User is on the AdBreakdown homepage or redirected to the analysis input area.
* **Core interaction**: User pastes a YouTube video URL into the input field and submits it.
* **Decision Points & Error Paths**:
    * **Decision**: Is the input a valid public YouTube URL?
        * **Valid**: Proceed to processing.
        * **Invalid**: Display error message "Please enter a valid YouTube URL." User can retry.
    * **Decision**: Is the video publicly accessible and within Gemini API terms?
        * **Yes**: Proceed to analysis.
        * **No**: Display error message "Video is private, restricted, or cannot be analyzed." User can retry with a different link.
* **Swimlanes/Roles**:
    * **User**: Inputs URL, reviews errors.
    * **System**: Validates URL, initiates Gemini API calls for video analysis and metadata inference.
* **Next-step trigger**: Valid YouTube URL is submitted, and initial analysis (metadata, transcript, summary, marketing analysis) is completed.
* **Flowchart**
    ```mermaid
    flowchart TD
      A[Start - Analysis Input Area] --> B[Input YouTube Link Field]
      B --> C[User Pastes URL]
      C --> D[Click "Analyze" Button]
      D --> E{Valid & Public YouTube URL?}
      E -->|No| F[Display "Invalid/Restricted URL" Error]
      F --> G[Retry Input]
      E -->|Yes| H[System Initiates Gemini API Calls]
      H --> I[Display Analysis Progress/Loading State]
      I --> J[Initial Analysis Complete (Metadata, Transcript, Marketing Analysis)]
      J --> K[End - Load Analysis Report Page (/ad/[id])]
    ```
* **Metrics & Success Criteria**: Percentage of valid URL submissions leading to analysis. Time taken from submission to report generation.

### 2. Ad Analysis Report Viewing & LLM-Powered Feature Generation

* **Entry path**: Analysis of a YouTube video is completed, and the user is on the `/ad/[id]` page.
* **Core interaction**: User navigates through the tabbed interface to view comprehensive analysis. User can then trigger additional AI-powered features.
* **UI Components & Interactions**:
    * **Video Player Section**: Displays video thumbnail, title, overall sentiment, and duration. Mock playback controls and progress bar.
    * **Video Metadata Section**: Displays inferred Video Title and Inferred Brand Name.
    * **Tabbed Interface (Overview, Sentiment, Script, Visual, Audio, Targeting)**:
        * **Overview Tab**:
            * **Expert Marketing Analysis (from initial analysis)**: Displays the detailed 10-point marketing breakdown.
            * Summary cards for Sentiment Score, Visual Appeal, Audio Quality, Target Match.
            * Competitor Comparison (currently mocked data).
        * **Sentiment Tab**: Emotion Distribution (inferred by AI) and Sentiment Timeline (currently mocked).
        * **Script Tab**:
            * **Video Transcript & Summary (from initial analysis)**: Displays the full transcript and concise summary.
            * Script Analysis section with Key Themes (inferred by AI).
        * **Visual Tab**: Visual Analysis (Visual Appeal, Color Palette, Scene Breakdown - mostly mocked, basic inference).
        * **Audio Tab**: Audio Analysis (Music Mood, Voice Tone - inferred by AI; Sound Effects, Audio Quality - mocked/simplified).
        * **Targeting Tab**: Targeting Recommendations (Demographics, Interests, Behaviors - inferred by AI; Key Recommendations - derived from analysis).
    * **Action Buttons for LLM Features (visible after initial analysis)**:
        * "Generate Marketing Copy ✨" (triggers `generateMarketingCopy` function)
        * "Propose Social Media Posts ✨" (triggers `proposeSocialMediaPosts` function)
        * "Generate Marketing Scorecard 📊" (triggers `generateMarketingScorecard` function)
        * "Extract SEO Keywords 🔍" (triggers `extractSeoKeywords` function)
        * "Suggest Content Improvements 💡" (triggers `suggestContentImprovements` function)
    * **Dedicated Result Sections (outside tabs, appear dynamically)**: For Marketing Copy, Social Media Posts, Marketing Scorecard, SEO Keywords, and Content Improvement Suggestions. These appear once generated by their respective buttons.
* **Decision Points & Error Paths**:
    * **Decision**: Does the user have a subscription to access a specific premium LLM feature?
        * **Yes**: Feature button is enabled, proceeds with generation.
        * **No**: Feature button is disabled or triggers a "Upgrade to access" modal/redirect to Lemon Squeezy.
    * **Error**: Gemini API call for a specific feature fails. Display clear error message. User can retry that specific generation.
* **Swimlanes/Roles**:
    * **User**: Views report, interacts with tabs, clicks buttons for new generations.
    * **System**: Renders report, makes subsequent Gemini API calls, displays generated content, handles loading/errors.
* **Next-step trigger**: User closes the report, initiates a new analysis, or navigates to another part of the website.
* **Flowchart**
    ```mermaid
    flowchart TD
      A[Start - Analysis Report Loaded (/ad/[id])] --> B[Display Video Player & Metadata]
      B --> C[Display Tabbed Analysis Interface]
      C --> D{User Selects Tab?}
      D -->|Overview| E[Display Expert Marketing Analysis & Overview Metrics]
      D -->|Sentiment| F[Display Emotion Distribution & Sentiment Timeline]
      D -->|Script| G[Display Video Transcript & Summary & Script Analysis]
      D -->|Visual| H[Display Visual Analysis]
      D -->|Audio| I[Display Audio Analysis]
      D -->|Targeting| J[Display Targeting Recommendations]
    
      E --> K{Trigger LLM Feature?}
      F --> K
      G --> K
      H --> K
      I --> K
      J --> K
    
      K -->|Generate Copy| L[Trigger Generate Marketing Copy Function]
      K -->|Propose Social Posts| M[Trigger Propose Social Media Posts Function]
      K -->|Generate Scorecard| N[Trigger Generate Marketing Scorecard Function]
      K -->|Extract SEO Keywords| O[Trigger Extract SEO Keywords Function]
      K -->|Suggest Improvements| P[Trigger Suggest Content Improvements Function]
    
      L --> Q[Display Generated Marketing Copy]
      M --> R[Display Proposed Social Media Posts]
      N --> S[Display Marketing Scorecard]
      O --> T[Display Extracted SEO Keywords]
      P --> U[Display Content Improvement Suggestions]
    
      Q --> V{Explore More / New Analysis?}
      R --> V
      S --> V
      T --> V
      U --> V
      V -->|Yes| C
      V -->|No| W[End - User Completes Task]
    ```
* **Metrics & Success Criteria**: Average time spent on report page. Number of interactions with each tab. Number of times each LLM feature button is clicked. User satisfaction with the generated creative assets and recommendations.

### 3. Competitor Ad Comparison (Future Enhancement)

* **Entry path**: User is viewing an ad analysis report and initiates a competitor comparison. This could be a dedicated button or tab entry.
* **Core interaction**: User inputs competitor brand names, keywords, or industries. System suggests relevant competitor ads for selection. User selects competitors, and the system generates a comparison report.
* **Decision Points & Error Paths**:
    * **Decision**: Is competitor input valid (e.g., recognized brand, relevant keyword)?
        * **Valid**: System suggests competitors.
        * **Invalid**: Display "No results found for your query." User can retry.
    * **Decision**: Does the user select enough competitors for a meaningful comparison?
        * **Yes**: Proceed to comparison generation.
        * **No (e.g., 0 selected)**: Prompt user to select at least one competitor.
* **Swimlanes/Roles**:
    * **User**: Enters competitor criteria, selects ads.
    * **System**: Searches, suggests, processes comparison.
* **Next-step trigger**: Competitor comparison report is generated.
* **Flowchart**
    ```mermaid
    flowchart TD
      A[Start - Viewing Analysis Report] --> B[Click "Compare Competitors" Button/Tab]
      B --> C[Display Competitor Input Form]
      C --> D[User Enters Criteria]
      D --> E[System Searches & Suggests Competitors]
      E --> F{Competitors Found?}
      F -->|No| G[Display "No Results" Message]
      G --> C
      F -->|Yes| H[User Selects Competitor Ads]
      H --> I[Click "Generate Comparison" Button]
      I --> J[System Generates Comparison Report]
      J --> K[Display Competitor Comparison Report]
      K --> L[End - User Views Comparison]
    ```
* **Metrics & Success Criteria**: Number of competitor comparison reports generated. User engagement with comparison insights.

## 6. Completion Flow

* **Steps**:
    1.  User has completed reviewing an analysis or comparison report.
    2.  User navigates away, starts a new analysis, or logs out.
* **Decision Points & Error Paths**:
    * **Decision**: Does user want to save/export the report (future feature)?
        * **Yes**: Trigger corresponding save/export flow.
        * **No**: Proceed to exit or new task.
* **Metrics & Success Criteria**: Number of new analyses initiated after viewing a report.
* **Postconditions**: User's task (ad analysis, creative generation, or comparison) is complete. The system is ready for the next action.

## 7. Loopbacks & Exit Paths

* **Supporting paths**:
    * **New Analysis**: From any report view, user can click "New Analysis" (or return to home) to return to the YouTube link input screen.
    * **History/Dashboard (future)**: User navigates to a dashboard to view past analyses.
* **Exit paths**:
    * **Close Browser Tab**: User closes the AdBreakdown application tab.
    * **Navigate Away**: User enters a new URL in the browser.
    * **Logout (Clerk)**: User explicitly logs out via the Clerk-provided UI.
* **Flowchart**
    ```mermaid
    flowchart TD
      A[Any Report View] --> B[Click "New Analysis" / Go Home]
      B --> C[Go to YouTube Link Input]
      A --> D[User Closes Tab]
      D --> E[Exit Application]
      A --> F[User Navigates Away]
      F --> E
      A --> G[Click "Home/Dashboard"]
      G --> H[Go to Main Dashboard (Future)]
      A --> I[Click "Logout" (Clerk)]
      I --> J[Clerk Logout Process]
      J --> K[Redirect to Homepage / Login]
    ```
````