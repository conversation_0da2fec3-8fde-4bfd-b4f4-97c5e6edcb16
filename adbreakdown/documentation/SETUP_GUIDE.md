# AdBreakdown Setup Guide

## 🎯 Current Status
- ✅ **Clerk Authentication**: Configured and working
- ✅ **Supabase Keys**: Real keys configured
- ✅ **Gemini AI Key**: Configured  
- ✅ **Application Code**: Complete and ready
- ⏳ **Database Setup**: Need to run schema
- ⏳ **Webhook Configuration**: Need to set up endpoints

## 📋 Required Setup Steps

### 1. Database Setup (Required)

**Run the following SQL scripts in your Supabase SQL Editor:**

1. **Main Schema**: Copy and run `database/schema.sql`
2. **RLS Policies**: Copy and run `database/rls-policies.sql`
3. **Credit Functions**: Copy and run `database/migrations/V4_add_credit_functions.sql`
4. **Monthly Reset**: Copy and run `database/functions/monthly-credit-reset.sql`

**To run these:**
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Select your project: `elossghirdivbobfycob`
3. Go to **SQL Editor**
4. Copy each file content and execute

### 2. Clerk Webhook Setup (Required for User Registration)

**Configure Clerk webhook to create user profiles:**

1. Go to [Clerk Dashboard](https://dashboard.clerk.com)
2. Navigate to **Webhooks**
3. Click **+ Add Endpoint**
4. Set URL: `https://your-domain.com/api/webhooks/clerk`
   - For local testing: Use ngrok or similar tunnel
   - For production: Your deployed URL
5. Select events: `user.created`, `user.updated`, `user.deleted`
6. Update `.env.local` with the webhook secret

### 3. Supabase Edge Functions (Optional - for full AI functionality)

**Deploy AI processing functions:**

1. Install Supabase CLI: `npm install -g supabase`
2. Login: `supabase login`
3. Link project: `supabase link --project-ref elossghirdivbobfycob`
4. Deploy functions:
   ```bash
   supabase functions deploy run-ad-analysis
   supabase functions deploy run-llm-feature-generation
   ```

### 4. Environment Variables Checklist

Make sure your `.env.local` has all required values:

```env
# Clerk (✅ Configured)
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_...
CLERK_SECRET_KEY=sk_test_...
CLERK_WEBHOOK_SECRET=whsec_... # Update with real webhook secret

# Supabase (✅ Configured)
NEXT_PUBLIC_SUPABASE_URL=https://elossghirdivbobfycob.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGci...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGci...

# Gemini AI (✅ Configured)
GEMINI_API_KEY=AIzaSyBoI6_LcEKmsmLVJXE7Q_Fe72WmwlqOL9M

# Lemon Squeezy (⏳ Optional for payments)
LEMON_SQUEEZY_API_KEY=...
LEMON_SQUEEZY_STORE_ID=...
# ... other Lemon Squeezy keys
```

## 🧪 Testing Steps

### After Database Setup:

1. **Test User Registration**:
   - Go to http://localhost:3000
   - Click "Get Started" 
   - Create a new account
   - Check if user appears in Supabase `users` and `profiles` tables

2. **Test Dashboard Access**:
   - After registration, should redirect to `/dashboard`
   - Should see your credit balance
   - Should see analysis input form

3. **Test Database Connection**:
   - Dashboard should load without errors
   - Profile API should return user data

### After Edge Functions Setup:

4. **Test Video Analysis**:
   - Enter a YouTube URL
   - Should create analysis record
   - Should show processing status

5. **Test LLM Features**:
   - Go to an analysis page
   - Click LLM feature buttons
   - Should generate content (requires credits)

## 🚀 What Works Right Now

With just the database setup, you'll have:
- ✅ **User authentication and registration**
- ✅ **Dashboard with real user data**
- ✅ **Credit tracking and billing UI**
- ✅ **User profile management**
- ✅ **Complete navigation flow**

## 🔧 Minimal Viable Product

**To get a working MVP, you only need:**
1. Run the database schema (Step 1)
2. Set up Clerk webhook (Step 2)

This will give you a fully functional user management system with authentication, profiles, and credit tracking!

## 📞 Need Help?

If you encounter any issues:
1. Check the browser console for errors
2. Check the terminal/server logs
3. Verify environment variables are loaded
4. Ensure database tables were created correctly

The application is production-ready once these setup steps are complete! 🎉