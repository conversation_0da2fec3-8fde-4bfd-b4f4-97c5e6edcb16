# AdBreakdown: User Flow & Feature Overview

## 1. Core Purpose
AdBreakdown is an AI-powered SaaS platform designed to provide data-driven analysis of YouTube video advertisements, generate creative assets, and offer insights for optimizing ad campaigns.

## 2. User Flows

### 2.1. Onboarding & Authentication
*   **Entry Point**: User navigates to the AdBreakdown homepage (`/`).
*   **Authentication Check**: System checks if the user is logged in (via <PERSON>).
    *   **Not Logged In**: User is prompted to Log In or Sign Up (Clerk UI components). Authentication methods include email/password and social login.
    *   **Logged In**: System checks user's subscription status (via Lemon Squeezy integration).
        *   **No Active Subscription**: User is informed of premium features and prompted to Subscribe (Lemon Squeezy checkout flow).
        *   **Active Subscription**: User gains full access.
*   **Post-Authentication**: User is redirected to the main analysis input area or dashboard.

### 2.2. YouTube Video Analysis Submission
*   **Input**: User pastes a YouTube video URL into an input field (e.g., on the dashboard).
*   **Validation**: System validates the URL (must be a public YouTube URL).
*   **Analysis Trigger**: Upon submission, the system initiates AI analysis (using Gemini API).
*   **Credit Deduction**: For authenticated users, credits are deducted for analysis.
*   **Processing**: Analysis runs in the background, and a loading/processing state is displayed.

### 2.3. Ad Analysis Report Viewing (`/ad/[id]`)
*   **Access**: Accessible after analysis completion. For public analyses, no login is required. For private analyses, user must be authenticated and be the owner.
*   **Core Content**: Displays comprehensive, data-driven insights.
*   **Sections (Tabbed Interface)**:
    *   **Overview**: Expert Marketing Analysis (10-point breakdown), Summary Cards (Sentiment, Visual Appeal, Audio Quality, Target Match), Competitor Comparison (mocked/inferred data).
    *   **Sentiment**: Emotion Distribution (AI-inferred), Sentiment Timeline.
    *   **Script**: Video Transcript & Summary, Script Analysis (Key Themes).
    *   **Visual**: Visual Analysis (Visual Appeal, Color Palette, Scene Breakdown).
    *   **Audio**: Audio Analysis (Music Mood, Voice Tone, Sound Effects, Audio Quality).
    *   **Targeting**: Targeting Recommendations (Demographics, Interests, Behaviors).
*   **LLM-Powered Feature Generation (Action Buttons)**:
    *   "Generate Marketing Copy"
    *   "Propose Social Media Posts"
    *   "Generate Marketing Scorecard"
    *   "Extract SEO Keywords"
    *   "Suggest Content Improvements"
    *   (These features require additional credit deduction for authenticated users).
*   **Generated Content Display**: Dedicated sections appear dynamically to display the output of LLM features.

### 2.4. Public Analysis (`/explore`)
*   **Purpose**: Allows users (authenticated or unauthenticated) to browse and view a curated selection of public ad analyses.
*   **Content**: Displays a grid of ad tiles with basic information (title, brand, thumbnail, sentiment).
*   **Interaction**: Users can click on an ad tile to view its full analysis report (`/ad/[id]`).
*   **No Login Required**: Full analysis content is accessible without authentication for public analyses. A login overlay may appear after scrolling to encourage sign-up for advanced features.

### 2.5. Daily Showcase (`/daily-showcase`)
*   **Purpose**: Features a single, hand-picked ad analysis daily, accessible to all users without login.
*   **Content**: A replica of the `ad/[id]` page for the featured ad, including all analysis details and generated content.
*   **Callouts**: Includes specific sections for "Today's Featured Ad Analysis" with an excerpt and key insights.
*   **No Login Required**: Fully public access to the featured analysis.

## 3. Key Features

*   **AI Sentiment/Emotion Analysis**: In-depth breakdown of emotional tone and distribution within video ads.
*   **Scene-by-Scene Breakdowns**: Analysis of visual elements and transitions.
*   **Competitor Comparison**: (Currently mocked/inferred) Benchmarking against industry averages or top performers.
*   **Targeting Recommendations**: AI-inferred demographics, interests, and behaviors for optimal ad placement.
*   **Actionable Suggestions**: AI-generated recommendations for content improvement.
*   **Creative Asset Generation**:
    *   Marketing Copy & Headlines
    *   Social Media Posts
    *   Marketing Scorecard
    *   SEO Keyword Extraction
*   **Credit System**: Users consume credits for analysis and advanced AI features.
*   **Public/Private Analysis Toggle**: Authenticated users can control the visibility of their analyses.

## 4. Third-Party Integrations & Data Handling (Inferred/Known)

*   **Authentication**: Clerk (handles user accounts, login, sign-up).
*   **Payments/Subscriptions**: Lemon Squeezy (handles checkout and subscription management).
*   **AI/ML**: Google Gemini API (core for video analysis, content generation).
*   **Video Data**: YouTube API (for fetching video metadata like title, channel, views, likes, comments, duration, tags).
*   **Backend**: Node.js (Express), Python (AI/ML services).
*   **Database**: PostgreSQL (stores analysis results, user data, etc.).
*   **Cloud Services**: Supabase (likely for database, authentication, and possibly backend functions).

## 5. Data Considerations for Legal Documents

*   **User Data Collected**:
    *   **Clerk**: Email, social login information (if used), user ID.
    *   **Lemon Squeezy**: Subscription details, payment information (handled by Lemon Squeezy, not directly by AdBreakdown).
    *   **AdBreakdown (Direct)**: YouTube video URLs submitted for analysis, analysis results (transcripts, summaries, AI-generated content, inferred brand names, sentiment data, etc.), user-generated content (if any, e.g., custom notes).
*   **Data Usage**:
    *   To provide core service (ad analysis, content generation).
    *   To personalize user experience (e.g., dashboard).
    *   For internal analytics and service improvement.
    *   For public display (with user consent via `is_public` toggle).
*   **Data Sharing**:
    *   With third-party services (Clerk, Lemon Squeezy, YouTube API, Gemini API) as necessary for service functionality.
    *   Publicly, if the user explicitly marks an analysis as `is_public`.
*   **Data Retention**: (Needs to be defined in policy) How long is user data and analysis data stored?
*   **User Rights**: Access, rectification, erasure of data.
*   **Content Ownership**: Clarify ownership of submitted video URLs and generated analysis reports.
*   **Limitations**: Acknowledge that AI analysis is inferential and may not always be 100% accurate.
*   **YouTube API Compliance**: AdBreakdown must adhere to YouTube API Services Terms of Service and Google Privacy Policy.
