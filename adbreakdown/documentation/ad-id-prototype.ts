import React, { useState, useEffect } from 'react';

// --- Re-usable UI Components (Mimicking Shadcn UI with Tailwind) ---
const FakeButton = ({ children, onClick, disabled, className, variant = "default", size = "md" }) => {
    let baseStyles = "px-4 py-2 rounded-lg font-semibold transition duration-300 ease-in-out shadow-md";
    if (size === "lg") baseStyles = "px-8 py-4 rounded-lg font-semibold text-lg transition duration-300 ease-in-out shadow-lg";
    if (size === "sm") baseStyles = "px-3 py-1 rounded-md text-sm font-medium transition duration-300 ease-in-out shadow-sm";

    let variantStyles = "bg-purple-600 hover:bg-purple-700 text-white"; // default
    if (variant === "ghost") variantStyles = "bg-transparent hover:bg-gray-200 text-gray-800";
    if (variant === "secondary") variantStyles = "bg-gray-200 hover:bg-gray-300 text-gray-800";
    if (className && className.includes('bg-')) variantStyles = ''; // Allow custom bg for specific buttons

    return (
        <button
            onClick={onClick}
            disabled={disabled}
            className={`${baseStyles} ${variantStyles} ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`}
        >
            {children}
        </button>
    );
};

const FakeCard = ({ children, className }) => (
    <div className={`bg-white rounded-xl shadow-lg border border-gray-200 ${className}`}>
        {children}
    </div>
);

const FakeCardHeader = ({ children, className }) => (
    <div className={`p-6 pb-4 ${className}`}>
        {children}
    </div>
);

const FakeCardTitle = ({ children, className }) => (
    <h3 className={`text-xl font-semibold text-gray-800 ${className}`}>
        {children}
    </h3>
);

const FakeCardDescription = ({ children, className }) => (
    <p className={`text-sm text-gray-600 ${className}`}>
        {children}
    </p>
);

const FakeCardContent = ({ children, className }) => (
    <div className={`p-6 pt-0 ${className}`}>
        {children}
    </div>
);

const FakeBadge = ({ children, variant = "default", className }) => {
    let baseStyles = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    let variantStyles = "bg-blue-100 text-blue-800"; // default
    if (variant === "secondary") variantStyles = "bg-gray-100 text-gray-800";
    if (variant === "outline") variantStyles = "border border-gray-300 text-gray-700";

    return (
        <span className={`${baseStyles} ${variantStyles} ${className}`}>
            {children}
        </span>
    );
};

const FakeProgress = ({ value, className }) => (
    <div className={`w-full bg-gray-200 rounded-full h-2 ${className}`}>
        <div
            className="bg-blue-600 h-2 rounded-full"
            style={{ width: `${value}%` }}
        ></div>
    </div>
);

const FakeTabs = ({ defaultValue, children, className }) => {
    const [activeTab, setActiveTab] = useState(defaultValue);
    return (
        <div className={className}>
            {React.Children.map(children, child => {
                if (child.type === FakeTabsList) {
                    return React.cloneElement(child, { activeTab, setActiveTab });
                }
                if (child.type === FakeTabsContent) {
                    return React.cloneElement(child, { activeTab });
                }
                return child;
            })}
        </div>
    );
};

const FakeTabsList = ({ children, className, activeTab, setActiveTab }) => (
    <div className={`flex bg-gray-100 rounded-lg p-1 mb-4 ${className}`}>
        {React.Children.map(children, child =>
            React.cloneElement(child, { activeTab, setActiveTab })
        )}
    </div>
);

const FakeTabsTrigger = ({ value, children, activeTab, setActiveTab }) => (
    <button
        onClick={() => setActiveTab(value)}
        className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors duration-200
            ${activeTab === value ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-800'}
        `}
    >
        {children}
    </button>
);

const FakeTabsContent = ({ value, children, activeTab }) => (
    <div className={`${activeTab === value ? '' : 'hidden'}`}>
        {children}
    </div>
);

// Lucide React Icons (mimicking their usage with inline SVGs for simplicity in immersive)
const Icon = ({ name, className = "" }) => {
    const icons = {
        ArrowLeft: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><path d="m12 19-7-7 7-7"/><path d="M19 12H5"/></svg>,
        Play: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><polygon points="5 3 19 12 5 21 5 3"/></svg>,
        Pause: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><rect x="6" y="4" width="4" height="16"/><rect x="14" y="4" width="4" height="16"/></svg>,
        TrendingUp: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><polyline points="22 7 13.5 15.5 8.5 10.5 2 17"/><polyline points="16 7 22 7 22 13"/></svg>,
        Users: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M22 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>,
        Target: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><circle cx="12" cy="12" r="10"/><circle cx="12" cy="12" r="6"/><circle cx="12" cy="12" r="2"/></svg>,
        Eye: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><path d="M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z"/><circle cx="12" cy="12" r="3"/></svg>,
        Volume2: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/><path d="M19.07 4.93a10 10 0 0 1 0 14.14M22.39 2.61a14 14 0 0 1 0 19.78"/></svg>,
        MessageSquare: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>,
        BarChart3: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><path d="M12 20V10"/><path d="M18 20V4"/><path d="M6 20v-4"/></svg>,
        ThumbsUp: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><path d="M7 10v12c0 1.1.9 2 2 2h3l2-2h3a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-5l-2-3H4a2 2 0 0 0-2 2v6c0 1.1.9 2 2 2h2"/></svg>,
        ThumbsDown: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><path d="M17 14V2c0-1.1-.9-2-2-2H9L7 2H4a2 2 0 0 0-2 2v6c0 1.1.9 2 2 2h5l2 3H20a2 2 0 0 0 2-2v-6c0-1.1-.9-2-2-2h-2"/></svg>,
        Meh: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}><circle cx="12" cy="12" r="10"/><line x1="8" y1="15" x2="16" y2="15"/><line x1="9" y1="9" x2="9.01" y2="9"/><line x1="15" y1="9" x2="15.01" y2="9"/></svg>,
    };
    return icons[name] || null;
};


// Main App component
const App = () => {
    // State variables for managing UI and data
    const [youtubeUrl, setYoutubeUrl] = useState('');
    const [videoInfo, setVideoInfo] = useState(''); // Raw transcript + summary text
    const [marketingAnalysis, setMarketingAnalysis] = useState(''); // Detailed 10-point analysis
    const [marketingCopy, setMarketingCopy] = useState('');
    const [socialMediaPosts, setSocialMediaPosts] = useState('');
    const [marketingScorecard, setMarketingScorecard] = useState('');
    const [seoKeywords, setSeoKeywords] = useState('');
    const [contentSuggestions, setContentSuggestions] = useState('');

    // Metadata & Detailed Analysis Data (parsed/inferred from Gemini or mock)
    const [videoMetadata, setVideoMetadata] = useState({
        title: '',
        thumbnail: 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail', // Placeholder
        duration: '0:00',
        inferredBrandName: '',
    });
    const [detailedAnalysisData, setDetailedAnalysisData] = useState({
        overallSentiment: 0,
        emotions: { joy: 0, sadness: 0, anger: 0, fear: 0, surprise: 0, disgust: 0 },
        scriptAnalysis: { transcript: '', keyThemes: [], sentimentTimeline: [] },
        visualAnalysis: { scenes: [], colorPalette: [], visualAppeal: 0 },
        audioAnalysis: { musicMood: '', voiceTone: '', audioQuality: 0, soundEffects: [] },
        targetingRecommendations: { demographics: [], interests: [], behaviors: [] },
        competitorComparison: [], // Will remain mock for now
    });


    const [loading, setLoading] = useState(false);
    const [copyLoading, setCopyLoading] = useState(false);
    const [socialLoading, setSocialLoading] = useState(false);
    const [scorecardLoading, setScorecardLoading] = useState(false);
    const [keywordsLoading, setKeywordsLoading] = useState(false);
    const [suggestionsLoading, setSuggestionsLoading] = useState(false);
    const [error, setError] = useState('');
    const [activeTab, setActiveTab] = useState('overview'); // State for controlling active tab

    const [isPlaying, setIsPlaying] = useState(false); // For mock video player
    const [currentTime, setCurrentTime] = useState(0); // For mock video player


    // Placeholder for API Key - The environment will provide this at runtime.
    // NOTE: This should be an empty string in the final deployed version in Canvas.
    // I've kept the provided key for local testing purposes if needed outside Canvas.
    const apiKey = "AIzaSyBoI6_LcEKmsmLVJXE7Q_Fe72WmwlqOL9M"; // Ensure this is empty for Canvas deployment

    /**
     * Common function to make API calls to Gemini.
     * @param {string} prompt - The text prompt for Gemini.
     * @param {string} modelName - The Gemini model to use (e.g., 'gemini-1.5-flash').
     * @param {Array} parts - Array of parts for the content (can include text and fileData/inlineData).
     * @returns {Promise<string>} - The generated text from Gemini.
     */
    const callGeminiApi = async (prompt, modelName, parts, isJsonResponse = false) => {
        let chatHistory = [];
        chatHistory.push({ role: "user", parts: parts });

        const payload = { contents: chatHistory };
        if (isJsonResponse) {
             payload.generationConfig = {
                responseMimeType: "application/json",
            };
        }

        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/${modelName}:generateContent?key=${apiKey}`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload)
        });

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(`API Error: ${errorData.error.message || response.statusText}`);
        }

        const result = await response.json();
        if (result.candidates && result.candidates.length > 0 &&
            result.candidates[0].content && result.candidates[0].content.parts &&
            result.candidates[0].content.parts.length > 0) {
            const textContent = result.candidates[0].content.parts[0].text;
            return isJsonResponse ? JSON.parse(textContent) : textContent;
        } else {
            throw new Error("Could not get valid response from Gemini.");
        }
    };

    /**
     * Handles the initial video analysis and populates all initial data points.
     */
    const handleAnalyzeVideo = async () => {
        setError('');
        setVideoInfo('');
        setMarketingAnalysis('');
        setMarketingCopy('');
        setSocialMediaPosts('');
        setMarketingScorecard('');
        setSeoKeywords('');
        setContentSuggestions('');
        setVideoMetadata({ title: '', thumbnail: 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail', duration: '0:00', inferredBrandName: '' });
        setDetailedAnalysisData({
            overallSentiment: 0, emotions: { joy: 0, sadness: 0, anger: 0, fear: 0, surprise: 0, disgust: 0 },
            scriptAnalysis: { transcript: '', keyThemes: [], sentimentTimeline: [] },
            visualAnalysis: { scenes: [], colorPalette: [], visualAppeal: 0 },
            audioAnalysis: { musicMood: '', voiceTone: '', audioQuality: 0, soundEffects: [] },
            targetingRecommendations: { demographics: [], interests: [], behaviors: [] },
            competitorComparison: [], // Mock data will be kept for this or simplified
        });
        setLoading(true);

        if (!youtubeUrl) {
            setError('Please enter a YouTube URL.');
            setLoading(false);
            return;
        }

        try {
            // Step 1: Get Video Transcript, Summary, and infer Metadata
            const videoAnalysisPrompt = `
                Analyze this YouTube video from the URL: ${youtubeUrl}.
                Provide a detailed transcript of the spoken content and a concise summary
                of what the video is about and what is happening in it.
                From the video content, infer the video's main title and any explicit or strongly implied brand name.
                Also, provide inferred key themes, a general emotional tone (e.g., positive, neutral, negative) and associated emotions (joy, sadness, anger, surprise),
                music mood, voice tone, and general targeting recommendations (demographics, interests, behaviors).
                Estimate the video's duration.

                Format your response as a JSON object with the following structure:
                {
                    "title": "Inferred Video Title",
                    "inferredBrandName": "Inferred Brand Name",
                    "duration": "MM:SS",
                    "transcript": "Full video transcript...",
                    "summary": "Concise summary...",
                    "overallSentiment": 0.0, // Score from -1.0 to 1.0
                    "emotions": {"joy": 0, "sadness": 0, "anger": 0, "fear": 0, "surprise": 0, "disgust": 0}, // Percentages
                    "keyThemes": ["theme1", "theme2"],
                    "musicMood": "Inferred Music Mood",
                    "voiceTone": "Inferred Voice Tone",
                    "targetingRecommendations": {
                        "demographics": ["demographic1"],
                        "interests": ["interest1"],
                        "behaviors": ["behavior1"]
                    }
                }
            `;
            const inferredData = await callGeminiApi(
                videoAnalysisPrompt,
                'gemini-1.5-flash',
                [
                    { text: videoAnalysisPrompt },
                    { fileData: { mimeType: "video/mp4", fileUri: youtubeUrl } }
                ],
                true // Expect JSON response
            );

            // Update metadata and initial video info
            setVideoMetadata({
                title: inferredData.title || 'Not Found',
                thumbnail: `https://i.ytimg.com/vi/${youtubeUrl.split('v=')[1]?.split('&')[0]}/hqdefault.jpg` || 'https://placehold.co/700x400/3B82F6/FFFFFF?text=Video+Thumbnail',
                duration: inferredData.duration || '0:00',
                inferredBrandName: inferredData.inferredBrandName || 'Not Found',
            });
            setVideoInfo(inferredData.transcript + "\n\n**Summary:**\n" + inferredData.summary);

            // Update detailed analysis data
            setDetailedAnalysisData(prev => ({
                ...prev,
                overallSentiment: inferredData.overallSentiment || 0,
                emotions: inferredData.emotions || { joy: 0, sadness: 0, anger: 0, fear: 0, surprise: 0, disgust: 0 },
                scriptAnalysis: {
                    ...prev.scriptAnalysis,
                    transcript: inferredData.transcript || '',
                    keyThemes: inferredData.keyThemes || []
                },
                audioAnalysis: {
                    ...prev.audioAnalysis,
                    musicMood: inferredData.musicMood || '',
                    voiceTone: inferredData.voiceTone || '',
                    audioQuality: Math.floor(Math.random() * 3) + 7, // Mock score 7-9
                },
                targetingRecommendations: inferredData.targetingRecommendations || { demographics: [], interests: [], behaviors: [] },
                // Mocked or simplified data for complex sections
                visualAnalysis: {
                    ...prev.visualAnalysis,
                    visualAppeal: Math.floor(Math.random() * 3) + 7, // Mock score 7-9
                    colorPalette: ["#000000", "#FFFFFF", "#FF6B35", "#F7931E"], // Mock
                    scenes: [ // Mock
                        { time: 0, description: "Opening scene", objects: ["initial object"] },
                        { time: 30, description: "Mid-video content", objects: ["various elements"] },
                        { time: 60, description: "Concluding visuals", objects: ["final elements"] },
                    ],
                },
                sentimentTimeline: [ // Mock sentiment timeline, LLM can't directly infer from URL
                    { time: 0, sentiment: Math.random() * 0.4 + 0.3 },
                    { time: 30, sentiment: Math.random() * 0.4 + 0.5 },
                    { time: 60, sentiment: Math.random() * 0.3 + 0.7 },
                    { time: 90, sentiment: Math.random() * 0.2 + 0.8 },
                    { time: 120, sentiment: Math.random() * 0.1 + 0.85 },
                ],
                competitorComparison: [ // Mock
                    { name: "Competitor A", sentiment: Math.random() * 0.2 + 0.6, effectiveness: Math.random() * 2 + 7 },
                    { name: "Competitor B", sentiment: Math.random() * 0.2 + 0.5, effectiveness: Math.random() * 2 + 6 },
                ]
            }));


            // Step 2: Perform Marketing Analysis (using the full string of video info for context)
            const marketingAnalysisPrompt = `
                You are a marketing expert and brand marketing expert with 20 years of experience in ad creation, creative agency lead, and video production.
                Analyze the following video content and provide an expert analysis with the following structured output.
                The video content information (transcript and summary) is:

                ---
                ${inferredData.transcript}\n\nSummary: ${inferredData.summary}
                ---

                Provide the analysis using this framework:

                1.  **General Ad Impression & Core Concept:**
                2.  **Dissection of the Target User Profile:** (Who is the ad trying to reach? Demographics, psychographics, interests.)
                3.  **Problem Addressed/Pain Point:** (What problem or need does the ad acknowledge or create? Both explicit and implicit.)
                4.  **Brand Positioning:** (How is the brand portrayed in relation to competitors or the market? Explicit and implicit.)
                5.  **Solution Offered (Explicit & Implicit):** (What does the ad offer as a solution?)
                6.  **Brand Highlight / Unique Selling Proposition (USP):** (What makes the brand or its message unique?)
                7.  **Creative Strategy & Execution:** (Analyze tone, humor, visuals, sound design, pacing, storytelling.)
                8.  **Call to Action (CTA):** (What does the ad want the viewer to do? Explicit and implicit.)
                9.  **Effectiveness & Potential Impact:** (How well is it likely to achieve its goals? Strengths.)
                10. **Areas for Improvement / Further Considerations:** (Any weaknesses or missed opportunities?)

                Ensure your analysis is professional, insightful, and reflects a deep understanding of marketing principles.
            `;
            const generatedMarketingAnalysis = await callGeminiApi(
                marketingAnalysisPrompt,
                'gemini-1.5-flash',
                [{ text: marketingAnalysisPrompt }]
            );
            setMarketingAnalysis(generatedMarketingAnalysis);

            // Mocking current time for video player
            let current = 0;
            const interval = setInterval(() => {
                current += 1;
                if (current > 150) { // Assuming max duration of 2:30 for mock
                    clearInterval(interval);
                    setIsPlaying(false);
                    setCurrentTime(0);
                } else {
                    setCurrentTime(current);
                }
            }, 1000);
            setIsPlaying(true);


        } catch (err) {
            console.error("Analysis failed:", err);
            setError(`Failed to analyze video: ${err.message}`);
        } finally {
            setLoading(false);
        }
    };

    /**
     * Generates marketing copy and headlines based on existing analysis.
     */
    const generateMarketingCopy = async () => {
        setCopyLoading(true);
        setError('');
        setMarketingCopy('');

        if (!videoInfo || !marketingAnalysis) {
            setError('Please analyze the video first to get enough context for marketing copy generation.');
            setCopyLoading(false);
            return;
        }

        try {
            const copyPrompt = `
                Given the following video information (transcript and summary) and its marketing analysis:

                ---
                Video Content Info:
                ${videoInfo}

                ---
                Marketing Analysis:
                ${marketingAnalysis}
                ---

                As a marketing expert, generate 5 compelling headlines and 3 short, punchy marketing paragraphs (1-3 sentences each)
                that capture the essence and key selling points of this video/ad for various marketing channels.
                Use a tone appropriate for the analysis provided.
            `;
            const generatedCopy = await callGeminiApi(
                copyPrompt,
                'gemini-1.5-flash',
                [{ text: copyPrompt }]
            );
            setMarketingCopy(generatedCopy);
        } catch (err) {
            console.error("Marketing copy generation failed:", err);
            setError(`Failed to generate marketing copy: ${err.message}`);
        } finally {
            setCopyLoading(false);
        }
    };

    /**
     * Proposes social media posts based on existing analysis.
     */
    const proposeSocialMediaPosts = async () => {
        setSocialLoading(true);
        setError('');
        setSocialMediaPosts('');

        if (!videoInfo || !marketingAnalysis) {
            setError('Please analyze the video first to get enough context for social media posts.');
            setSocialLoading(false);
            return;
        }

        try {
            const socialPrompt = `
                Given the following video information (transcript and summary) and its marketing analysis:

                ---
                Video Content Info:
                ${videoInfo}

                ---
                Marketing Analysis:
                ${marketingAnalysis}
                ---

                As a social media strategist, propose 3-4 distinct social media posts for this video.
                Include variations suitable for platforms like Twitter (concise), LinkedIn (professional insights), and Instagram (short caption with emojis/hashtags).
                Focus on engaging the target audience identified in the analysis.
            `;
            const generatedPosts = await callGeminiApi(
                socialPrompt,
                'gemini-1.5-flash',
                [{ text: socialPrompt }]
            );
            setSocialMediaPosts(generatedPosts);
        } catch (err) {
            console.error("Social media posts generation failed:", err);
            setError(`Failed to propose social media posts: ${err.message}`);
        } finally {
            setSocialLoading(false);
        }
    };

    /**
     * Generates a marketing scorecard based on existing analysis.
     */
    const generateMarketingScorecard = async () => {
        setScorecardLoading(true);
        setError('');
        setMarketingScorecard('');

        if (!marketingAnalysis) {
            setError('Please perform the marketing analysis first to generate a scorecard.');
            setScorecardLoading(false);
            return;
        }

        try {
            const scorecardPrompt = `
                Given the following expert marketing analysis of a video:

                ---
                ${marketingAnalysis}
                ---

                As a marketing expert, evaluate the video based on the following parameters.
                Assign a score from 1 to 5 (1 = Poor, 2 = Fair, 3 = Good, 4 = Very Good, 5 = Excellent) for each,
                and provide a brief justification for your score.

                **Marketing Parameters:**
                - Message Clarity
                - Target Audience Fit
                - Call to Action (CTA) Strength
                - Creativity & Originality
                - Emotional Appeal / Engagement
                - Brand Alignment
                - Overall Impact

                Format the output as a Markdown table:

                | Parameter             | Score (1-5) | Justification                                |
                | :-------------------- | :---------- | :------------------------------------------- |
                | Message Clarity       |             |                                              |
                | Target Audience Fit   |             |                                              |
                | ...                   |             |                                              |
                | Overall Impact        |             |                                              |
            `;
            const generatedScorecard = await callGeminiApi(
                scorecardPrompt,
                'gemini-1.5-flash',
                [{ text: scorecardPrompt }]
            );
            setMarketingScorecard(generatedScorecard);
        } catch (err) {
            console.error("Marketing scorecard generation failed:", err);
            setError(`Failed to generate marketing scorecard: ${err.message}`);
        } finally {
            setScorecardLoading(false);
        }
    };

    /**
     * Extracts SEO keywords from the video information.
     */
    const extractSeoKeywords = async () => {
        setKeywordsLoading(true);
        setError('');
        setSeoKeywords('');

        if (!videoInfo) {
            setError('Please analyze the video first to extract SEO keywords.');
            setKeywordsLoading(false);
            return;
        }

        try {
            const keywordsPrompt = `
                Given the following video transcript and summary:

                ---
                ${videoInfo}
                ---

                As an SEO specialist, identify and list 10-15 highly relevant keywords and key phrases (long-tail keywords)
                that describe the video's content and target audience. Format them as a comma-separated list,
                followed by a bulleted list explaining why each keyword is relevant.
            `;
            const generatedKeywords = await callGeminiApi(
                keywordsPrompt,
                'gemini-1.5-flash',
                [{ text: keywordsPrompt }]
            );
            setSeoKeywords(generatedKeywords);
        } catch (err) {
            console.error("SEO keyword extraction failed:", err);
            setError(`Failed to extract SEO keywords: ${err.message}`);
        } finally {
            setKeywordsLoading(false);
        }
    };

    /**
     * Suggests content improvements based on the marketing analysis.
     */
    const suggestContentImprovements = async () => {
        setSuggestionsLoading(true);
        setError('');
        setContentSuggestions('');

        if (!marketingAnalysis) {
            setError('Please perform the marketing analysis first to get content improvement suggestions.');
            setSuggestionsLoading(false);
            return;
        }

        try {
            const suggestionsPrompt = `
                Based on the following expert marketing analysis of a video:

                ---
                ${marketingAnalysis}
                ---

                As a video content strategist, provide 3-5 actionable suggestions to improve the video's content,
                structure, or overall marketing effectiveness. Be specific and practical.
            `;
            const generatedSuggestions = await callGeminiApi(
                suggestionsPrompt,
                'gemini-1.5-flash',
                [{ text: suggestionsPrompt }]
            );
            setContentSuggestions(generatedSuggestions);
        } catch (err) {
            console.error("Content improvement suggestions failed:", err);
            setError(`Failed to suggest content improvements: ${err.message}`);
        } finally {
            setSuggestionsLoading(false);
        }
    };

    const getSentimentIcon = (sentiment: number) => {
        if (sentiment >= 0.7) return <Icon name="ThumbsUp" className="h-5 w-5 text-green-600" />;
        if (sentiment >= 0.4) return <Icon name="Meh" className="h-5 w-5 text-yellow-600" />;
        return <Icon name="ThumbsDown" className="h-5 w-5 text-red-600" />;
    };

    const getSentimentColor = (sentiment: number) => {
        if (sentiment >= 0.7) return "text-green-600";
        if (sentiment >= 0.4) return "text-yellow-600";
        return "text-red-600";
    };

    const mockCompetitorComparison = [
        { name: "Competitor A", sentiment: Math.random() * 0.2 + 0.6, effectiveness: Math.random() * 2 + 7 },
        { name: "Competitor B", sentiment: Math.random() * 0.2 + 0.5, effectiveness: Math.random() * 2 + 6 },
    ];


    return (
        <div className="min-h-screen bg-gray-50 text-gray-800 font-inter">
            <style>
                {`
                @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
                body { font-family: 'Inter', sans-serif; }
                .prose-invert br { /* Fix for markdown br rendering in prose */
                    content: "";
                    display: block;
                    margin-bottom: 0.5rem;
                }
                .prose-invert table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 1em;
                }
                .prose-invert th, .prose-invert td {
                    border: 1px solid #4a5568; /* gray-700 */
                    padding: 8px;
                    text-align: left;
                }
                .prose-invert th {
                    background-color: #2D3748; /* gray-800 */
                    color: #CBD5E0; /* gray-300 */
                }
                .prose-invert tr:nth-child(even) {
                    background-color: #2D3748; /* gray-800 */
                }
                .prose-invert tr:nth-child(odd) {
                    background-color: #1A202C; /* gray-900 */
                }
                `}
            </style>

            {/* Header */}
            <header className="border-b bg-white">
                <div className="container mx-auto px-4 py-4">
                    <div className="flex items-center gap-4">
                        <FakeButton variant="ghost" size="sm">
                            <Icon name="ArrowLeft" className="h-4 w-4 mr-2" />
                            Back to Dashboard
                        </FakeButton>
                        <div className="flex items-center gap-2">
                            <Icon name="BarChart3" className="h-6 w-6 text-blue-600" />
                            <span className="font-semibold">AdBreakdown Analysis</span>
                        </div>
                    </div>
                </div>
            </header>

            <main className="container mx-auto px-4 py-8 max-w-4xl">
                {/* Input Section */}
                <div className="mb-8">
                    <div className="flex flex-col md:flex-row gap-4 mb-4">
                        <input
                            type="text"
                            className="flex-grow p-4 rounded-lg bg-gray-100 border border-gray-300 text-gray-800 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500 transition duration-300 ease-in-out"
                            placeholder="Enter YouTube Video URL (e.g., https://www.youtube.com/watch?v=M6oAX9GLeSQ)"
                            value={youtubeUrl}
                            onChange={(e) => setYoutubeUrl(e.target.value)}
                        />
                        <FakeButton
                            onClick={handleAnalyzeVideo}
                            className={`bg-purple-600 hover:bg-purple-700 text-white
                                ${loading ? 'bg-gray-600 cursor-not-allowed' : ''}
                            `}
                            disabled={loading}
                            size="lg"
                        >
                            {loading ? 'Analyzing...' : 'Analyze Video'}
                        </FakeButton>
                    </div>

                    {loading && (
                        <div className="text-center text-purple-600 text-lg my-4">
                            <svg className="animate-spin h-8 w-8 text-purple-600 mx-auto mb-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Please wait, analyzing video and generating marketing insights...
                        </div>
                    )}
                    {error && (
                        <div className="bg-red-100 border border-red-400 text-red-700 p-4 rounded-lg text-center my-4">
                            {error}
                        </div>
                    )}
                </div>

                {/* Video Player Section */}
                {videoMetadata.title && (
                    <FakeCard className="mb-8">
                        <FakeCardContent className="p-0">
                            <div className="relative bg-black rounded-t-lg overflow-hidden">
                                <img
                                    src={videoMetadata.thumbnail}
                                    alt={videoMetadata.title}
                                    className="w-full aspect-video object-cover"
                                />
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <FakeButton size="lg" className="rounded-full w-16 h-16 bg-white bg-opacity-80 text-gray-900 hover:bg-opacity-100" onClick={() => setIsPlaying(!isPlaying)}>
                                        {isPlaying ? <Icon name="Pause" className="h-6 w-6" /> : <Icon name="Play" className="h-6 w-6" />}
                                    </FakeButton>
                                </div>
                                <div className="absolute bottom-4 left-4 right-4">
                                    <FakeProgress value={(currentTime / 150) * 100} className="mb-2 bg-gray-500" />
                                    <div className="flex justify-between text-white text-sm">
                                        <span>
                                            {Math.floor(currentTime / 60)}:{(currentTime % 60).toString().padStart(2, "0")}
                                        </span>
                                        <span>{videoMetadata.duration}</span>
                                    </div>
                                </div>
                            </div>
                            <div className="p-6">
                                <h1 className="text-2xl font-bold mb-2">{videoMetadata.title}</h1>
                                <div className="flex items-center gap-4">
                                    <div className="flex items-center gap-2">
                                        {getSentimentIcon(detailedAnalysisData.overallSentiment)}
                                        <span className={`font-semibold ${getSentimentColor(detailedAnalysisData.overallSentiment)}`}>
                                            Overall Sentiment: {(detailedAnalysisData.overallSentiment * 100).toFixed(0)}%
                                        </span>
                                    </div>
                                    <FakeBadge variant="secondary">Duration: {videoMetadata.duration}</FakeBadge>
                                </div>
                            </div>
                        </FakeCardContent>
                    </FakeCard>
                )}


                {/* Metadata Section */}
                {(videoMetadata.title || videoMetadata.inferredBrandName) && (
                    <FakeCard className="mb-8">
                        <FakeCardHeader>
                            <FakeCardTitle>Video Metadata</FakeCardTitle>
                        </FakeCardHeader>
                        <FakeCardContent>
                            <p><strong>Inferred Brand Name:</strong> {videoMetadata.inferredBrandName}</p>
                            <p className="text-sm text-gray-600 mt-2">
                                <em>Note: Date Published and exact publishing handle are typically retrieved via dedicated YouTube APIs for precise data,
                                which is beyond what direct video content analysis by Gemini can infer.</em>
                            </p>
                        </FakeCardContent>
                    </FakeCard>
                )}

                {/* Analysis Tabs */}
                {marketingAnalysis && (
                    <FakeTabs defaultValue="overview" className="space-y-6" activeTab={activeTab} setActiveTab={setActiveTab}>
                        <FakeTabsList className="grid w-full grid-cols-6">
                            <FakeTabsTrigger value="overview">Overview</FakeTabsTrigger>
                            <FakeTabsTrigger value="sentiment">Sentiment</FakeTabsTrigger>
                            <FakeTabsTrigger value="script">Script</FakeTabsTrigger>
                            <FakeTabsTrigger value="visual">Visual</FakeTabsTrigger>
                            <FakeTabsTrigger value="audio">Audio</FakeTabsTrigger>
                            <FakeTabsTrigger value="targeting">Targeting</FakeTabsTrigger>
                        </FakeTabsList>

                        <FakeTabsContent value="overview">
                            {/* Expert Marketing Analysis moved here */}
                            {marketingAnalysis && (
                                <FakeCard className="mb-6">
                                    <FakeCardHeader>
                                        <FakeCardTitle>Expert Marketing Analysis</FakeCardTitle>
                                    </FakeCardHeader>
                                    <FakeCardContent>
                                        <div className="prose text-gray-700">
                                            <div dangerouslySetInnerHTML={{ __html: marketingAnalysis.replace(/\n/g, '<br/>') }} />
                                        </div>
                                    </FakeCardContent>
                                </FakeCard>
                            )}
                            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <FakeCard>
                                    <FakeCardHeader className="pb-3">
                                        <FakeCardTitle className="text-sm font-medium flex items-center gap-2">
                                            <Icon name="TrendingUp" className="h-4 w-4" />
                                            Sentiment Score
                                        </FakeCardTitle>
                                    </FakeCardHeader>
                                    <FakeCardContent>
                                        <div className="text-2xl font-bold text-green-600">
                                            {(detailedAnalysisData.overallSentiment * 100).toFixed(0)}%
                                        </div>
                                        <p className="text-sm text-gray-600">Highly positive</p>
                                    </FakeCardContent>
                                </FakeCard>

                                <FakeCard>
                                    <FakeCardHeader className="pb-3">
                                        <FakeCardTitle className="text-sm font-medium flex items-center gap-2">
                                            <Icon name="Eye" className="h-4 w-4" />
                                            Visual Appeal
                                        </FakeCardTitle>
                                    </FakeCardHeader>
                                    <FakeCardContent>
                                        <div className="text-2xl font-bold text-blue-600">{detailedAnalysisData.visualAnalysis.visualAppeal}/10</div>
                                        <p className="text-sm text-gray-600">Excellent quality (inferred)</p>
                                    </FakeCardContent>
                                </FakeCard>

                                <FakeCard>
                                    <FakeCardHeader className="pb-3">
                                        <FakeCardTitle className="text-sm font-medium flex items-center gap-2">
                                            <Icon name="Volume2" className="h-4 w-4" />
                                            Audio Quality
                                        </FakeCardTitle>
                                    </FakeCardHeader>
                                    <FakeCardContent>
                                        <div className="text-2xl font-bold text-purple-600">{detailedAnalysisData.audioAnalysis.audioQuality}/10</div>
                                        <p className="text-sm text-gray-600">Professional grade (inferred)</p>
                                    </FakeCardContent>
                                </FakeCard>

                                <FakeCard>
                                    <FakeCardHeader className="pb-3">
                                        <FakeCardTitle className="text-sm font-medium flex items-center gap-2">
                                            <Icon name="Target" className="h-4 w-4" />
                                            Target Match
                                        </FakeCardTitle>
                                    </FakeCardHeader>
                                    <FakeCardContent>
                                        <div className="text-2xl font-bold text-orange-600">92%</div> {/* Mock value */}
                                        <p className="text-sm text-gray-600">Well targeted (inferred)</p>
                                    </FakeCardContent>
                                </FakeCard>
                            </div>

                            {/* Competitor Comparison (Mock Data) */}
                            <FakeCard>
                                <FakeCardHeader>
                                    <FakeCardTitle className="flex items-center gap-2">
                                        <Icon name="Users" className="h-5 w-5" />
                                        Competitor Comparison
                                    </FakeCardTitle>
                                    <FakeCardDescription>How your ad performs against similar competitor ads (Mock Data)</FakeCardDescription>
                                </FakeCardHeader>
                                <FakeCardContent>
                                    <div className="space-y-4">
                                        {mockCompetitorComparison.map((competitor, index) => (
                                            <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                                                <div className="flex items-center gap-3">
                                                    <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center text-gray-700 font-bold">
                                                        {competitor.name.charAt(0)}
                                                    </div>
                                                    <div>
                                                        <div className="font-medium">{competitor.name}</div>
                                                        <div className="text-sm text-gray-600">Competitor</div>
                                                    </div>
                                                </div>
                                                <div className="flex items-center gap-4">
                                                    <div className="text-center">
                                                        <div className="text-sm font-medium">Sentiment</div>
                                                        <div className={getSentimentColor(competitor.sentiment)}>
                                                            {(competitor.sentiment * 100).toFixed(0)}%
                                                        </div>
                                                    </div>
                                                    <div className="text-center">
                                                        <div className="text-sm font-medium">Effectiveness</div>
                                                        <div className="text-blue-600">{competitor.effectiveness.toFixed(1)}/10</div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </FakeCardContent>
                            </FakeCard>
                        </FakeTabsContent>

                        <FakeTabsContent value="sentiment">
                            <FakeCard>
                                <FakeCardHeader>
                                    <FakeCardTitle>Emotion Distribution (Inferred)</FakeCardTitle>
                                    <FakeCardDescription>Breakdown of emotions detected throughout the ad (inferred by AI)</FakeCardDescription>
                                </FakeCardHeader>
                                <FakeCardContent>
                                    <div className="space-y-4">
                                        {Object.entries(detailedAnalysisData.emotions).map(([emotion, percentage]) => (
                                            <div key={emotion} className="flex items-center gap-4">
                                                <div className="w-20 text-sm font-medium capitalize">{emotion}</div>
                                                <div className="flex-1">
                                                    <FakeProgress value={percentage} className="h-2" />
                                                </div>
                                                <div className="w-12 text-sm text-gray-600">{percentage}%</div>
                                            </div>
                                        ))}
                                    </div>
                                </FakeCardContent>
                            </FakeCard>

                            <FakeCard>
                                <FakeCardHeader>
                                    <FakeCardTitle>Sentiment Timeline (Mock)</FakeCardTitle>
                                    <FakeCardDescription>How sentiment changes throughout the ad duration (mocked for visualization)</FakeCardDescription>
                                </FakeCardHeader>
                                <FakeCardContent>
                                    <div className="h-64 flex items-end gap-2 bg-gray-100 rounded-lg p-2">
                                        {detailedAnalysisData.sentimentTimeline.map((point, index) => (
                                            <div key={index} className="flex-1 flex flex-col items-center">
                                                <div className="w-full bg-blue-500 rounded-t" style={{ height: `${point.sentiment * 200}px` }} />
                                                <div className="text-xs text-gray-600 mt-1">
                                                    {Math.floor(point.time / 60)}:{(point.time % 60).toString().padStart(2, "0")}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </FakeCardContent>
                            </FakeCard>
                        </FakeTabsContent>

                        <FakeTabsContent value="script">
                             {/* Video Transcript & Summary moved here */}
                            {videoInfo && (
                                <FakeCard className="mb-6">
                                    <FakeCardHeader>
                                        <FakeCardTitle>Video Transcript & Summary</FakeCardTitle>
                                    </FakeCardHeader>
                                    <FakeCardContent>
                                        <div className="prose text-gray-700">
                                            <p className="whitespace-pre-wrap">{videoInfo}</p>
                                        </div>
                                    </FakeCardContent>
                                </FakeCard>
                            )}
                            <FakeCard>
                                <FakeCardHeader>
                                    <FakeCardTitle className="flex items-center gap-2">
                                        <Icon name="MessageSquare" className="h-5 w-5" />
                                        Script Analysis
                                    </FakeCardTitle>
                                </FakeCardHeader>
                                <FakeCardContent className="space-y-6">
                                    <div>
                                        <h4 className="font-medium mb-2">Transcript</h4>
                                        <p className="text-gray-700 leading-relaxed bg-gray-50 p-4 rounded-lg whitespace-pre-wrap">
                                            {detailedAnalysisData.scriptAnalysis.transcript}
                                        </p>
                                    </div>

                                    <div>
                                        <h4 className="font-medium mb-3">Key Themes (Inferred)</h4>
                                        <div className="flex flex-wrap gap-2">
                                            {detailedAnalysisData.scriptAnalysis.keyThemes.map((theme, index) => (
                                                <FakeBadge key={index} variant="secondary">
                                                    {theme}
                                                </FakeBadge>
                                            ))}
                                        </div>
                                    </div>
                                </FakeCardContent>
                            </FakeCard>
                        </FakeTabsContent>

                        <FakeTabsContent value="visual">
                            <FakeCard>
                                <FakeCardHeader>
                                    <FakeCardTitle className="flex items-center gap-2">
                                        <Icon name="Eye" className="h-5 w-5" />
                                        Visual Analysis
                                    </FakeCardTitle>
                                </FakeCardHeader>
                                <FakeCardContent className="space-y-6">
                                    <div>
                                        <h4 className="font-medium mb-3">Scene Breakdown (Mock)</h4>
                                        <div className="space-y-3">
                                            {detailedAnalysisData.visualAnalysis.scenes.map((scene, index) => (
                                                <div key={index} className="border rounded-lg p-4">
                                                    <div className="flex justify-between items-start mb-2">
                                                        <h5 className="font-medium">Scene {index + 1}</h5>
                                                        <FakeBadge variant="outline">
                                                            {Math.floor(scene.time / 60)}:{(scene.time % 60).toString().padStart(2, "0")}
                                                        </FakeBadge>
                                                    </div>
                                                    <p className="text-gray-700 mb-2">{scene.description}</p>
                                                    <div className="flex flex-wrap gap-1">
                                                        {scene.objects.map((object, objIndex) => (
                                                            <FakeBadge key={objIndex} variant="secondary" className="text-xs">
                                                                {object}
                                                            </FakeBadge>
                                                        ))}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>

                                    <div>
                                        <h4 className="font-medium mb-3">Color Palette (Mock)</h4>
                                        <div className="flex gap-2">
                                            {detailedAnalysisData.visualAnalysis.colorPalette.map((color, index) => (
                                                <div key={index} className="flex flex-col items-center gap-1">
                                                    <div className="w-12 h-12 rounded-lg border" style={{ backgroundColor: color }} />
                                                    <span className="text-xs text-gray-600">{color}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </FakeCardContent>
                            </FakeCard>
                        </FakeTabsContent>

                        <FakeTabsContent value="audio">
                            <FakeCard>
                                <FakeCardHeader>
                                    <FakeCardTitle className="flex items-center gap-2">
                                        <Icon name="Volume2" className="h-5 w-5" />
                                        Audio Analysis
                                    </FakeCardTitle>
                                </FakeCardHeader>
                                <FakeCardContent className="space-y-6">
                                    <div className="grid md:grid-cols-2 gap-6">
                                        <div>
                                            <h4 className="font-medium mb-2">Music Mood (Inferred)</h4>
                                            <FakeBadge className="bg-purple-100 text-purple-800">{detailedAnalysisData.audioAnalysis.musicMood || 'Not Inferred'}</FakeBadge>
                                        </div>
                                        <div>
                                            <h4 className="font-medium mb-2">Voice Tone (Inferred)</h4>
                                            <FakeBadge className="bg-blue-100 text-blue-800">{detailedAnalysisData.audioAnalysis.voiceTone || 'Not Inferred'}</FakeBadge>
                                        </div>
                                    </div>

                                    <div>
                                        <h4 className="font-medium mb-3">Sound Effects (Mock)</h4>
                                        <div className="flex flex-wrap gap-2">
                                            {detailedAnalysisData.audioAnalysis.soundEffects.map((effect, index) => (
                                                <FakeBadge key={index} variant="outline">
                                                    {effect}
                                                </FakeBadge>
                                            ))}
                                        </div>
                                    </div>

                                    <div>
                                        <h4 className="font-medium mb-2">Audio Quality Score (Inferred)</h4>
                                        <div className="flex items-center gap-3">
                                            <FakeProgress value={detailedAnalysisData.audioAnalysis.audioQuality * 10} className="flex-1" />
                                            <span className="font-bold text-lg">{detailedAnalysisData.audioAnalysis.audioQuality}/10</span>
                                        </div>
                                    </div>
                                </FakeCardContent>
                            </FakeCard>
                        </FakeTabsContent>

                        <FakeTabsContent value="targeting">
                            <FakeCard>
                                <FakeCardHeader>
                                    <FakeCardTitle className="flex items-center gap-2">
                                        <Icon name="Target" className="h-5 w-5" />
                                        Targeting Recommendations (Inferred)
                                    </FakeCardTitle>
                                    <FakeCardDescription>Optimize your audience targeting based on ad content analysis</FakeCardDescription>
                                </FakeCardHeader>
                                <FakeCardContent className="space-y-6">
                                    <div>
                                        <h4 className="font-medium mb-3">Demographics</h4>
                                        <div className="flex flex-wrap gap-2">
                                            {detailedAnalysisData.targetingRecommendations.demographics.map((demo, index) => (
                                                <FakeBadge key={index} className="bg-green-100 text-green-800">
                                                    {demo}
                                                </FakeBadge>
                                            ))}
                                        </div>
                                    </div>

                                    <div>
                                        <h4 className="font-medium mb-3">Interests</h4>
                                        <div className="flex flex-wrap gap-2">
                                            {detailedAnalysisData.targetingRecommendations.interests.map((interest, index) => (
                                                <FakeBadge key={index} className="bg-blue-100 text-blue-800">
                                                    {interest}
                                                </FakeBadge>
                                            ))}
                                        </div>
                                    </div>

                                    <div>
                                        <h4 className="font-medium mb-3">Behaviors</h4>
                                        <div className="flex flex-wrap gap-2">
                                            {detailedAnalysisData.targetingRecommendations.behaviors.map((behavior, index) => (
                                                <FakeBadge key={index} className="bg-purple-100 text-purple-800">
                                                    {behavior}
                                                </FakeBadge>
                                            ))}
                                        </div>
                                    </div>

                                    <div className="bg-blue-50 p-4 rounded-lg">
                                        <h4 className="font-medium mb-2 text-blue-900">💡 Key Recommendations</h4>
                                        <ul className="text-sm text-blue-800 space-y-1">
                                            <li>• Focus on inferred demographics and interests</li>
                                            <li>• Tailor messaging to identified behaviors</li>
                                            <li>• Leverage insights for precise ad targeting</li>
                                        </ul>
                                    </div>
                                </FakeCardContent>
                            </FakeCard>
                        </FakeTabsContent>
                    </FakeTabs>
                )}


                {/* Action Buttons for new LLM features */}
                {marketingAnalysis && (
                    <>
                        <div className="flex flex-col md:flex-row gap-4 mt-6">
                            <FakeButton
                                onClick={generateMarketingCopy}
                                className={`flex-grow bg-green-600 hover:bg-green-700 text-white
                                    ${copyLoading ? 'bg-gray-600 cursor-not-allowed' : ''}
                                `}
                                disabled={copyLoading || loading}
                            >
                                {copyLoading ? 'Generating...' : 'Generate Marketing Copy ✨'}
                            </FakeButton>
                            <FakeButton
                                onClick={proposeSocialMediaPosts}
                                className={`flex-grow bg-blue-600 hover:bg-blue-700 text-white
                                    ${socialLoading ? 'bg-gray-600 cursor-not-allowed' : ''}
                                `}
                                disabled={socialLoading || loading}
                            >
                                {socialLoading ? 'Proposing...' : 'Propose Social Media Posts ✨'}
                            </FakeButton>
                        </div>
                        <div className="flex flex-col md:flex-row gap-4 mt-4">
                             <FakeButton
                                onClick={generateMarketingScorecard}
                                className={`flex-grow bg-orange-600 hover:bg-orange-700 text-white
                                    ${scorecardLoading ? 'bg-gray-600 cursor-not-allowed' : ''}
                                `}
                                disabled={scorecardLoading || loading}
                            >
                                {scorecardLoading ? 'Scoring...' : 'Generate Marketing Scorecard 📊'}
                            </FakeButton>
                            <FakeButton
                                onClick={extractSeoKeywords}
                                className={`flex-grow bg-teal-600 hover:bg-teal-700 text-white
                                    ${keywordsLoading ? 'bg-gray-600 cursor-not-allowed' : ''}
                                `}
                                disabled={keywordsLoading || loading}
                            >
                                {keywordsLoading ? 'Extracting...' : 'Extract SEO Keywords 🔍'}
                            </FakeButton>
                            <FakeButton
                                onClick={suggestContentImprovements}
                                className={`flex-grow bg-pink-600 hover:bg-pink-700 text-white
                                    ${suggestionsLoading ? 'bg-gray-600 cursor-not-allowed' : ''}
                                `}
                                disabled={suggestionsLoading || loading}
                            >
                                {suggestionsLoading ? 'Suggesting...' : 'Suggest Content Improvements 💡'}
                            </FakeButton>
                        </div>
                    </>
                )}

                {/* Results Sections (Other features, outside tabs) */}
                <div className="mt-8 space-y-8">
                    {marketingScorecard && (
                        <div className="bg-white rounded-xl p-6 shadow-md border border-gray-200">
                            <h2 className="text-2xl font-bold text-gray-800 mb-4">Marketing Scorecard 📊</h2>
                            <div className="prose text-gray-700">
                                <div dangerouslySetInnerHTML={{ __html: marketingScorecard.replace(/\n/g, '<br/>') }} />
                            </div>
                        </div>
                    )}

                    {seoKeywords && (
                        <div className="bg-white rounded-xl p-6 shadow-md border border-gray-200">
                            <h2 className="text-2xl font-bold text-gray-800 mb-4">Extracted SEO Keywords 🔍</h2>
                            <div className="prose text-gray-700">
                                <div dangerouslySetInnerHTML={{ __html: seoKeywords.replace(/\n/g, '<br/>') }} />
                            </div>
                        </div>
                    )}

                    {contentSuggestions && (
                        <div className="bg-white rounded-xl p-6 shadow-md border border-gray-200">
                            <h2 className="text-2xl font-bold text-gray-800 mb-4">Content Improvement Suggestions 💡</h2>
                            <div className="prose text-gray-700">
                                <div dangerouslySetInnerHTML={{ __html: contentSuggestions.replace(/\n/g, '<br/>') }} />
                            </div>
                        </div>
                    )}

                    {marketingCopy && (
                        <div className="bg-white rounded-xl p-6 shadow-md border border-gray-200">
                            <h2 className="text-2xl font-bold text-gray-800 mb-4">Generated Marketing Copy & Headlines ✨</h2>
                            <div className="prose text-gray-700">
                                <div dangerouslySetInnerHTML={{ __html: marketingCopy.replace(/\n/g, '<br/>') }} />
                            </div>
                        </div>
                    )}

                    {socialMediaPosts && (
                        <div className="bg-white rounded-xl p-6 shadow-md border border-gray-200">
                            <h2 className="text-2xl font-bold text-gray-800 mb-4">Proposed Social Media Posts ✨</h2>
                            <div className="prose text-gray-700">
                                <div dangerouslySetInnerHTML={{ __html: socialMediaPosts.replace(/\n/g, '<br/>') }} />
                            </div>
                        </div>
                    )}
                </div>
            </main>
        </div>
    );
};

export default App;
