

const { createClient } = require('@supabase/supabase-js');

// Supabase credentials from your environment variables or direct input
const SUPABASE_URL = 'https://elossghirdivbobfycob.supabase.co';
const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVsb3NzZ2hpcmRpdmJvYmZ5Y29iIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTkzNTAsImV4cCI6MjA2NjQ5NTM1MH0.EuIuMDBRBPx9qIVkL8qodDeS9mkb-iFgA9PphuQH9pI';

const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY);

// Function to strip JSON tags from a string
function stripJsonTags(jsonString) {
    if (typeof jsonString !== 'string') {
        return jsonString;
    }
    let cleanString = jsonString.replace(/^```json\s*/, '').replace(/```\s*$/, '').trim();
    // Handle cases where there might be extra characters before/after the JSON block
    const jsonMatch = cleanString.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
        cleanString = jsonMatch[0];
    }
    return cleanString;
}

// Function to transform old JSON structure to new JSON structure
function transformOldToNewJson(oldJson) {
    const newJson = {
        metadata: {},
        executive_briefing: {
            scorecard: {}
        },
        core_analysis: {},
        diagnosis: {},
        strategic_deep_dive: {
            brand_and_market_context: {},
            creative_and_cultural_context: {},
            brand_strategy: {}
        },
        internal_signals: {}
    };

    // --- Metadata ---
    if (oldJson.lightning_round) {
        newJson.metadata.ad_title = oldJson.lightning_round.ad_title || null;
        newJson.metadata.brand = oldJson.lightning_round.brand || null;
        newJson.metadata.product_category = oldJson.lightning_round.product_category || null;
        newJson.metadata.parent_entity = oldJson.lightning_round.parent_entity || null;
        newJson.metadata.campaign_category = oldJson.lightning_round.campaign_category || null;
        newJson.metadata.runtime = oldJson.lightning_round.runtime || null;
        newJson.metadata.celebrity = oldJson.lightning_round.celebrity || null;
    }
    newJson.metadata.geography = null; // No direct mapping in old JSON

    // --- Executive Briefing ---
    newJson.executive_briefing.the_one_thing_that_matters = oldJson.lightning_round?.central_insight || null;
    newJson.executive_briefing.gut_reaction = oldJson.gut_reaction || null;

    // Scorecard - This is a significant structural change.
    // We will map overall_impact_score and set others to default/null.
    if (oldJson.scorecard) {
        newJson.executive_briefing.scorecard.brand_integration = { score: oldJson.scorecard.brand_integration || 0, justification: '' };
        newJson.executive_briefing.scorecard.emotional_response = { score: oldJson.scorecard.emotional_resonance || 0, justification: '' }; // Mapping emotional_resonance
        newJson.executive_briefing.scorecard.hook_power = { score: 0, justification: '' }; // New field
        newJson.executive_briefing.scorecard.brand_differentiation = { score: 0, justification: '' }; // New field
        newJson.executive_briefing.scorecard.brand_consideration = { score: 0, justification: '' }; // New field
        newJson.executive_briefing.scorecard.purchase_intent = { score: oldJson.scorecard.cta_clarity || 0, justification: '' }; // Mapping cta_clarity to purchase_intent
        newJson.executive_briefing.scorecard.overall_impact_score = { score: oldJson.scorecard.overall_impact_score || 0, justification: '' };
    } else {
        // Set default empty scorecard if old one doesn't exist
        newJson.executive_briefing.scorecard = {
            brand_integration: { score: 0, justification: '' },
            emotional_response: { score: 0, justification: '' },
            hook_power: { score: 0, justification: '' },
            brand_differentiation: { score: 0, justification: '' },
            brand_consideration: { score: 0, justification: '' },
            purchase_intent: { score: 0, justification: '' },
            overall_impact_score: { score: 0, justification: '' }
        };
    }


    // --- Core Analysis ---
    // Combine relevant parts from old business_integration and strategic_deep_dive
    let coreAnalysisEssay = '';
    if (oldJson.business_integration) {
        coreAnalysisEssay += `Business Integration:\n`;
        for (const key in oldJson.business_integration) {
            if (typeof oldJson.business_integration[key] === 'string') {
                coreAnalysisEssay += `- ${key.replace(/_/g, ' ')}: ${oldJson.business_integration[key]}\n`;
            }
        }
        coreAnalysisEssay += '\n';
    }
    if (oldJson.strategic_deep_dive) {
        coreAnalysisEssay += `Strategic Deep Dive:\n`;
        if (oldJson.strategic_deep_dive.target_audience_read) {
            coreAnalysisEssay += `  Target Audience:\n`;
            for (const key in oldJson.strategic_deep_dive.target_audience_read) {
                if (typeof oldJson.strategic_deep_dive.target_audience_read[key] === 'string') {
                    coreAnalysisEssay += `  - ${key.replace(/_/g, ' ')}: ${oldJson.strategic_deep_dive.target_audience_read[key]}\n`;
                }
            }
        }
        if (oldJson.strategic_deep_dive.competitive_context) {
            coreAnalysisEssay += `  Competitive Context:\n`;
            for (const key in oldJson.strategic_deep_dive.competitive_context) {
                if (typeof oldJson.strategic_deep_dive.competitive_context[key] === 'string') {
                    coreAnalysisEssay += `  - ${key.replace(/_/g, ' ')}: ${oldJson.strategic_deep_dive.competitive_context[key]}\n`;
                }
            }
        }
        if (oldJson.strategic_deep_dive.cultural_relevance) {
            coreAnalysisEssay += `  Cultural Relevance:\n`;
            for (const key in oldJson.strategic_deep_dive.cultural_relevance) {
                if (typeof oldJson.strategic_deep_dive.cultural_relevance[key] === 'string') {
                    coreAnalysisEssay += `  - ${key.replace(/_/g, ' ')}: ${oldJson.strategic_deep_dive.cultural_relevance[key]}\n`;
                }
            }
        }
        coreAnalysisEssay += '\n';
    }
    newJson.core_analysis.essay = coreAnalysisEssay.trim();


    // --- Diagnosis ---
    let clarityPitfall = null;
    let clarityImprovement = null;

    if (oldJson.pitfalls_identified?.messaging_pitfalls) {
        clarityPitfall = oldJson.pitfalls_identified.messaging_pitfalls;
    }
    if (oldJson.veteran_verdict?.what_missed_mark) {
        clarityImprovement = oldJson.veteran_verdict.what_missed_mark;
    }
    newJson.diagnosis.clarity_of_whats_next = {
        pitfall: clarityPitfall,
        improvement_signal: clarityImprovement
    };
    newJson.diagnosis.emotional_connection = { pitfall: null, improvement_signal: null };
    newJson.diagnosis.strategic_sense = { pitfall: null, improvement_signal: null };


    // --- Strategic Deep Dive (New Structure) ---
    if (oldJson.strategic_deep_dive) {
        if (oldJson.strategic_deep_dive.target_audience_read) {
            newJson.strategic_deep_dive.brand_and_market_context.target_audience_evidence =
                Object.values(oldJson.strategic_deep_dive.target_audience_read).filter(val => typeof val === 'string').join(' ');
        }
        if (oldJson.strategic_deep_dive.competitive_context) {
            newJson.strategic_deep_dive.brand_and_market_context.competitive_landscape_evidence =
                Object.values(oldJson.strategic_deep_dive.competitive_context).filter(val => typeof val === 'string').join(' ');
        }
        if (oldJson.strategic_deep_dive.cultural_relevance) {
            newJson.strategic_deep_dive.creative_and_cultural_context.cultural_hook =
                Object.values(oldJson.strategic_deep_dive.cultural_relevance).filter(val => typeof val === 'string').join(' ');
        }
        newJson.strategic_deep_dive.creative_and_cultural_context.creative_game_plan = null;
        newJson.strategic_deep_dive.brand_strategy.brand_position = null;
        newJson.strategic_deep_dive.brand_strategy.brand_archetype = null;
        newJson.strategic_deep_dive.brand_strategy.hypothetical_brief = null;
    }


    // --- Internal Signals ---
    if (oldJson.system_signals) {
        if (oldJson.system_signals.pattern_recognition) {
            newJson.internal_signals.pattern_recognition =
                (oldJson.system_signals.pattern_recognition.effectiveness_pattern || '') + ' ' +
                (oldJson.system_signals.pattern_recognition.similar_ads || '');
            newJson.internal_signals.pattern_recognition = newJson.internal_signals.pattern_recognition.trim();
        }
        if (oldJson.system_signals.prediction_factors) {
            newJson.internal_signals.prediction_factors =
                (oldJson.system_signals.prediction_factors.overall_score_explanation || '') + ' ' +
                (oldJson.system_signals.prediction_factors.performance_predictors || '');
            newJson.internal_signals.prediction_factors = newJson.internal_signals.prediction_factors.trim();
        }
    }

    return newJson;
}

async function migrateAnalyses() {
    console.log('Starting migration...');
    let offset = 0;
    const limit = 100; // Process 100 records at a time

    while (true) {
        console.log(`Fetching analyses from offset: ${offset}`);
        const { data: analyses, error } = await supabase
            .from('ad_analyses')
            .select('id, marketing_analysis')
            .is('marketing_analysis_new', null) // Only process records where the new column is null
            .order('id', { ascending: true })
            .range(offset, offset + limit - 1);

        if (error) {
            console.error('Error fetching analyses:', error);
            break;
        }

        if (!analyses || analyses.length === 0) {
            console.log('No more analyses to migrate.');
            break;
        }

        console.log(`Processing ${analyses.length} analyses...`);
        for (const analysis of analyses) {
            try {
                const cleanedJsonString = stripJsonTags(analysis.marketing_analysis);
                const oldJson = JSON.parse(cleanedJsonString);
                const newJson = transformOldToNewJson(oldJson);

                const { error: updateError } = await supabase
                    .from('ad_analyses')
                    .update({ marketing_analysis_new: newJson })
                    .eq('id', analysis.id);

                if (updateError) {
                    console.error(`Error updating analysis ${analysis.id}:`, updateError);
                } else {
                    console.log(`Successfully migrated analysis ${analysis.id}`);
                }
            } catch (e) {
                console.error(`Failed to process analysis ${analysis.id}:`, e);
            }
        }

        offset += limit;
        if (analyses.length < limit) {
            console.log('Finished processing all available analyses.');
            break;
        }
    }
    console.log('Migration complete.');
}

migrateAnalyses();
