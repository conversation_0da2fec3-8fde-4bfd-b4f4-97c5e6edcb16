#!/usr/bin/env node

/**
 * Cache Invalidation Script for Production
 * 
 * This script invalidates all ISR caches and forces revalidation
 * Use when you need to push urgent updates to production
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 Starting cache invalidation...');

// 1. Update cache headers with new timestamp
const nextConfig = path.join(__dirname, 'next.config.js');
const timestamp = Date.now();

console.log(`📅 Cache timestamp: ${timestamp}`);

// 2. Create a cache-busting file that changes on each deployment
const cacheBuster = {
  buildTime: new Date().toISOString(),
  timestamp: timestamp,
  version: require('./package.json').version
};

fs.writeFileSync(
  path.join(__dirname, 'public/cache-buster.json'),
  JSON.stringify(cacheBuster, null, 2)
);

console.log('✅ Cache buster file created: public/cache-buster.json');

// 3. Instructions for ISR invalidation
console.log(`
🔄 Cache Invalidation Complete!

To fully invalidate caches in production:

1. Deploy this build (new file hashes will bust browser cache)
2. For ISR pages, hit these endpoints after deployment:
   - GET /api/revalidate?path=/featured
   - GET /api/revalidate?path=/ad-library
   
3. For CDN cache (if using Vercel/Netlify):
   - Clear deployment cache in your platform dashboard
   
4. For user browsers (if needed):
   - The new ETag headers will force revalidation
   - Or bump the 'version' in package.json

Current cache-buster timestamp: ${timestamp}
`);